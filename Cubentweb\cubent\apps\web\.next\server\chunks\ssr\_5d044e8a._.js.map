{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/dist/esm/app-router/server-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies } from \"next/headers\";\nasync function invalidateCacheAction() {\n  void (await cookies()).delete(`__clerk_invalidate_cache_cookie_${Date.now()}`);\n}\nexport {\n  invalidateCacheAction\n};\n"], "names": [], "mappings": ";;;;;AACA;;;;;AACA,eAAe;IACb,KAAK,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD,GAAG,EAAE,MAAM,CAAC,CAAC,gCAAgC,EAAE,KAAK,GAAG,IAAI;AAC/E;;;;IAEE;;AAAA,8VAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/errorThrower.ts"], "sourcesContent": ["import { buildErrorThrower } from '@clerk/shared/error';\n\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/nextjs' });\n"], "names": [], "mappings": ";;;;AAAA,SAAS,yBAAyB;;;AAE3B,MAAM,6RAAe,oBAAA,EAAkB;IAAE,aAAa;AAAgB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/headers-utils.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\nimport type { NextRequest } from 'next/server';\n\nimport type { RequestLike } from './types';\n\nexport function getCustomAttributeFromRequest(req: RequestLike, key: string): string | null | undefined {\n  // @ts-expect-error - TS doesn't like indexing into RequestLike\n  return key in req ? req[key] : undefined;\n}\n\nexport function getAuthKeyFromRequest(\n  req: RequestLike,\n  key: keyof typeof constants.Attributes,\n): string | null | undefined {\n  return getCustomAttributeFromRequest(req, constants.Attributes[key]) || getHeader(req, constants.Headers[key]);\n}\n\nexport function getHeader(req: RequestLike, name: string): string | null | undefined {\n  if (isNextRequest(req) || isRequestWebAPI(req)) {\n    return req.headers.get(name);\n  }\n\n  // If no header has been determined for IncomingMessage case, check if available within private `socket` headers\n  // When deployed to vercel, req.headers for API routes is a `IncomingHttpHeaders` key-val object which does not follow\n  // the Headers spec so the name is no longer case-insensitive.\n  return req.headers[name] || req.headers[name.toLowerCase()] || (req.socket as any)?._httpMessage?.getHeader(name);\n}\n\nexport function detectClerkMiddleware(req: RequestLike): boolean {\n  return Boolean(getAuthKeyFromRequest(req, 'AuthStatus'));\n}\n\nexport function isNextRequest(val: unknown): val is NextRequest {\n  try {\n    const { headers, nextUrl, cookies } = (val || {}) as NextRequest;\n    return (\n      typeof headers?.get === 'function' &&\n      typeof nextUrl?.searchParams.get === 'function' &&\n      typeof cookies?.get === 'function'\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport function isRequestWebAPI(val: unknown): val is Request {\n  try {\n    const { headers } = (val || {}) as Request;\n    return typeof headers?.get === 'function';\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS,iBAAiB;;;AAKnB,SAAS,8BAA8B,GAAA,EAAkB,GAAA,EAAwC;IAEtG,OAAO,OAAO,MAAM,GAAA,CAAI,GAAG,CAAA,GAAI,KAAA;AACjC;AAEO,SAAS,sBACd,GAAA,EACA,GAAA,EAC2B;IAC3B,OAAO,8BAA8B,6QAAK,YAAA,CAAU,UAAA,CAAW,GAAG,CAAC,KAAK,UAAU,6QAAK,YAAA,CAAU,OAAA,CAAQ,GAAG,CAAC;AAC/G;AAEO,SAAS,UAAU,GAAA,EAAkB,IAAA,EAAyC;IAjBrF,IAAA,IAAA;IAkBE,IAAI,cAAc,GAAG,KAAK,gBAAgB,GAAG,GAAG;QAC9C,OAAO,IAAI,OAAA,CAAQ,GAAA,CAAI,IAAI;IAC7B;IAKA,OAAO,IAAI,OAAA,CAAQ,IAAI,CAAA,IAAK,IAAI,OAAA,CAAQ,KAAK,WAAA,CAAY,CAAC,CAAA,IAAA,CAAA,CAAM,KAAA,CAAA,KAAA,IAAI,MAAA,KAAJ,OAAA,KAAA,IAAA,GAAoB,YAAA,KAApB,OAAA,KAAA,IAAA,GAAkC,SAAA,CAAU,KAAA;AAC9G;AAEO,SAAS,sBAAsB,GAAA,EAA2B;IAC/D,OAAO,QAAQ,sBAAsB,KAAK,YAAY,CAAC;AACzD;AAEO,SAAS,cAAc,GAAA,EAAkC;IAC9D,IAAI;QACF,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC/C,OACE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ,cACxB,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,YAAA,CAAa,GAAA,MAAQ,cACrC,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IAE5B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,GAAA,EAA8B;IAC5D,IAAI;QACF,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC7B,OAAO,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IACjC,EAAA,OAAQ;QACN,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,kBAAkB,QAAQ,IAAI,oFAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,kRAAiB,2BAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,gBAAe,wRAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,mSAAqB,WAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;AACpF,MAAM,gSAAkB,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;AAE9E,MAAM,iSAAmB,WAAA,EAAS,QAAQ,GAAA,CAAI,kCAAkC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,aAAa;;;AAEpB,MAAM,kOAAW,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,KAAK;AAMjD,MAAM,kCAAkC,mOAAY,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gCAAgC;AAEzC,SAAS,wBAAwB;AACjC,SAAS,uCAAuC;;;;;AAEhD,MAAM,gBACJ,wRAAC,kCAAA,IAAA,+GAAA;8QAED,2BAAA,CAAyB,MACzB,mRAAC,mBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/keyless.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { canUseKeyless } from '../utils/feature-flags';\n\nconst keylessCookiePrefix = `__clerk_keys_`;\n\nasync function hashString(str: string) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(str);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n  const hashArray = Array.from(new Uint8Array(hashBuffer));\n  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n  return hashHex.slice(0, 16); // Take only the first 16 characters\n}\n\nasync function getKeylessCookieName(): Promise<string> {\n  // eslint-disable-next-line turbo/no-undeclared-env-vars\n  const PATH = process.env.PWD;\n\n  // Handle gracefully missing PWD\n  if (!PATH) {\n    return `${keylessCookiePrefix}${0}`;\n  }\n\n  const lastThreeDirs = PATH.split('/').filter(Boolean).slice(-3).reverse().join('/');\n\n  // Hash the resulting string\n  const hash = await hashString(lastThreeDirs);\n\n  return `${keylessCookiePrefix}${hash}`;\n}\n\nasync function getKeylessCookieValue(\n  getter: (cookieName: string) => string | undefined,\n): Promise<AccountlessApplication | undefined> {\n  if (!canUseKeyless) {\n    return undefined;\n  }\n\n  const keylessCookieName = await getKeylessCookieName();\n  let keyless;\n\n  try {\n    if (keylessCookieName) {\n      keyless = JSON.parse(getter(keylessCookieName) || '{}');\n    }\n  } catch {\n    keyless = undefined;\n  }\n\n  return keyless;\n}\n\nexport { getKeylessCookieValue, getKeylessCookieName };\n"], "names": [], "mappings": ";;;;AAEA,SAAS,qBAAqB;;;AAE9B,MAAM,sBAAsB,CAAA,aAAA,CAAA;AAE5B,eAAe,WAAW,GAAA,EAAa;IACrC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,OAAO,QAAQ,MAAA,CAAO,GAAG;IAC/B,MAAM,aAAa,MAAM,OAAO,MAAA,CAAO,MAAA,CAAO,WAAW,IAAI;IAC7D,MAAM,YAAY,MAAM,IAAA,CAAK,IAAI,WAAW,UAAU,CAAC;IACvD,MAAM,UAAU,UAAU,GAAA,CAAI,CAAA,IAAK,EAAE,QAAA,CAAS,EAAE,EAAE,QAAA,CAAS,GAAG,GAAG,CAAC,EAAE,IAAA,CAAK,EAAE;IAC3E,OAAO,QAAQ,KAAA,CAAM,GAAG,EAAE;AAC5B;AAEA,eAAe,uBAAwC;IAErD,MAAM,OAAO,QAAQ,GAAA,CAAI,GAAA;IAGzB,IAAI,CAAC,MAAM;QACT,OAAO,GAAG,mBAAmB,GAAG,CAAC,EAAA;IACnC;IAEA,MAAM,gBAAgB,KAAK,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO,EAAE,KAAA,CAAM,CAAA,CAAE,EAAE,OAAA,CAAQ,EAAE,IAAA,CAAK,GAAG;IAGlF,MAAM,OAAO,MAAM,WAAW,aAAa;IAE3C,OAAO,GAAG,mBAAmB,GAAG,IAAI,EAAA;AACtC;AAEA,eAAe,sBACb,MAAA,EAC6C;IAC7C,IAAI,yRAAC,gBAAA,EAAe;QAClB,OAAO,KAAA;IACT;IAEA,MAAM,oBAAoB,MAAM,qBAAqB;IACrD,IAAI;IAEJ,IAAI;QACF,IAAI,oCAAmB;YACrB,UAAU,KAAK,KAAA,CAAM,OAAO,iBAAiB,KAAK,IAAI;QACxD;IACF,EAAA,OAAQ;QACN,UAAU,KAAA;IACZ;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AACA,MAAM,sBAAsB;IAC1B,QAAQ;IACR,UAAU;IACV,UAAU;AACZ;AACA,eAAe,wBAAwB,IAAI;IACzC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;IAC3D,MAAM,cAAc,MAAM,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD;IAChC,MAAM,UAAU,IAAI,QAAQ,2BAA2B;QAAE,SAAS,MAAM,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD;IAAI;IAClF,MAAM,UAAU,MAAM,CAAA,GAAA,+QAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC;QAC3C,IAAI;QACJ,OAAO,CAAC,KAAK,YAAY,GAAG,CAAC,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK;IACjE;IACA,MAAM,WAAW,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,cAAc,MAAM;IACzE,MAAM,WAAW,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,SAAS,MAAM;IACpE,IAAI,YAAY,UAAU;QACxB;IACF;IACA,YAAY,GAAG,CACb,MAAM,CAAA,GAAA,+QAAA,CAAA,uBAAoB,AAAD,KACzB,KAAK,SAAS,CAAC;QAAE;QAAU;QAAgB;IAAU,IACrD;IAEF,IAAI,CAAA,GAAA,wRAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;QAClC,CAAA,GAAA,oSAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,8BAA8B,EAAE,WAAW,EAAE,oSAAA,CAAA,eAAY,CAAC,OAAO;QAC3E;IACF;IACA;AACF;AACA,eAAe;IACb,IAAI,CAAC,uRAAA,CAAA,gBAAa,EAAE;QAClB,OAAO;IACT;IACA,MAAM,SAAS,MAAM,8OAAoC,IAAI,CAAC,CAAC,IAAM,EAAE,mBAAmB,IAAI,KAAK,CAAC,IAAM;IAC1G,IAAI,CAAC,QAAQ;QACX,oRAAA,CAAA,eAAY,CAAC,+BAA+B;QAC5C,OAAO;IACT;IACA,MAAM,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,GAAG;IAC5D,yBAAyB,OAAO,KAAK,IAAI,sBAAsB,GAAG,CAAC;QACjE,UAAU,OAAO,cAAc;QAC/B,KAAK,yBAAyB;IAChC;IACA,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;IAC5D,KAAK,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CACxB,MAAM,CAAA,GAAA,+QAAA,CAAA,uBAAoB,AAAD,KACzB,KAAK,SAAS,CAAC;QAAE;QAAU;QAAgB;IAAU,IACrD;IAEF,OAAO;QACL;QACA;QACA;IACF;AACF;AACA,eAAe;IACb,IAAI,CAAC,uRAAA,CAAA,gBAAa,EAAE;QAClB;IACF;IACA,MAAM,8OAAoC,IAAI,CAAC,CAAC,IAAM,EAAE,aAAa,IAAI,KAAK,CAAC,KAC/E;IACA;AACF;;;;IAEE;IACA;IACA;;AAFA,8VAAA;AACA,8VAAA;AACA,8VAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/.next-internal/server/app/%5Blocale%5D/legal/%5Bslug%5D/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {$$RSC_SERVER_ACTION_2 as '0096e53795e6a564b18f93bb8fcafed8c92ce69fd0'} from 'ACTIONS_MODULE0'\nexport {$$RSC_SERVER_ACTION_0 as '6097e1140601cb7e26bf714e6feb992c08cd55994e'} from 'ACTIONS_MODULE0'\nexport {$$RSC_SERVER_ACTION_3 as '60abc6f9e6e8750102e6c6b14f96f2958fc7055d29'} from 'ACTIONS_MODULE0'\nexport {$$RSC_SERVER_ACTION_1 as '60ad23fee6b5eb1517ffe82db266ee5173f6c96370'} from 'ACTIONS_MODULE0'\nexport {$$RSC_SERVER_ACTION_0 as '40e41d29e77b3a7ab132de11415d6e33ba9fc9409b'} from 'ACTIONS_MODULE1'\nexport {invalidateCacheAction as '7ff83766f16d7a517e525f5e3addcd3b4216951139'} from 'ACTIONS_MODULE2'\nexport {createOrReadKeylessAction as '7f96a2fd407ce9e86c335c8a960562dbe5505817d3'} from 'ACTIONS_MODULE3'\nexport {$$RSC_SERVER_ACTION_0 as '402b8e03b1f0b8a7712e317018747fcc5f91f9c504'} from 'ACTIONS_MODULE4'\n"], "names": [], "mappings": ";AAAA;AAIA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/error.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n"], "names": ["packageName", "customMessages"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,oBAAoB,CAAA,EAAiB;IACnD,MAAM,SAAS,GAAG;IAClB,MAAM,OAAO,GAAG,QAAA,CAAS,CAAC,CAAA,EAAG;IAC7B,OAAO,SAAS,4BAA4B,WAAW;AACzD;AAEO,SAAS,eAAe,CAAA,EAAmC;IAChE,OAAO;QAAC;QAAmB;QAAuB,uBAAuB;KAAA,CAAE,QAAA,CAAS,EAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;AACtG;AAEO,SAAS,WAAW,CAAA,EAAiB;IAC1C,MAAM,SAAS,GAAG;IAClB,OAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAEO,SAAS,eAAe,CAAA,EAAiB;IAE9C,MAAM,UAAA,CAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,EAAA,IAAM,EAAA,EAAI,WAAA,CAAY,EAAE,OAAA,CAAQ,QAAQ,EAAE;IAChF,OAAO,QAAQ,QAAA,CAAS,cAAc;AACxC;AAiBO,SAAS,aAAa,KAAA,EAAgF;IAC3G,OAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,GAAA,EAAwC;IAC9E,OAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,GAAA,EAAoC;IACtE,OAAO,uBAAuB;AAChC;AAEO,SAAS,+BAA+B,GAAA,EAAU;IACvD,OAAO,oBAAoB,GAAG,KAAK,IAAI,IAAA,KAAS;AAClD;AAEO,SAAS,gBAAgB,GAAA,EAAgC;IAC9D,OAAO,UAAU,OAAO;QAAC;QAAM;QAAO,KAAK;KAAA,CAAE,QAAA,CAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAEO,SAAS,kBAAkB,GAAA,EAAU;IAC1C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,qBAAqB,GAAA,EAAU;IAC7C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,YAAY,OAA4B,CAAC,CAAA,EAAoB;IAC3E,OAAO,KAAK,MAAA,GAAS,IAAI,KAAK,GAAA,CAAI,UAAU,IAAI,CAAC,CAAA;AACnD;AAEO,SAAS,WAAW,KAAA,EAAyC;IAClE,OAAO;QACL,MAAM,MAAM,IAAA;QACZ,SAAS,MAAM,OAAA;QACf,aAAa,MAAM,YAAA;QACnB,MAAM;YACJ,WAAW,OAAO,MAAM;YACxB,WAAW,OAAO,MAAM;YACxB,gBAAgB,OAAO,MAAM;YAC7B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,SAAS,YAAY,KAAA,EAAgD;IAC1E,OAAO;QACL,MAAM,OAAO,QAAQ;QACrB,SAAS,OAAO,WAAW;QAC3B,cAAc,OAAO;QACrB,MAAM;YACJ,YAAY,OAAO,MAAM;YACzB,YAAY,OAAO,MAAM;YACzB,iBAAiB,OAAO,MAAM;YAC9B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAM;IAU/C,YAAY,OAAA,EAAiB,EAAE,IAAA,EAAM,MAAA,EAAQ,YAAA,EAAc,UAAA,CAAW,CAAA,CAA4B;QAChG,KAAA,CAAM,OAAO;QAYf,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,IAAI,UAAU,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,CAAA;OAAA,EAAY,IAAA,CAAK,MAAM,CAAA;mBAAA,EAAwB,IAAA,CAAK,MAAA,CAAO,GAAA,CAC9G,CAAA,IAAK,KAAK,SAAA,CAAU,CAAC,IACtB;YAED,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,WAAW,CAAA;gBAAA,EAAqB,IAAA,CAAK,YAAY,EAAA;YACnD;YAEA,OAAO;QACT;QApBE,OAAO,cAAA,CAAe,IAAA,EAAM,uBAAsB,SAAS;QAE3D,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS,YAAY,IAAI;IAChC;AAaF;AASO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;IAiB3C,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqB;QACvD,MAAM,SAAS;QACf,MAAM,QAAQ,IAAI,OAAO,OAAO,OAAA,CAAQ,KAAK,MAAM,GAAG,GAAG;QACzD,MAAM,YAAY,QAAQ,OAAA,CAAQ,OAAO,EAAE;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAA,CAAA,EAAI,UAAU,IAAA,CAAK,CAAC,CAAA;;OAAA,EAAc,IAAI,CAAA;AAAA,CAAA;QAChE,KAAA,CAAM,QAAQ;QAehB;;;;KAAA,GAAA,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,OAAO,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,EAAA;QAChD;QAfE,OAAO,cAAA,CAAe,IAAA,EAAM,mBAAkB,SAAS;QAEvD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,IAAA,GAAO;IACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;IAGxC,YAAY,IAAA,CAAc;QACxB,KAAA,CAAM,IAAI;QACV,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,cAAA,CAAe,IAAA,EAAM,gBAAe,SAAS;IACtD;AACF;AAEO,SAAS,iBAAiB,GAAA,EAAmC;IAClE,OAAO,IAAI,IAAA,KAAS;AACtB;AAOO,IAAM,qBAAqB;IAChC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;IACtC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,MAAA,CAAO;IACpC,6BAA6B,CAAA,gJAAA,CAAA;IAC7B,mCAAmC,CAAA,uJAAA,CAAA;IACnC,mCAAmC,CAAA,sGAAA,CAAA;IACnC,8BAA8B,CAAA,iGAAA,CAAA;IAC9B,sBAAsB,CAAA,gIAAA,CAAA;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,WAAA,EAAa,cAAA,CAAe,CAAA,EAAsC;IACpG,IAAI,MAAM;IAEV,MAAM,WAAW;QACf,GAAG,eAAA;QACH,GAAG,cAAA;IACL;IAEA,SAAS,aAAa,UAAA,EAAoB,YAAA,EAAgD;QACxF,IAAI,CAAC,cAAc;YACjB,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,UAAU,EAAA;QAC9B;QAEA,IAAI,MAAM;QACV,MAAM,UAAU,WAAW,QAAA,CAAS,uBAAuB;QAE3D,KAAA,MAAW,SAAS,QAAS;YAC3B,MAAM,cAAA,CAAe,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC,CAAA,IAAK,EAAA,EAAI,QAAA,CAAS;YAC5D,MAAM,IAAI,OAAA,CAAQ,CAAA,EAAA,EAAK,KAAA,CAAM,CAAC,CAAC,CAAA,EAAA,CAAA,EAAM,WAAW;QAClD;QAEA,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,GAAG,EAAA;IACvB;IAEA,OAAO;QACL,gBAAe,EAAE,aAAAA,YAAAA,CAAY,CAAA,EAAsC;YACjE,IAAI,OAAOA,iBAAgB,UAAU;gBACnC,MAAMA;YACR;YACA,OAAO,IAAA;QACT;QAEA,aAAY,EAAE,gBAAAC,eAAAA,CAAe,CAAA,EAAsC;YACjE,OAAO,MAAA,CAAO,UAAUA,mBAAkB,CAAC,CAAC;YAC5C,OAAO,IAAA;QACT;QAEA,iCAAgC,MAAA,EAAiC;YAC/D,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAA,EAAmC,MAAM,CAAC;QAClF;QAEA,sBAAqB,MAAA,EAAiC;YACpD,MAAM,IAAI,MAAM,aAAa,SAAS,2BAAA,EAA6B,MAAM,CAAC;QAC5E;QAEA,kCAAyC;YACvC,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;QAC1E;QAEA,6BAAoC;YAClC,MAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;QACrE;QAEA,gCAA+B,MAAA,EAAoC;YACjE,MAAM,IAAI,MAAM,aAAa,SAAS,oBAAA,EAAsB,MAAM,CAAC;QACrE;QAEA,OAAM,OAAA,EAAwB;YAC5B,MAAM,IAAI,MAAM,aAAa,OAAO,CAAC;QACvC;IACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;IAMxD,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqC;QACvE,KAAA,CAAM,SAAS;YAAE;QAAK,CAAC;QACvB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/instance.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,UAAU,WAAA,EAA8B;IACtD,OACE,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,iBAAiB,KACtC,YAAY,QAAA,CAAS,oBAAoB;AAE7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/constants.ts"], "sourcesContent": ["export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,IAAM,+BAA+B;IAAC;IAAY;IAAiB,eAAe;CAAA;AAClF,IAAM,gCAAgC;IAAC;IAAiB;IAAsB,wBAAwB;CAAA;AACtG,IAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AACO,IAAM,qBAAqB;IAAC;IAAY;IAAgB;IAAiB,wBAAwB;CAAA;AACjG,IAAM,uBAAuB;IAAC,oBAAoB;CAAA;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,EAAA,EAAY,SAAyB,KAAA,EAAe;IAC/E,OAAO,CAAA,6BAAA,EAAgC,EAAE,CAAA,CAAA,EAAI,MAAM,EAAA;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/url.ts"], "sourcesContent": ["import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n"], "names": ["url"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,kBAAkB,cAAc,EAAA,EAAqB;IACnE,IAAI,YAAY,UAAA,CAAW,GAAG,GAAG;QAC/B,cAAc,YAAY,KAAA,CAAM,CAAC;IACnC;IACA,OAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,EAAA,EAAY;IAC5C,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,GAAA,EAAyB;IACtD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI;IACJ,IAAI,IAAI,KAAA,CAAM,iBAAiB,GAAG;QAChC,QAAQ;IACV,OAAA,IAAW,IAAI,KAAA,CAAM,kBAAkB,GAAG;QACxC,OAAO;IACT,OAAO;QACL,QAAQ;IACV;IAEA,MAAM,WAAW,IAAI,OAAA,CAAQ,OAAO,EAAE;IACtC,OAAO,CAAA,MAAA,EAAS,QAAQ,EAAA;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;IACpF,IAAI,CAAC,WAAW,0RAAA,EAAU,WAAW,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,OAAO,QAAQ,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,cAAA,CAAe,CAAA,KAAmC;IACpG,MAAM,sBAAsB,YAAY,OAAA,CAAQ,iBAAiB,EAAE;IACnE,MAAM,QAAQ,4BAA4B,aAAa,cAAc;IACrE,OAAO,CAAA,QAAA,EAAW,mBAAmB,CAAA,qBAAA,EAAwB,kBAAkB,KAAK,CAAA,sBAAA,CAAA;AACtF;AAMO,SAAS,+BAA+B,IAAA,EAAuB;IACpE,iRAAO,+BAAA,CAA6B,IAAA,CAAK,CAAA,oBAAmB;QAC1D,OAAO,KAAK,UAAA,CAAW,WAAW,KAAK,KAAK,QAAA,CAAS,eAAe;IACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,IAAA,EAAuB;IACrE,iRAAO,gCAAA,CAA8B,IAAA,CAAK,CAAA,qBAAoB;QAC5D,OAAO,KAAK,QAAA,CAAS,gBAAgB,KAAK,CAAC,KAAK,QAAA,CAAS,WAAW,gBAAgB;IACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,EAAA,EAAI,uBAAA,EAA4C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG;IAC3B;IACA,OAAO,kBAAkB,IAAA,CAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG,IAAI,QAAQ,QAAQ;IAC/C;IACA,IAAI,iBAAiB,OAAO,IAAI,GAAG;QACjC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;QACpC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;IACF;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAO,KAAK,MAAA,CAAO,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IAC1F,IAAI,CAAC,yBAAyB;QAC5B,OAAA,CAAQ,iBAAiB,KAAK,IAAI,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI,KAAA,KAAU;IACnE;IACA,IAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;QAClC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;IACtC;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAA,CAAQ,GAAG,KAAA,CAAM,GAAG,CAAA,CAAE,KAAK,GAAA,IAAA,CAAQ,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,EAAA,EAAa;IACnD,OAAO,MAAM,UAAA,CAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,EAAA,EAAY;IACtD,OAAA,CAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAA,CAAM,CAAC,IAAI,KAAA,KAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,EAAA,EAAY;IACnD,OAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,EAAA,EAAY;IACrD,OAAO,MACJ,KAAA,CAAM,KAAK,EACX,GAAA,CAAI,CAAA,UAAW,QAAQ,OAAA,CAAQ,WAAW,GAAG,CAAC,EAC9C,IAAA,CAAK,KAAK;AACf;AAEO,SAAS,cAAc,GAAA,EAAa;IACzC,OAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,IAAA,EAAA,GAAiB,KAAA,EAAyB;IAChE,IAAI,MAAM,QAAQ;IAElB,KAAA,MAAW,WAAW,MAAM,MAAA,CAAO,CAAAA,OAAO,cAAcA,IAAG,CAAC,EAAG;QAC7D,IAAI,KAAK;YAEP,MAAM,WAAW,QAAQ,OAAA,CAAQ,uBAAuB,EAAE;YAC1D,MAAM,kBAAkB,GAAG,IAAI;QACjC,OAAO;YACL,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,MAAgB,mBAAmB,IAAA,CAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/retry.ts"], "sourcesContent": ["type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;AAyCA,IAAM,iBAAyC;IAC7C,cAAc;IACd,wBAAwB;IACxB,QAAQ;IACR,aAAa,CAAC,GAAY,YAAsB,YAAY;IAC5D,kBAAkB;IAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,KAAqB,IAAI,QAAQ,CAAA,IAAK,WAAW,GAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;IAC5D,OAAO,SAAS,QAAA,CAAS,IAAI,KAAK,MAAA,CAAO,CAAA,IAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;IACH,IAAI,cAAc;IAElB,MAAM,qBAAqB,MAAM;QAC/B,MAAM,WAAW,KAAK,YAAA;QACtB,MAAM,OAAO,KAAK,MAAA;QAClB,IAAI,QAAQ,WAAW,KAAK,GAAA,CAAI,MAAM,WAAW;QACjD,QAAQ,YAAY,OAAO,KAAK,MAAM;QACtC,OAAO,KAAK,GAAA,CAAI,KAAK,sBAAA,IAA0B,OAAO,KAAK;IAC7D;IAEA,OAAO,YAA2B;QAChC,MAAM,MAAM,mBAAmB,CAAC;QAChC;IACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,CAAA,KAAkB;IACxG,IAAI,aAAa;IACjB,MAAM,EAAE,WAAA,EAAa,YAAA,EAAc,sBAAA,EAAwB,MAAA,EAAQ,gBAAA,EAAkB,MAAA,CAAO,CAAA,GAAI;QAC9F,GAAG,cAAA;QACH,GAAG,OAAA;IACL;IAEA,MAAM,QAAQ,8BAA8B;QAC1C;QACA;QACA;QACA;IACF,CAAC;IAED,MAAO,KAAM;QACX,IAAI;YACF,OAAO,MAAM,SAAS;QACxB,EAAA,OAAS,GAAG;YACV;YACA,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG;gBAC/B,MAAM;YACR;YACA,IAAI,oBAAoB,eAAe,GAAG;gBACxC,MAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;YAC1D,OAAO;gBACL,MAAM,MAAM;YACd;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicAtob.ts"], "sourcesContent": ["/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAIO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,MAAM,QAAQ,EAAE,QAAA,CAAS;IACpD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicBtoa.ts"], "sourcesContent": ["export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,IAAI,EAAE,QAAA,CAAS,QAAQ;IAClD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/keys.ts"], "sourcesContent": ["import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAaA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAGpC,IAAM,qCAAqC;AAEpC,SAAS,oBAAoB,WAAA,EAA6B;IAC/D,MAAM,WACJ,mCAAmC,IAAA,CAAK,WAAW,KAClD,YAAY,UAAA,CAAW,QAAQ,+QAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC;IACrG,MAAM,YAAY,WAAW,8BAA8B;IAC3D,OAAO,GAAG,SAAS,iRAAG,iBAAA,EAAe,GAAG,WAAW,CAAA,CAAA,CAAG,CAAC,EAAA;AACzD;AAUO,SAAS,oBACd,GAAA,EACA,UAA0F,CAAC,CAAA,EACpE;IACvB,MAAM,OAAO;IAEb,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;QAClC,IAAI,QAAQ,KAAA,IAAS,CAAC,KAAK;YACzB,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,QAAQ,KAAA,IAAS,CAAC,iBAAiB,GAAG,GAAG;YAC3C,MAAM,IAAI,MAAM,4BAA4B;QAC9C;QACA,OAAO;IACT;IAEA,MAAM,eAAe,IAAI,UAAA,CAAW,2BAA2B,IAAI,eAAe;IAElF,IAAI,4RAAc,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IAGlD,cAAc,YAAY,KAAA,CAAM,GAAG,CAAA,CAAE;IAErC,IAAI,QAAQ,QAAA,EAAU;QACpB,cAAc,QAAQ,QAAA;IACxB,OAAA,IAAW,iBAAiB,iBAAiB,QAAQ,MAAA,IAAU,QAAQ,WAAA,EAAa;QAClF,cAAc,CAAA,MAAA,EAAS,QAAQ,MAAM,EAAA;IACvC;IAEA,OAAO;QACL;QACA;IACF;AACF;AAQO,SAAS,iBAAiB,MAAc,EAAA,EAAI;IACjD,IAAI;QACF,MAAM,iBAAiB,IAAI,UAAA,CAAW,2BAA2B,KAAK,IAAI,UAAA,CAAW,2BAA2B;QAEhH,MAAM,2SAA6B,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAE,EAAE,QAAA,CAAS,GAAG;QAEvF,OAAO,kBAAkB;IAC3B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,6BAA6B;IAC3C,MAAM,uBAAuB,aAAA,GAAA,IAAI,IAAqB;IAEtD,OAAO;QACL,mBAAmB,CAAC,QAA+B;YACjD,IAAI,CAAC,KAAK;gBACR,OAAO;YACT;YAEA,MAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI,QAAA;YACrD,IAAI,MAAM,qBAAqB,GAAA,CAAI,QAAQ;YAC3C,IAAI,QAAQ,KAAA,GAAW;gBACrB,gRAAM,0BAAA,CAAwB,IAAA,CAAK,CAAA,IAAK,SAAS,QAAA,CAAS,CAAC,CAAC;gBAC5D,qBAAqB,GAAA,CAAI,UAAU,GAAG;YACxC;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,gCAAgC,MAAA,EAAyB;IACvE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,+BAA+B,MAAA,EAAyB;IACtE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,2BAA2B,MAAA,EAAyB;IAClE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,0BAA0B,MAAA,EAAyB;IACjE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEA,eAAsB,gBACpB,cAAA,EACA,SAAuB,WAAW,MAAA,CAAO,MAAA,EACxB;IACjB,MAAM,OAAO,IAAI,YAAY,EAAE,MAAA,CAAO,cAAc;IACpD,MAAM,SAAS,MAAM,OAAO,MAAA,CAAO,SAAS,IAAI;IAChD,MAAM,eAAe,OAAO,YAAA,CAAa,GAAG,IAAI,WAAW,MAAM,CAAC;IAElE,qRAAO,iBAAA,EAAe,YAAY,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,SAAA,CAAU,GAAG,CAAC;AAC9F;AAEO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;IACzF,OAAO,GAAG,UAAU,CAAA,CAAA,EAAI,YAAY,EAAA;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/runtimeEnvironment.ts"], "sourcesContent": ["export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;AAAO,IAAM,2BAA2B,MAAe;IACrD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAIT,OAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;IAC9C,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;IACpD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/deprecated.ts"], "sourcesContent": ["import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;AAqBA,IAAM,oBAAoB,aAAA,GAAA,IAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;IACjF,MAAM,4RAAc,oBAAA,CAAkB,OAAK,uSAAA,CAAwB;IACnE,MAAM,YAAY,OAAO;IACzB,IAAI,kBAAkB,GAAA,CAAI,SAAS,KAAK,aAAa;QACnD;IACF;IACA,kBAAkB,GAAA,CAAI,SAAS;IAE/B,QAAQ,IAAA,CACN,CAAA,8BAAA,EAAiC,MAAM,CAAA;AAAA,EAAmE,OAAO,EAAA;AAErH;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,KAAA,KAAgB;IAC9G,MAAM,SAAS,WAAW,MAAM,IAAI,SAAA;IAEpC,IAAI,QAAQ,MAAA,CAAO,QAAQ,CAAA;IAC3B,OAAO,cAAA,CAAe,QAAQ,UAAU;QACtC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG,IAAI,IAAI,CAAA,CAAA,EAAI,QAAQ,EAAE;YACvD,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;IACT,IAAI,QAAQ,GAAA,CAAI,QAAQ,CAAA;IACxB,OAAO,cAAA,CAAe,KAAK,UAAU;QACnC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG;YACjC,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization.ts"], "sourcesContent": ["import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => (value.startsWith('org:') ? value : `org:${value}`);\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return orgRole === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n"], "names": ["config"], "mappings": ";;;;;;;AA4CA,IAAM,mBAAkC;IACtC,YAAY;QACV,cAAc;QACd,OAAO;IACT;IACA,QAAQ;QACN,cAAc;QACd,OAAO;IACT;IACA,UAAU;QACR,cAAc;QACd,OAAO;IACT;IACA,KAAK;QACH,cAAc;QACd,OAAO;IACT;AACF;AAEA,IAAM,iBAAiB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAgB;IAAiB,cAAc;CAAC;AAE1G,IAAM,gBAAgB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAc;IAAU;IAAY,KAAK;CAAC;AAGnG,IAAM,gBAAgB,CAAC,SAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,QAAe,eAAe,GAAA,CAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,OAAc,cAAc,GAAA,CAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,QAAmB,MAAM,UAAA,CAAW,MAAM,IAAI,QAAQ,CAAA,IAAA,EAAO,KAAK,EAAA;AAOzF,IAAM,wBAA+C,CAAC,QAAQ,YAAY;IACxE,MAAM,EAAE,KAAA,EAAO,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;IAC3C,IAAI,CAAC,OAAO,IAAA,IAAQ,CAAC,OAAO,UAAA,EAAY;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;QACzC,OAAO;IACT;IAEA,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,eAAe,QAAA,CAAS,cAAc,OAAO,UAAU,CAAC;IACjE;IAEA,IAAI,OAAO,IAAA,EAAM;QACf,OAAO,YAAY,cAAc,OAAO,IAAI;IAC9C;IACA,OAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;IACtE,MAAM,EAAE,KAAK,WAAA,EAAa,MAAM,YAAA,CAAa,CAAA,GAAI,aAAa,KAAK;IACnE,MAAM,CAAC,OAAO,GAAG,CAAA,GAAI,cAAc,KAAA,CAAM,GAAG;IAC5C,MAAM,KAAK,OAAO;IAElB,IAAI,UAAU,OAAO;QACnB,OAAO,YAAY,QAAA,CAAS,EAAE;IAChC,OAAA,IAAW,UAAU,QAAQ;QAC3B,OAAO,aAAa,QAAA,CAAS,EAAE;IACjC,OAAO;QAEL,OAAO,CAAC;eAAG,aAAa;eAAG,YAAY;SAAA,CAAE,QAAA,CAAS,EAAE;IACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;IAChF,MAAM,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;IAE5B,IAAI,OAAO,OAAA,IAAW,UAAU;QAC9B,OAAO,sBAAsB,UAAU,OAAO,OAAO;IACvD;IAEA,IAAI,OAAO,IAAA,IAAQ,OAAO;QACxB,OAAO,sBAAsB,OAAO,OAAO,IAAI;IACjD;IACA,OAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;IACvD,MAAM,WAAW,MAAM,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC,IAAI,CAAC,CAAA;IAG5D,OAAO;QACL,KAAK,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;QACjF,MAAM,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;IACxF,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,wBAAwB,CAACA,YAAiC;QAC9D,IAAI,OAAOA,YAAW,UAAU;YAC9B,OAAO,gBAAA,CAAiBA,OAAM,CAAA;QAChC;QACA,OAAOA;IACT;IAEA,MAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;IACvF,MAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;IAE/F,IAAI,sBAAsB,oBAAoB;QAC5C,OAAO,sBAAsB,IAAA,CAAK,MAAM,MAAM;IAChD;IAEA,OAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,qBAAA,CAAsB,CAAA,KAAM;IAChH,IAAI,CAAC,OAAO,cAAA,IAAkB,CAAC,uBAAuB;QACpD,OAAO;IACT;IAEA,MAAM,wBAAwB,6BAA6B,OAAO,cAAc;IAChF,IAAI,CAAC,uBAAuB;QAC1B,OAAO;IACT;IAEA,MAAM,EAAE,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI,sBAAsB;IACtD,MAAM,CAAC,YAAY,UAAU,CAAA,GAAI;IAIjC,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IACvE,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IAEvE,OAAQ,OAAO;QACb,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB;QAC9C,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB,kBAAkB;IAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;IAC3G,OAAO,CAAC,WAAoB;QAC1B,IAAI,CAAC,QAAQ,MAAA,EAAQ;YACnB,OAAO;QACT;QAEA,MAAM,uBAAuB,0BAA0B,QAAQ,OAAO;QACtE,MAAM,mBAAmB,sBAAsB,QAAQ,OAAO;QAC9D,MAAM,8BAA8B,iCAAiC,QAAQ,OAAO;QAEpF,IAAI;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI,GAAG;YACjG,OAAO;gBAAC,wBAAwB;gBAAkB,2BAA2B;aAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI;QACrG;QAEA,OAAO;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,KAAA,CAAM,CAAA,IAAK,MAAM,IAAI;IACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC,EACxB,YAAY,EACV,SAAA,EACA,aAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,EACA,GAAA,EACA,aAAA,EACF,EACA,SAAS,EAAE,0BAA0B,IAAA,CAAK,CAAA,EAC5C,KAAmD;IACjD,IAAI,cAAc,KAAA,KAAa,WAAW,KAAA,GAAW;QACnD,OAAO;YACL,UAAU;YACV,YAAY,KAAA;YACZ;YACA,eAAe,KAAA;YACf;YACA,OAAO,KAAA;YACP,OAAO,KAAA;YACP,SAAS,KAAA;YACT,SAAS,KAAA;YACT,KAAK,KAAA;YACL;YACA;QACF;IACF;IAEA,IAAI,cAAc,QAAQ,WAAW,MAAM;QACzC,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,2BAA2B,kBAAkB,WAAW;QAC1D,OAAO;YACL,UAAU;YACV,YAAY;YACZ,WAAW;YACX,QAAQ;YACR,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;QACtE,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB;YACA;YACA,SAAS,WAAW;YACpB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;QACxD,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB,OAAO;YACP,SAAS;YACT,SAAS;YACT;YACA;YACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/jwtPayloadParser.ts"], "sourcesContent": ["import type {\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport { splitByScope } from './authorization';\n\nexport const parsePermissions = ({ per, fpm }: { per?: string; fpm?: string }) => {\n  if (!per || !fpm) {\n    return { permissions: [], featurePermissionMap: [] };\n  }\n\n  const permissions = per.split(',').map(p => p.trim());\n\n  // TODO: make this more efficient\n  const featurePermissionMap = fpm\n    .split(',')\n    .map(permission => Number.parseInt(permission.trim(), 10))\n    .map((permission: number) =>\n      permission\n        .toString(2)\n        .padStart(permissions.length, '0')\n        .split('')\n        .map(bit => Number.parseInt(bit, 10))\n        .reverse(),\n    )\n    .filter(Boolean);\n\n  return { permissions, featurePermissionMap };\n};\n\nfunction buildOrgPermissions({\n  features,\n  permissions,\n  featurePermissionMap,\n}: {\n  features?: string[];\n  permissions?: string[];\n  featurePermissionMap?: number[][];\n}) {\n  // Early return if any required input is missing\n  if (!features || !permissions || !featurePermissionMap) {\n    return [];\n  }\n\n  const orgPermissions: string[] = [];\n\n  // Process each feature and its permissions in a single loop\n  for (let featureIndex = 0; featureIndex < features.length; featureIndex++) {\n    const feature = features[featureIndex];\n\n    if (featureIndex >= featurePermissionMap.length) {\n      continue;\n    }\n\n    const permissionBits = featurePermissionMap[featureIndex];\n    if (!permissionBits) continue;\n\n    for (let permIndex = 0; permIndex < permissionBits.length; permIndex++) {\n      if (permissionBits[permIndex] === 1) {\n        orgPermissions.push(`org:${feature}:${permissions[permIndex]}`);\n      }\n    }\n  }\n\n  return orgPermissions;\n}\n\n/**\n * @experimental\n *\n * Resolves the signed-in auth state from JWT claims.\n */\nconst __experimental_JWTPayloadToAuthObjectProperties = (claims: JwtPayload): SharedSignedInAuthObjectProperties => {\n  let orgId: string | undefined;\n  let orgRole: OrganizationCustomRoleKey | undefined;\n  let orgSlug: string | undefined;\n  let orgPermissions: OrganizationCustomPermissionKey[] | undefined;\n\n  // fva can be undefined for instances that have not opt-in\n  const factorVerificationAge = claims.fva ?? null;\n\n  // sts can be undefined for instances that have not opt-in\n  const sessionStatus = claims.sts ?? null;\n\n  switch (claims.v) {\n    case 2: {\n      if (claims.o) {\n        orgId = claims.o?.id;\n        orgSlug = claims.o?.slg;\n\n        if (claims.o?.rol) {\n          orgRole = `org:${claims.o?.rol}`;\n        }\n        const { org } = splitByScope(claims.fea);\n        const { permissions, featurePermissionMap } = parsePermissions({\n          per: claims.o?.per,\n          fpm: claims.o?.fpm,\n        });\n        orgPermissions = buildOrgPermissions({\n          features: org,\n          featurePermissionMap: featurePermissionMap,\n          permissions: permissions,\n        });\n      }\n      break;\n    }\n    default:\n      orgId = claims.org_id;\n      orgRole = claims.org_role;\n      orgSlug = claims.org_slug;\n      orgPermissions = claims.org_permissions;\n      break;\n  }\n\n  return {\n    sessionClaims: claims,\n    sessionId: claims.sid,\n    sessionStatus,\n    actor: claims.act,\n    userId: claims.sub,\n    orgId: orgId,\n    orgRole: orgRole,\n    orgSlug: orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  };\n};\n\nexport { __experimental_JWTPayloadToAuthObjectProperties };\n"], "names": [], "mappings": ";;;;;;;;;AASO,IAAM,mBAAmB,CAAC,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,KAAsC;IAChF,IAAI,CAAC,OAAO,CAAC,KAAK;QAChB,OAAO;YAAE,aAAa,CAAC,CAAA;YAAG,sBAAsB,CAAC,CAAA;QAAE;IACrD;IAEA,MAAM,cAAc,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC;IAGpD,MAAM,uBAAuB,IAC1B,KAAA,CAAM,GAAG,EACT,GAAA,CAAI,CAAA,aAAc,OAAO,QAAA,CAAS,WAAW,IAAA,CAAK,GAAG,EAAE,CAAC,EACxD,GAAA,CAAI,CAAC,aACJ,WACG,QAAA,CAAS,CAAC,EACV,QAAA,CAAS,YAAY,MAAA,EAAQ,GAAG,EAChC,KAAA,CAAM,EAAE,EACR,GAAA,CAAI,CAAA,MAAO,OAAO,QAAA,CAAS,KAAK,EAAE,CAAC,EACnC,OAAA,CAAQ,GAEZ,MAAA,CAAO,OAAO;IAEjB,OAAO;QAAE;QAAa;IAAqB;AAC7C;AAEA,SAAS,oBAAoB,EAC3B,QAAA,EACA,WAAA,EACA,oBAAA,EACF,EAIG;IAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,sBAAsB;QACtD,OAAO,CAAC,CAAA;IACV;IAEA,MAAM,iBAA2B,CAAC,CAAA;IAGlC,IAAA,IAAS,eAAe,GAAG,eAAe,SAAS,MAAA,EAAQ,eAAgB;QACzE,MAAM,UAAU,QAAA,CAAS,YAAY,CAAA;QAErC,IAAI,gBAAgB,qBAAqB,MAAA,EAAQ;YAC/C;QACF;QAEA,MAAM,iBAAiB,oBAAA,CAAqB,YAAY,CAAA;QACxD,IAAI,CAAC,eAAgB,CAAA;QAErB,IAAA,IAAS,YAAY,GAAG,YAAY,eAAe,MAAA,EAAQ,YAAa;YACtE,IAAI,cAAA,CAAe,SAAS,CAAA,KAAM,GAAG;gBACnC,eAAe,IAAA,CAAK,CAAA,IAAA,EAAO,OAAO,CAAA,CAAA,EAAI,WAAA,CAAY,SAAS,CAAC,EAAE;YAChE;QACF;IACF;IAEA,OAAO;AACT;AAOA,IAAM,kDAAkD,CAAC,WAA2D;IAClH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAGJ,MAAM,wBAAwB,OAAO,GAAA,IAAO;IAG5C,MAAM,gBAAgB,OAAO,GAAA,IAAO;IAEpC,OAAQ,OAAO,CAAA,EAAG;QAChB,KAAK;YAAG;gBACN,IAAI,OAAO,CAAA,EAAG;oBACZ,QAAQ,OAAO,CAAA,EAAG;oBAClB,UAAU,OAAO,CAAA,EAAG;oBAEpB,IAAI,OAAO,CAAA,EAAG,KAAK;wBACjB,UAAU,CAAA,IAAA,EAAO,OAAO,CAAA,EAAG,GAAG,EAAA;oBAChC;oBACA,MAAM,EAAE,GAAA,CAAI,CAAA,iRAAI,eAAA,EAAa,OAAO,GAAG;oBACvC,MAAM,EAAE,WAAA,EAAa,oBAAA,CAAqB,CAAA,GAAI,iBAAiB;wBAC7D,KAAK,OAAO,CAAA,EAAG;wBACf,KAAK,OAAO,CAAA,EAAG;oBACjB,CAAC;oBACD,iBAAiB,oBAAoB;wBACnC,UAAU;wBACV;wBACA;oBACF,CAAC;gBACH;gBACA;YACF;QACA;YACE,QAAQ,OAAO,MAAA;YACf,UAAU,OAAO,QAAA;YACjB,UAAU,OAAO,QAAA;YACjB,iBAAiB,OAAO,eAAA;YACxB;IACJ;IAEA,OAAO;QACL,eAAe;QACf,WAAW,OAAO,GAAA;QAClB;QACA,OAAO,OAAO,GAAA;QACd,QAAQ,OAAO,GAAA;QACf;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/compiled/path-to-regexp/index.js", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/pathToRegexp.ts"], "sourcesContent": ["/* eslint-disable no-redeclare, curly */\n\nfunction _(r) {\n  for (var n = [], e = 0; e < r.length; ) {\n    var a = r[e];\n    if (a === '*' || a === '+' || a === '?') {\n      n.push({\n        type: 'MODIFIER',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '\\\\') {\n      n.push({\n        type: 'ESCAPED_CHAR',\n        index: e++,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '{') {\n      n.push({\n        type: 'OPEN',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '}') {\n      n.push({\n        type: 'CLOSE',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === ':') {\n      for (var u = '', t = e + 1; t < r.length; ) {\n        var c = r.charCodeAt(t);\n        if ((c >= 48 && c <= 57) || (c >= 65 && c <= 90) || (c >= 97 && c <= 122) || c === 95) {\n          u += r[t++];\n          continue;\n        }\n        break;\n      }\n      if (!u) throw new TypeError('Missing parameter name at '.concat(e));\n      n.push({\n        type: 'NAME',\n        index: e,\n        value: u,\n      }),\n        (e = t);\n      continue;\n    }\n    if (a === '(') {\n      var o = 1,\n        m = '',\n        t = e + 1;\n      if (r[t] === '?') throw new TypeError('Pattern cannot start with \"?\" at '.concat(t));\n      for (; t < r.length; ) {\n        if (r[t] === '\\\\') {\n          m += r[t++] + r[t++];\n          continue;\n        }\n        if (r[t] === ')') {\n          if ((o--, o === 0)) {\n            t++;\n            break;\n          }\n        } else if (r[t] === '(' && (o++, r[t + 1] !== '?'))\n          throw new TypeError('Capturing groups are not allowed at '.concat(t));\n        m += r[t++];\n      }\n      if (o) throw new TypeError('Unbalanced pattern at '.concat(e));\n      if (!m) throw new TypeError('Missing pattern at '.concat(e));\n      n.push({\n        type: 'PATTERN',\n        index: e,\n        value: m,\n      }),\n        (e = t);\n      continue;\n    }\n    n.push({\n      type: 'CHAR',\n      index: e,\n      value: r[e++],\n    });\n  }\n  return (\n    n.push({\n      type: 'END',\n      index: e,\n      value: '',\n    }),\n    n\n  );\n}\n\nfunction F(r, n) {\n  n === void 0 && (n = {});\n  for (\n    var e = _(r),\n      a = n.prefixes,\n      u = a === void 0 ? './' : a,\n      t = n.delimiter,\n      c = t === void 0 ? '/#?' : t,\n      o = [],\n      m = 0,\n      h = 0,\n      p = '',\n      f = function (l) {\n        if (h < e.length && e[h].type === l) return e[h++].value;\n      },\n      w = function (l) {\n        var v = f(l);\n        if (v !== void 0) return v;\n        var E = e[h],\n          N = E.type,\n          S = E.index;\n        throw new TypeError('Unexpected '.concat(N, ' at ').concat(S, ', expected ').concat(l));\n      },\n      d = function () {\n        for (var l = '', v; (v = f('CHAR') || f('ESCAPED_CHAR')); ) l += v;\n        return l;\n      },\n      M = function (l) {\n        for (var v = 0, E = c; v < E.length; v++) {\n          var N = E[v];\n          if (l.indexOf(N) > -1) return !0;\n        }\n        return !1;\n      },\n      A = function (l) {\n        var v = o[o.length - 1],\n          E = l || (v && typeof v == 'string' ? v : '');\n        if (v && !E)\n          throw new TypeError('Must have text between two parameters, missing text after \"'.concat(v.name, '\"'));\n        return !E || M(E) ? '[^'.concat(s(c), ']+?') : '(?:(?!'.concat(s(E), ')[^').concat(s(c), '])+?');\n      };\n    h < e.length;\n\n  ) {\n    var T = f('CHAR'),\n      x = f('NAME'),\n      C = f('PATTERN');\n    if (x || C) {\n      var g = T || '';\n      u.indexOf(g) === -1 && ((p += g), (g = '')),\n        p && (o.push(p), (p = '')),\n        o.push({\n          name: x || m++,\n          prefix: g,\n          suffix: '',\n          pattern: C || A(g),\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    var i = T || f('ESCAPED_CHAR');\n    if (i) {\n      p += i;\n      continue;\n    }\n    p && (o.push(p), (p = ''));\n    var R = f('OPEN');\n    if (R) {\n      var g = d(),\n        y = f('NAME') || '',\n        O = f('PATTERN') || '',\n        b = d();\n      w('CLOSE'),\n        o.push({\n          name: y || (O ? m++ : ''),\n          pattern: y && !O ? A(g) : O,\n          prefix: g,\n          suffix: b,\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    w('END');\n  }\n  return o;\n}\n\nfunction H(r, n) {\n  var e = [],\n    a = P(r, e, n);\n  return I(a, e, n);\n}\n\nfunction I(r, n, e) {\n  e === void 0 && (e = {});\n  var a = e.decode,\n    u =\n      a === void 0\n        ? function (t) {\n            return t;\n          }\n        : a;\n  return function (t) {\n    var c = r.exec(t);\n    if (!c) return !1;\n    for (\n      var o = c[0],\n        m = c.index,\n        h = Object.create(null),\n        p = function (w) {\n          if (c[w] === void 0) return 'continue';\n          var d = n[w - 1];\n          d.modifier === '*' || d.modifier === '+'\n            ? (h[d.name] = c[w].split(d.prefix + d.suffix).map(function (M) {\n                return u(M, d);\n              }))\n            : (h[d.name] = u(c[w], d));\n        },\n        f = 1;\n      f < c.length;\n      f++\n    )\n      p(f);\n    return {\n      path: o,\n      index: m,\n      params: h,\n    };\n  };\n}\n\nfunction s(r) {\n  return r.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n}\n\nfunction D(r) {\n  return r && r.sensitive ? '' : 'i';\n}\n\nfunction $(r, n) {\n  if (!n) return r;\n  for (var e = /\\((?:\\?<(.*?)>)?(?!\\?)/g, a = 0, u = e.exec(r.source); u; )\n    n.push({\n      name: u[1] || a++,\n      prefix: '',\n      suffix: '',\n      modifier: '',\n      pattern: '',\n    }),\n      (u = e.exec(r.source));\n  return r;\n}\n\nfunction W(r, n, e) {\n  var a = r.map(function (u) {\n    return P(u, n, e).source;\n  });\n  return new RegExp('(?:'.concat(a.join('|'), ')'), D(e));\n}\n\nfunction L(r, n, e) {\n  return U(F(r, e), n, e);\n}\n\nfunction U(r, n, e) {\n  e === void 0 && (e = {});\n  for (\n    var a = e.strict,\n      u = a === void 0 ? !1 : a,\n      t = e.start,\n      c = t === void 0 ? !0 : t,\n      o = e.end,\n      m = o === void 0 ? !0 : o,\n      h = e.encode,\n      p =\n        h === void 0\n          ? function (v) {\n              return v;\n            }\n          : h,\n      f = e.delimiter,\n      w = f === void 0 ? '/#?' : f,\n      d = e.endsWith,\n      M = d === void 0 ? '' : d,\n      A = '['.concat(s(M), ']|$'),\n      T = '['.concat(s(w), ']'),\n      x = c ? '^' : '',\n      C = 0,\n      g = r;\n    C < g.length;\n    C++\n  ) {\n    var i = g[C];\n    if (typeof i == 'string') x += s(p(i));\n    else {\n      var R = s(p(i.prefix)),\n        y = s(p(i.suffix));\n      if (i.pattern)\n        if ((n && n.push(i), R || y))\n          if (i.modifier === '+' || i.modifier === '*') {\n            var O = i.modifier === '*' ? '?' : '';\n            x += '(?:'\n              .concat(R, '((?:')\n              .concat(i.pattern, ')(?:')\n              .concat(y)\n              .concat(R, '(?:')\n              .concat(i.pattern, '))*)')\n              .concat(y, ')')\n              .concat(O);\n          } else x += '(?:'.concat(R, '(').concat(i.pattern, ')').concat(y, ')').concat(i.modifier);\n        else {\n          if (i.modifier === '+' || i.modifier === '*')\n            throw new TypeError('Can not repeat \"'.concat(i.name, '\" without a prefix and suffix'));\n          x += '('.concat(i.pattern, ')').concat(i.modifier);\n        }\n      else x += '(?:'.concat(R).concat(y, ')').concat(i.modifier);\n    }\n  }\n  if (m) u || (x += ''.concat(T, '?')), (x += e.endsWith ? '(?='.concat(A, ')') : '$');\n  else {\n    var b = r[r.length - 1],\n      l = typeof b == 'string' ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;\n    u || (x += '(?:'.concat(T, '(?=').concat(A, '))?')), l || (x += '(?='.concat(T, '|').concat(A, ')'));\n  }\n  return new RegExp(x, D(e));\n}\n\nfunction P(r, n, e) {\n  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);\n}\nexport { H as match, P as pathToRegexp };\n", "import type {\n  Match,\n  MatchFunction,\n  ParseOptions,\n  Path,\n  RegexpToFunctionOptions,\n  TokensToRegexpOptions,\n} from './compiled/path-to-regexp';\nimport { match as matchBase, pathToRegexp as pathToRegexpBase } from './compiled/path-to-regexp';\n\nexport const pathToRegexp = (path: string) => {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return pathToRegexpBase(path);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path: ${path}.\\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n};\n\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n): MatchFunction<P> {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return matchBase(str, options);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n}\n\nexport { type Match, type MatchFunction };\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,IAAA,IAAS,IAAI,CAAC,CAAA,EAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;QACtC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;YACvC,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,MAAM;YACd,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAA,IAAS,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;gBAC1C,IAAI,IAAI,EAAE,UAAA,CAAW,CAAC;gBACtB,IAAK,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,OAAQ,MAAM,IAAI;oBACrF,KAAK,CAAA,CAAE,GAAG,CAAA;oBACV;gBACF;gBACA;YACF;YACA,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,6BAA6B,MAAA,CAAO,CAAC,CAAC;YAClE,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAI,IAAI,GACN,IAAI,IACJ,IAAI,IAAI;YACV,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,IAAK,CAAA,MAAM,IAAI,UAAU,oCAAoC,MAAA,CAAO,CAAC,CAAC;YACnF,MAAO,IAAI,EAAE,MAAA,EAAU;gBACrB,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,MAAM;oBACjB,KAAK,CAAA,CAAE,GAAG,CAAA,GAAI,CAAA,CAAE,GAAG,CAAA;oBACnB;gBACF;gBACA,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAK;oBAChB,IAAK,KAAK,MAAM,GAAI;wBAClB;wBACA;oBACF;gBACF,OAAA,IAAW,CAAA,CAAE,CAAC,CAAA,KAAM,OAAA,CAAQ,KAAK,CAAA,CAAE,IAAI,CAAC,CAAA,KAAM,GAAA,GAC5C,MAAM,IAAI,UAAU,uCAAuC,MAAA,CAAO,CAAC,CAAC;gBACtE,KAAK,CAAA,CAAE,GAAG,CAAA;YACZ;YACA,IAAI,EAAG,CAAA,MAAM,IAAI,UAAU,yBAAyB,MAAA,CAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,sBAAsB,MAAA,CAAO,CAAC,CAAC;YAC3D,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,EAAE,IAAA,CAAK;YACL,MAAM;YACN,OAAO;YACP,OAAO,CAAA,CAAE,GAAG,CAAA;QACd,CAAC;IACH;IACA,OACE,EAAE,IAAA,CAAK;QACL,MAAM;QACN,OAAO;QACP,OAAO;IACT,CAAC,GACD;AAEJ;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,CAAC,GACT,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAO,GAC1B,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,CAAC,CAAA,EACL,IAAI,GACJ,IAAI,GACJ,IAAI,IACJ,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,MAAA,IAAU,CAAA,CAAE,CAAC,CAAA,CAAE,IAAA,KAAS,EAAG,CAAA,OAAO,CAAA,CAAE,GAAG,CAAA,CAAE,KAAA;IACrD,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,CAAC;QACX,IAAI,MAAM,KAAA,EAAQ,CAAA,OAAO;QACzB,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,IAAA,EACN,IAAI,EAAE,KAAA;QACR,MAAM,IAAI,UAAU,cAAc,MAAA,CAAO,GAAG,MAAM,EAAE,MAAA,CAAO,GAAG,aAAa,EAAE,MAAA,CAAO,CAAC,CAAC;IACxF,GACA,IAAI,WAAY;QACd,IAAA,IAAS,IAAI,IAAI,GAAI,IAAI,EAAE,MAAM,KAAK,EAAE,cAAc,GAAM,CAAA,IAAK;QACjE,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,IAAK;YACxC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;YACX,IAAI,EAAE,OAAA,CAAQ,CAAC,IAAI,CAAA,EAAI,CAAA,OAAO;QAChC;QACA,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,KAAA,CAAM,KAAK,OAAO,KAAK,WAAW,IAAI,EAAA;QAC5C,IAAI,KAAK,CAAC,GACR,MAAM,IAAI,UAAU,8DAA8D,MAAA,CAAO,EAAE,IAAA,EAAM,GAAG,CAAC;QACvG,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,IAAI,SAAS,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,EAAE,MAAA,CAAO,EAAE,CAAC,GAAG,MAAM;IACjG,GACF,IAAI,EAAE,MAAA,EAEN;QACA,IAAI,IAAI,EAAE,MAAM,GACd,IAAI,EAAE,MAAM,GACZ,IAAI,EAAE,SAAS;QACjB,IAAI,KAAK,GAAG;YACV,IAAI,IAAI,KAAK;YACb,EAAE,OAAA,CAAQ,CAAC,MAAM,CAAA,KAAA,CAAQ,KAAK,GAAK,IAAI,EAAA,GACrC,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA,GACtB,EAAE,IAAA,CAAK;gBACL,MAAM,KAAK;gBACX,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,EAAE,CAAC;gBACjB,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,IAAI,IAAI,KAAK,EAAE,cAAc;QAC7B,IAAI,GAAG;YACL,KAAK;YACL;QACF;QACA,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA;QACtB,IAAI,IAAI,EAAE,MAAM;QAChB,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,GACR,IAAI,EAAE,MAAM,KAAK,IACjB,IAAI,EAAE,SAAS,KAAK,IACpB,IAAI,EAAE;YACR,EAAE,OAAO,GACP,EAAE,IAAA,CAAK;gBACL,MAAM,KAAA,CAAM,IAAI,MAAM,EAAA;gBACtB,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI;gBAC1B,QAAQ;gBACR,QAAQ;gBACR,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,EAAE,KAAK;IACT;IACA,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,IAAI,CAAC,CAAA,EACP,IAAI,EAAE,GAAG,GAAG,CAAC;IACf,OAAO,EAAE,GAAG,GAAG,CAAC;AAClB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAI,IAAI,EAAE,MAAA,EACR,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA;IACR,OAAO,SAAU,CAAA,EAAG;QAClB,IAAI,IAAI,EAAE,IAAA,CAAK,CAAC;QAChB,IAAI,CAAC,EAAG,CAAA,OAAO;QACf,IAAA,IACM,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,KAAA,EACN,IAAI,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI,GACtB,IAAI,SAAU,CAAA,EAAG;YACf,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAA,EAAQ,CAAA,OAAO;YAC5B,IAAI,IAAI,CAAA,CAAE,IAAI,CAAC,CAAA;YACf,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,MAChC,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,EAAE,MAAA,GAAS,EAAE,MAAM,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;gBAC5D,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,IACA,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,EAAE,CAAA,CAAE,CAAC,CAAA,EAAG,CAAC;QAC5B,GACA,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IAEA,EAAE,CAAC;QACL,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,EAAE,OAAA,CAAQ,6BAA6B,MAAM;AACtD;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,KAAK,EAAE,SAAA,GAAY,KAAK;AACjC;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,CAAC,EAAG,CAAA,OAAO;IACf,IAAA,IAAS,IAAI,2BAA2B,IAAI,GAAG,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM,GAAG,GACnE,EAAE,IAAA,CAAK;QACL,MAAM,CAAA,CAAE,CAAC,CAAA,IAAK;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;IACX,CAAC,GACE,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM;IACxB,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,IAAI,IAAI,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;QACzB,OAAO,EAAE,GAAG,GAAG,CAAC,EAAE,MAAA;IACpB,CAAC;IACD,OAAO,IAAI,OAAO,MAAM,MAAA,CAAO,EAAE,IAAA,CAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACxD;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACxB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,MAAA,EACR,IAAI,MAAM,KAAA,IAAS,QAAK,GACxB,IAAI,EAAE,KAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,GAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,MAAA,EACN,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA,GACN,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,KAAK,GACxB,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,GAC1B,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,GAAG,GACxB,IAAI,IAAI,MAAM,IACd,IAAI,GACJ,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IACA;QACA,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,OAAO,KAAK,SAAU,CAAA,KAAK,EAAE,EAAE,CAAC,CAAC;aAChC;YACH,IAAI,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,GACnB,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC;YACnB,IAAI,EAAE,OAAA,EACJ,IAAK,KAAK,EAAE,IAAA,CAAK,CAAC,GAAG,KAAK,GACxB,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KAAK;gBAC5C,IAAI,IAAI,EAAE,QAAA,KAAa,MAAM,MAAM;gBACnC,KAAK,MACF,MAAA,CAAO,GAAG,MAAM,EAChB,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,CAAC,EACR,MAAA,CAAO,GAAG,KAAK,EACf,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,GAAG,GAAG,EACb,MAAA,CAAO,CAAC;YACb,MAAO,CAAA,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;iBACrF;gBACH,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KACvC,MAAM,IAAI,UAAU,mBAAmB,MAAA,CAAO,EAAE,IAAA,EAAM,+BAA+B,CAAC;gBACxF,KAAK,IAAI,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;YACnD;iBACG,KAAK,MAAM,MAAA,CAAO,CAAC,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;QAC5D;IACF;IACA,IAAI,EAAG,CAAA,KAAA,CAAM,KAAK,GAAG,MAAA,CAAO,GAAG,GAAG,CAAA,GAAK,KAAK,EAAE,QAAA,GAAW,MAAM,MAAA,CAAO,GAAG,GAAG,IAAI;SAC3E;QACH,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,OAAO,KAAK,WAAW,EAAE,OAAA,CAAQ,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAC,IAAI,CAAA,IAAK,MAAM,KAAA;QACrE,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,KAAK,EAAE,MAAA,CAAO,GAAG,KAAK,CAAA,GAAI,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,CAAA;IACpG;IACA,OAAO,IAAI,OAAO,GAAG,EAAE,CAAC,CAAC;AAC3B;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,aAAa,SAAS,EAAE,GAAG,CAAC,IAAI,MAAM,OAAA,CAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC;AAClF;;AC/TO,IAAM,eAAe,CAAC,SAAiB;IAC5C,IAAI;QAEF,OAAO,EAAiB,IAAI;IAC9B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA,cAAA,EAAiB,IAAI,CAAA;;AAAA,EAA6G,EAAE,OAAO,EAAA;IAE/I;AACF;AAEO,SAAS,MACd,GAAA,EACA,OAAA,EACkB;IAClB,IAAI;QAEF,OAAO,EAAU,KAAK,OAAO;IAC/B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA;AAAA,EAAoI,EAAE,OAAO,EAAA;IAEjJ;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/buildAccountsBaseUrl.ts"], "sourcesContent": ["/**\n * Builds a full origin string pointing to the Account Portal for the given frontend API.\n */\nexport function buildAccountsBaseUrl(frontendApi?: string): string {\n  if (!frontendApi) {\n    return '';\n  }\n\n  // convert url from FAPI to accounts for Kima and legacy (prod & dev) instances\n  const accountsBaseUrl = frontendApi\n    // staging accounts\n    .replace(/clerk\\.accountsstage\\./, 'accountsstage.')\n    .replace(/clerk\\.accounts\\.|clerk\\./, 'accounts.');\n  return `https://${accountsBaseUrl}`;\n}\n"], "names": [], "mappings": ";;;;;;AAGO,SAAS,qBAAqB,WAAA,EAA8B;IACjE,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAGA,MAAM,kBAAkB,YAErB,OAAA,CAAQ,0BAA0B,gBAAgB,EAClD,OAAA,CAAQ,6BAA6B,WAAW;IACnD,OAAO,CAAA,QAAA,EAAW,eAAe,EAAA;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization-errors.ts"], "sourcesContent": ["import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n"], "names": [], "mappings": ";;;;;;AAMA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,gBAAA,CAKK;QACL,aAAa;YACX,MAAM;YACN,QAAQ;YACR,UAAU;gBACR,gBAAgB;YAClB;QACF;IACF,CAAA;AAEA,IAAM,8BAA8B,CAAA,GAAI,OACtC,IAAI,SAAS,KAAK,SAAA,CAAU,oBAAoB,GAAG,IAAI,CAAC,GAAG;QACzD,QAAQ;IACV,CAAC;AAEH,IAAM,uBAAuB,CAAC,WAAkE;IAC9F,OACE,UACA,OAAO,WAAW,YAClB,iBAAiB,UACjB,OAAO,WAAA,EAAa,SAAS,eAC7B,OAAO,WAAA,EAAa,WAAW;AAEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/allSettled.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/logErrorInDevMode.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/fastDeepMerge.ts"], "sourcesContent": ["/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIO,SAAS,WACd,QAAA,EACsF;IACtF,MAAM,WAAW,MAAM,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,CAAA,IACxC,EAAE,IAAA,CACA,CAAA,QAAA,CAAU;gBAAE,QAAQ;gBAAa;YAAM,CAAA,GACvC,CAAA,SAAA,CAAW;gBAAE,QAAQ;gBAAY;YAAO,CAAA;IAG5C,OAAO,QAAQ,GAAA,CAAI,QAAQ;AAC7B;;ACZO,IAAM,oBAAoB,CAAC,YAAoB;IACpD,kRAAI,2BAAA,CAAyB,IAAG;QAC9B,QAAQ,KAAA,CAAM,CAAA,OAAA,EAAU,OAAO,EAAE;IACnC;AACF;;ACDO,IAAM,0BAA0B,CACrC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,wBAAwB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAClD,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,GAAG;YAC5D,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,qBAAqB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAC/C,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;YACzF,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/noop.ts"], "sourcesContent": ["export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,OAAO,CAAA,GAAI,SAExB,CAF+C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/createDeferredPromise.ts"], "sourcesContent": ["import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n"], "names": [], "mappings": ";;;;;;AAUO,IAAM,wBAAwB,MAAM;IACzC,IAAI,oRAAoB,OAAA;IACxB,IAAI,mRAAmB,OAAA;IACvB,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;QACxC,UAAU;QACV,SAAS;IACX,CAAC;IACD,OAAO;QAAE;QAAS;QAAS;IAAO;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/handleValueOrFn.ts"], "sourcesContent": ["type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,gBAAmB,KAAA,EAAyB,GAAA,EAAU,YAAA,EAAiC;IACrG,IAAI,OAAO,UAAU,YAAY;QAC/B,OAAQ,MAAwB,GAAG;IACrC;IAEA,IAAI,OAAO,UAAU,aAAa;QAChC,OAAO;IACT;IAEA,IAAI,OAAO,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/apiUrlFromPublishableKey.ts"], "sourcesContent": ["import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n"], "names": [], "mappings": ";;;;;;;;AAgBO,IAAM,2BAA2B,CAAC,mBAA2B;IAClE,MAAM,4RAAc,sBAAA,EAAoB,cAAc,GAAG;IAEzD,IAAI,aAAa,WAAW,QAAQ,+QAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACnH,iRAAO,eAAA;IACT;IAEA,6QAAI,sBAAA,CAAmB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACpE,iRAAO,gBAAA;IACT;IACA,IAAI,iSAAA,CAAqB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACtE,iRAAO,kBAAA;IACT;IACA,iRAAO,eAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/underscore.ts"], "sourcesContent": ["/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAMO,IAAM,aAAa,CAAC,UAA4B;IAErD,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO;IACT;IACA,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO,KAAA,CAAM,CAAC,CAAA;IAChB;IACA,IAAI,WAAW,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,IAAI;IAC3C,YAAY,CAAA,KAAA,EAAQ,MAAM,KAAA,CAAM,CAAA,CAAE,CAAC,EAAA;IACnC,OAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,GAAA,EAAyC;IACrE,OAAO,oBAAoB,IAAA,CAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,GAAA,EAAwC;IAC/D,MAAM,IAAI,OAAO;IACjB,OAAO,EAAE,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,EAAE,KAAA,CAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,gBAAgB,CAAA,QAAS,MAAM,WAAA,CAAY,EAAE,OAAA,CAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,UAAU,CAAA,SAAU,CAAA,CAAA,EAAI,OAAO,WAAA,CAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;IACtD,MAAM,gBAAgB,CAAC,QAAkB;QACvC,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,IAAI,MAAM,OAAA,CAAQ,GAAG,GAAG;YACtB,OAAO,IAAI,GAAA,CAAI,CAAA,OAAM;gBACnB,IAAI,OAAO,OAAO,YAAY,MAAM,OAAA,CAAQ,EAAE,GAAG;oBAC/C,OAAO,cAAc,EAAE;gBACzB;gBACA,OAAO;YACT,CAAC;QACH;QAEA,MAAM,OAAO;YAAE,GAAG,GAAA;QAAI;QACtB,MAAM,OAAO,OAAO,IAAA,CAAK,IAAI;QAC7B,KAAA,MAAW,WAAW,KAAM;YAC1B,MAAM,UAAU,UAAU,QAAQ,QAAA,CAAS,CAAC;YAC5C,IAAI,YAAY,SAAS;gBACvB,IAAA,CAAK,OAAO,CAAA,GAAI,IAAA,CAAK,OAAO,CAAA;gBAC5B,OAAO,IAAA,CAAK,OAAO,CAAA;YACrB;YACA,IAAI,OAAO,IAAA,CAAK,OAAO,CAAA,KAAM,UAAU;gBACrC,IAAA,CAAK,OAAO,CAAA,GAAI,cAAc,IAAA,CAAK,OAAO,CAAC;YAC7C;QACF;QACA,OAAO;IACT;IAEA,OAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,KAAA,EAAyB;IAEhD,IAAI,OAAO,UAAU,CAAA,OAAA,CAAA,EAAW;QAC9B,OAAO;IACT;IAGA,IAAI,UAAU,KAAA,KAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,UAAU,CAAA,MAAA,CAAA,EAAU;QAC7B,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,IAAA,CAAA,EAAQ;YAClC,OAAO;QACT;QAEA,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,KAAA,CAAA,EAAS;YACnC,OAAO;QACT;IACF;IAGA,MAAM,SAAS,SAAS,OAAiB,EAAE;IAC3C,IAAI,MAAM,MAAM,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IAGA,OAAO;AACT;AAKO,SAAS,sBAAwC,GAAA,EAAoB;IAC1E,OAAO,OAAO,OAAA,CAAQ,GAAG,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAA,KAAM;QACvD,IAAI,UAAU,KAAA,GAAW;YACvB,GAAA,CAAI,GAAc,CAAA,GAAI;QACxB;QACA,OAAO;IACT,GAAG,CAAC,CAAe;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/map-obj%404.3.0/node_modules/map-obj/index.js"], "sourcesContent": ["'use strict';\n\nconst isObject = value => typeof value === 'object' && value !== null;\nconst mapObjectSkip = Symbol('skip');\n\n// Customized for this use-case\nconst isObjectCustom = value =>\n\tisObject(value) &&\n\t!(value instanceof RegExp) &&\n\t!(value instanceof Error) &&\n\t!(value instanceof Date);\n\nconst mapObject = (object, mapper, options, isSeen = new WeakMap()) => {\n\toptions = {\n\t\tdeep: false,\n\t\ttarget: {},\n\t\t...options\n\t};\n\n\tif (isSeen.has(object)) {\n\t\treturn isSeen.get(object);\n\t}\n\n\tisSeen.set(object, options.target);\n\n\tconst {target} = options;\n\tdelete options.target;\n\n\tconst mapArray = array => array.map(element => isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);\n\tif (Array.isArray(object)) {\n\t\treturn mapArray(object);\n\t}\n\n\tfor (const [key, value] of Object.entries(object)) {\n\t\tconst mapResult = mapper(key, value, object);\n\n\t\tif (mapResult === mapObjectSkip) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [newKey, newValue, {shouldRecurse = true} = {}] = mapResult;\n\n\t\t// Drop `__proto__` keys.\n\t\tif (newKey === '__proto__') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (options.deep && shouldRecurse && isObjectCustom(newValue)) {\n\t\t\tnewValue = Array.isArray(newValue) ?\n\t\t\t\tmapArray(newValue) :\n\t\t\t\tmapObject(newValue, mapper, options, isSeen);\n\t\t}\n\n\t\ttarget[newKey] = newValue;\n\t}\n\n\treturn target;\n};\n\nmodule.exports = (object, mapper, options) => {\n\tif (!isObject(object)) {\n\t\tthrow new TypeError(`Expected an object, got \\`${object}\\` (${typeof object})`);\n\t}\n\n\treturn mapObject(object, mapper, options);\n};\n\nmodule.exports.mapObjectSkip = mapObjectSkip;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,WAAW,CAAA,QAAS,OAAO,UAAU,YAAY,UAAU;AACjE,MAAM,gBAAgB,OAAO;AAE7B,+BAA+B;AAC/B,MAAM,iBAAiB,CAAA,QACtB,SAAS,UACT,CAAC,CAAC,iBAAiB,MAAM,KACzB,CAAC,CAAC,iBAAiB,KAAK,KACxB,CAAC,CAAC,iBAAiB,IAAI;AAExB,MAAM,YAAY,CAAC,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;IACjE,UAAU;QACT,MAAM;QACN,QAAQ,CAAC;QACT,GAAG,OAAO;IACX;IAEA,IAAI,OAAO,GAAG,CAAC,SAAS;QACvB,OAAO,OAAO,GAAG,CAAC;IACnB;IAEA,OAAO,GAAG,CAAC,QAAQ,QAAQ,MAAM;IAEjC,MAAM,EAAC,MAAM,EAAC,GAAG;IACjB,OAAO,QAAQ,MAAM;IAErB,MAAM,WAAW,CAAA,QAAS,MAAM,GAAG,CAAC,CAAA,UAAW,eAAe,WAAW,UAAU,SAAS,QAAQ,SAAS,UAAU;IACvH,IAAI,MAAM,OAAO,CAAC,SAAS;QAC1B,OAAO,SAAS;IACjB;IAEA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QAClD,MAAM,YAAY,OAAO,KAAK,OAAO;QAErC,IAAI,cAAc,eAAe;YAChC;QACD;QAEA,IAAI,CAAC,QAAQ,UAAU,EAAC,gBAAgB,IAAI,EAAC,GAAG,CAAC,CAAC,CAAC,GAAG;QAEtD,yBAAyB;QACzB,IAAI,WAAW,aAAa;YAC3B;QACD;QAEA,IAAI,QAAQ,IAAI,IAAI,iBAAiB,eAAe,WAAW;YAC9D,WAAW,MAAM,OAAO,CAAC,YACxB,SAAS,YACT,UAAU,UAAU,QAAQ,SAAS;QACvC;QAEA,MAAM,CAAC,OAAO,GAAG;IAClB;IAEA,OAAO;AACR;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ,QAAQ;IACjC,IAAI,CAAC,SAAS,SAAS;QACtB,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO,CAAC,CAAC;IAC/E;IAEA,OAAO,UAAU,QAAQ,QAAQ;AAClC;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/tslib%402.8.1/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/lower-case%402.0.2/node_modules/lower-case/src/index.ts"], "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\",\n    },\n  },\n};\n\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return lowerCase(str);\n}\n\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str: string) {\n  return str.toLowerCase();\n}\n"], "names": [], "mappings": "AAQA;;GAEG;;;;AACH,IAAM,gBAAgB,GAA2B;IAC/C,EAAE,EAAE;QACF,MAAM,EAAE,6BAA6B;QACrC,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,SAAS;QACjB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,4CAA4C;QACpD,GAAG,EAAE;YACH,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;SACxB;KACF;CACF,CAAC;AAKI,SAAU,eAAe,CAAC,GAAW,EAAE,MAAc;IACzD,IAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,IAAI,IAAI,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,SAAC,CAAC;QAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAAX,CAAW,CAAC,CAAC,CAAC;IACzE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAKK,SAAU,SAAS,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC3B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/no-case%403.0.4/node_modules/no-case/src/index.ts"], "sourcesContent": ["import { lowerCase } from \"lower-case\";\n\nexport interface Options {\n  splitRegexp?: RegExp | RegExp[];\n  stripRegexp?: RegExp | RegExp[];\n  delimiter?: string;\n  transform?: (part: string, index: number, parts: string[]) => string;\n}\n\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nconst DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n\n// Remove all non-word characters.\nconst DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input: string, options: Options = {}) {\n  const {\n    splitRegexp = DEFAULT_SPLIT_REGEXP,\n    stripRegexp = DEFAULT_STRIP_REGEXP,\n    transform = lowerCase,\n    delimiter = \" \",\n  } = options;\n\n  let result = replace(\n    replace(input, splitRegexp, \"$1\\0$2\"),\n    stripRegexp,\n    \"\\0\"\n  );\n  let start = 0;\n  let end = result.length;\n\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  while (result.charAt(end - 1) === \"\\0\") end--;\n\n  // Transform each token independently.\n  return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input: string, re: RegExp | RegExp[], value: string) {\n  if (re instanceof RegExp) return input.replace(re, value);\n  return re.reduce((input, re) => input.replace(re, value), input);\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;;AASvC,oFAAoF;AACpF,IAAM,oBAAoB,GAAG;IAAC,oBAAoB;IAAE,sBAAsB;CAAC,CAAC;AAE5E,kCAAkC;AAClC,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAKtC,SAAU,MAAM,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAEvD,IAAA,KAIE,OAAO,CAAA,WAJyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAGE,OAAO,CAAA,WAHyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAEE,OAAO,CAAA,SAFY,EAArB,SAAS,GAAA,OAAA,KAAA,uNAAG,YAAS,GAAA,EAAA,EACrB,KACE,OAAO,CAAA,SADM,EAAf,SAAS,GAAA,OAAA,KAAA,IAAG,GAAG,GAAA,EAAA,CACL;IAEZ,IAAI,MAAM,GAAG,OAAO,CAClB,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EACrC,WAAW,EACX,IAAI,CACL,CAAC;IACF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,oDAAoD;IACpD,MAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,CAAE,KAAK,EAAE,CAAC;IAC9C,MAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE,GAAG,EAAE,CAAC;IAE9C,sCAAsC;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7E,CAAC;AAED;;GAEG,CACH,SAAS,OAAO,CAAC,KAAa,EAAE,EAAqB,EAAE,KAAa;IAClE,IAAI,EAAE,YAAY,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,EAAE,CAAC,MAAM,CAAC,SAAC,KAAK,EAAE,EAAE;QAAK,OAAA,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC;IAAxB,CAAwB,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3203, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/dot-case%403.0.4/node_modules/dot-case/src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,EAAW,MAAM,SAAS,CAAC;;;AAIpC,SAAU,OAAO,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC1D,wNAAO,SAAA,AAAM,EAAC,KAAK,EAAA,CAAA,GAAA,wLAAA,CAAA,WAAA,EAAA;QACjB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3224, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/snake-case%403.0.4/node_modules/snake-case/src/index.ts"], "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function snakeCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"_\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,OAAO,EAAW,MAAM,UAAU,CAAC;;;AAItC,SAAU,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC5D,0NAAO,UAAA,AAAO,EAAC,KAAK,EAAA,CAAA,GAAA,wLAAA,CAAA,WAAA,EAAA;QAClB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/snakecase-keys%408.0.1/node_modules/snakecase-keys/index.js"], "sourcesContent": ["'use strict'\n\nconst map = require('map-obj')\nconst { snakeCase } = require('snake-case')\n\nconst PlainObjectConstructor = {}.constructor\n\nmodule.exports = function (obj, options) {\n  if (Array.isArray(obj)) {\n    if (obj.some(item => item.constructor !== PlainObjectConstructor)) {\n      throw new Error('obj must be array of plain objects')\n    }\n  } else {\n    if (obj.constructor !== PlainObjectConstructor) {\n      throw new Error('obj must be an plain object')\n    }\n  }\n\n  options = Object.assign({ deep: true, exclude: [], parsingOptions: {} }, options)\n\n  return map(obj, function (key, val) {\n    return [\n      matches(options.exclude, key) ? key : snakeCase(key, options.parsingOptions),\n      val,\n      mapperOptions(key, val, options)\n    ]\n  }, options)\n}\n\nfunction matches (patterns, value) {\n  return patterns.some(function (pattern) {\n    return typeof pattern === 'string'\n      ? pattern === value\n      : pattern.test(value)\n  })\n}\n\nfunction mapperOptions (key, val, options) {\n  return options.shouldRecurse\n    ? { shouldRecurse: options.shouldRecurse(key, val) }\n    : undefined\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AAEnB,MAAM,yBAAyB,CAAC,EAAE,WAAW;AAE7C,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,IAAI,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,yBAAyB;YACjE,MAAM,IAAI,MAAM;QAClB;IACF,OAAO;QACL,IAAI,IAAI,WAAW,KAAK,wBAAwB;YAC9C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,UAAU,OAAO,MAAM,CAAC;QAAE,MAAM;QAAM,SAAS,EAAE;QAAE,gBAAgB,CAAC;IAAE,GAAG;IAEzE,OAAO,IAAI,KAAK,SAAU,GAAG,EAAE,GAAG;QAChC,OAAO;YACL,QAAQ,QAAQ,OAAO,EAAE,OAAO,MAAM,UAAU,KAAK,QAAQ,cAAc;YAC3E;YACA,cAAc,KAAK,KAAK;SACzB;IACH,GAAG;AACL;AAEA,SAAS,QAAS,QAAQ,EAAE,KAAK;IAC/B,OAAO,SAAS,IAAI,CAAC,SAAU,OAAO;QACpC,OAAO,OAAO,YAAY,WACtB,YAAY,QACZ,QAAQ,IAAI,CAAC;IACnB;AACF;AAEA,SAAS,cAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IACvC,OAAO,QAAQ,aAAa,GACxB;QAAE,eAAe,QAAQ,aAAa,CAAC,KAAK;IAAK,IACjD;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3286, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/cookie%401.0.2/node_modules/cookie/src/index.ts"], "sourcesContent": ["/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp =\n  /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\nconst __toString = Object.prototype.toString;\n\nconst NullObject = /* @__PURE__ */ (() => {\n  const C = function () {};\n  C.prototype = Object.create(null);\n  return C;\n})() as unknown as { new (): any };\n\n/**\n * Parse options.\n */\nexport interface ParseOptions {\n  /**\n   * Specifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\n   * a previously-encoded cookie value into a JavaScript string.\n   *\n   * The default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\n   * is thrown it will return the cookie's original value. If you provide your own encode/decode\n   * scheme you must ensure errors are appropriately handled.\n   *\n   * @default decode\n   */\n  decode?: (str: string) => string | undefined;\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(\n  str: string,\n  options?: ParseOptions,\n): Record<string, string | undefined> {\n  const obj: Record<string, string | undefined> = new NullObject();\n  const len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  const dec = options?.decode || decode;\n  let index = 0;\n\n  do {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    const colonIdx = str.indexOf(\";\", index);\n    const endIdx = colonIdx === -1 ? len : colonIdx;\n\n    if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n\n    const keyStartIdx = startIndex(str, index, eqIdx);\n    const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    const key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (obj[key] === undefined) {\n      let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      const value = dec(str.slice(valStartIdx, valEndIdx));\n      obj[key] = value;\n    }\n\n    index = endIdx + 1;\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str: string, index: number, max: number) {\n  do {\n    const code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str: string, index: number, min: number) {\n  while (index > min) {\n    const code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize options.\n */\nexport interface SerializeOptions {\n  /**\n   * Specifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\n   * a value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n   *\n   * @default encodeURIComponent\n   */\n  encode?: (str: string) => string;\n  /**\n   * Specifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  maxAge?: number;\n  /**\n   * Specifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\n   * When no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  expires?: Date;\n  /**\n   * Specifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\n   * When no domain is set clients consider the cookie to apply to the current domain only.\n   */\n  domain?: string;\n  /**\n   * Specifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\n   * When no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n   */\n  path?: string;\n  /**\n   * Enables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\n   * When enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n   */\n  httpOnly?: boolean;\n  /**\n   * Enables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\n   * When enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n   */\n  secure?: boolean;\n  /**\n   * Enables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\n   * When enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n   *\n   * This is an attribute that has not yet been fully standardized, and may change in the future.\n   * This also means clients may ignore this attribute until they understand it. More information\n   * about can be found in [the proposal](https://github.com/privacycg/CHIPS).\n   */\n  partitioned?: boolean;\n  /**\n   * Specifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   *\n   * - `'low'` will set the `Priority` attribute to `Low`.\n   * - `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n   * - `'high'` will set the `Priority` attribute to `High`.\n   *\n   * More information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   */\n  priority?: \"low\" | \"medium\" | \"high\";\n  /**\n   * Specifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   *\n   * - `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   * - `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n   * - `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n   * - `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   *\n   * More information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   */\n  sameSite?: boolean | \"lax\" | \"strict\" | \"none\";\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(\n  name: string,\n  val: string,\n  options?: SerializeOptions,\n): string {\n  const enc = options?.encode || encodeURIComponent;\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError(`argument name is invalid: ${name}`);\n  }\n\n  const value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError(`argument val is invalid: ${val}`);\n  }\n\n  let str = name + \"=\" + value;\n  if (!options) return str;\n\n  if (options.maxAge !== undefined) {\n    if (!Number.isInteger(options.maxAge)) {\n      throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n    }\n\n    str += \"; Max-Age=\" + options.maxAge;\n  }\n\n  if (options.domain) {\n    if (!domainValueRegExp.test(options.domain)) {\n      throw new TypeError(`option domain is invalid: ${options.domain}`);\n    }\n\n    str += \"; Domain=\" + options.domain;\n  }\n\n  if (options.path) {\n    if (!pathValueRegExp.test(options.path)) {\n      throw new TypeError(`option path is invalid: ${options.path}`);\n    }\n\n    str += \"; Path=\" + options.path;\n  }\n\n  if (options.expires) {\n    if (\n      !isDate(options.expires) ||\n      !Number.isFinite(options.expires.valueOf())\n    ) {\n      throw new TypeError(`option expires is invalid: ${options.expires}`);\n    }\n\n    str += \"; Expires=\" + options.expires.toUTCString();\n  }\n\n  if (options.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n\n  if (options.secure) {\n    str += \"; Secure\";\n  }\n\n  if (options.partitioned) {\n    str += \"; Partitioned\";\n  }\n\n  if (options.priority) {\n    const priority =\n      typeof options.priority === \"string\"\n        ? options.priority.toLowerCase()\n        : undefined;\n    switch (priority) {\n      case \"low\":\n        str += \"; Priority=Low\";\n        break;\n      case \"medium\":\n        str += \"; Priority=Medium\";\n        break;\n      case \"high\":\n        str += \"; Priority=High\";\n        break;\n      default:\n        throw new TypeError(`option priority is invalid: ${options.priority}`);\n    }\n  }\n\n  if (options.sameSite) {\n    const sameSite =\n      typeof options.sameSite === \"string\"\n        ? options.sameSite.toLowerCase()\n        : options.sameSite;\n    switch (sameSite) {\n      case true:\n      case \"strict\":\n        str += \"; SameSite=Strict\";\n        break;\n      case \"lax\":\n        str += \"; SameSite=Lax\";\n        break;\n      case \"none\":\n        str += \"; SameSite=None\";\n        break;\n      default:\n        throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str: string): string {\n  if (str.indexOf(\"%\") === -1) return str;\n\n  try {\n    return decodeURIComponent(str);\n  } catch (e) {\n    return str;\n  }\n}\n\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val: any): val is Date {\n  return __toString.call(val) === \"[object Date]\";\n}\n"], "names": [], "mappings": ";;;;AAiGA,QAAA,KAAA,GAAA,MA0CC;AA4GD,QAAA,SAAA,GAAA,UA6GC;AApWD;;;;;;;;;;;;;GAaG,CACH,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;AAEjE;;;;;;;;;;;GAWG,CACH,MAAM,iBAAiB,GAAG,iCAAiC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;GAsBG,CACH,MAAM,iBAAiB,GACrB,qFAAqF,CAAC;AAExF;;;;;;GAMG,CACH,MAAM,eAAe,GAAG,iCAAiC,CAAC;AAE1D,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE7C,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,CAAC,GAAG,EAAE;IACvC,MAAM,CAAC,GAAG,YAAa,CAAC,CAAC;IACzB,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC,EAAgC,CAAC;AAoBnC;;;;;GAKG,CACH,SAAgB,KAAK,CACnB,GAAW,EACX,OAAsB;IAEtB,MAAM,GAAG,GAAuC,IAAI,UAAU,EAAE,CAAC;IACjE,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,iGAAiG;IACjG,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;IAExB,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,GAAG,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,wBAAwB;QAEjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEhD,IAAI,KAAK,GAAG,MAAM,EAAE,CAAC;YACnB,+BAA+B;YAC/B,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE9C,mBAAmB;QACnB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEnD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACrD,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC,OAAQ,KAAK,GAAG,GAAG,CAAE;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACzD,GAAG,CAAC;QACF,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,CAAC;IACpE,CAAC,OAAQ,EAAE,KAAK,GAAG,GAAG,CAAE;IACxB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACvD,MAAO,KAAK,GAAG,GAAG,CAAE,CAAC;QACnB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,GAAG,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAmFD;;;;;;;;GAQG,CACH,SAAgB,SAAS,CACvB,IAAY,EACZ,GAAW,EACX,OAA0B;IAE1B,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,kBAAkB,CAAC;IAElD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,SAAS,CAAC,CAAA,yBAAA,EAA4B,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;IAEzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,SAAS,CAAC,CAAA,wBAAA,EAA2B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IACE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IACxB,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAC3C,CAAC;YACD,MAAM,IAAI,SAAS,CAAC,CAAA,2BAAA,EAA8B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,GAAG,IAAI,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,GAAG,IAAI,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,GAAG,IAAI,eAAe,CAAC;IACzB,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,SAAS,CAAC;QAChB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,OAAO,CAAC,QAAQ,CAAC;QACvB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC;YACV,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAW;IACzB,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;IAExC,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC;AAClD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/basehub%408.2.7_%40babel%2Bruntim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/chunk-BG6MEPCE.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\nexport {\n  __spreadValues,\n  __spreadProps,\n  __objRest,\n  __async\n};\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,aAAa,OAAO,gBAAgB;AACxC,IAAI,oBAAoB,OAAO,yBAAyB;AACxD,IAAI,sBAAsB,OAAO,qBAAqB;AACtD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,oBAAoB;AACxD,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,iBAAiB,CAAC,GAAG;IACvB,IAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAC3B,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC,IAAI,qBACF,KAAK,IAAI,QAAQ,oBAAoB,GAAI;QACvC,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC;IACF,OAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,IAAM,WAAW,GAAG,kBAAkB;AAC9D,IAAI,YAAY,CAAC,QAAQ;IACvB,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,QAAQ,OACf,IAAI,aAAa,IAAI,CAAC,QAAQ,SAAS,QAAQ,OAAO,CAAC,QAAQ,GAC7D,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B,IAAI,UAAU,QAAQ,qBACpB,KAAK,IAAI,QAAQ,oBAAoB,QAAS;QAC5C,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,aAAa,IAAI,CAAC,QAAQ,OACzD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B;IACF,OAAO;AACT;AACA,IAAI,UAAU,CAAC,QAAQ,aAAa;IAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,YAAY,CAAC;YACf,IAAI;gBACF,KAAK,UAAU,IAAI,CAAC;YACtB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,WAAW,CAAC;YACd,IAAI;gBACF,KAAK,UAAU,KAAK,CAAC;YACvB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,OAAO,CAAC,IAAM,EAAE,IAAI,GAAG,QAAQ,EAAE,KAAK,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW;QACvF,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,QAAQ,YAAY,EAAE,IAAI;IAC9D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/basehub%408.2.7_%40babel%2Bruntim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/react-rich-text.js"], "sourcesContent": ["import {\n  __objRest,\n  __spreadProps,\n  __spreadValues\n} from \"./chunk-BG6MEPCE.js\";\n\n// src/react/rich-text/primitive.tsx\nimport GithubSlugger from \"github-slugger\";\n\n// src/react/rich-text/util/heading-id.ts\nfunction extractTextFromNode(node) {\n  var _a;\n  let textContent = \"\";\n  (_a = node == null ? void 0 : node.content) == null ? void 0 : _a.forEach((child) => {\n    if (child.type === \"text\") {\n      textContent += child.text;\n    }\n    if (\"content\" in child && child.content) {\n      textContent += extractTextFromNode(child);\n    }\n  });\n  return textContent;\n}\n\n// src/react/rich-text/primitive.tsx\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar isDev = process.env.NODE_ENV === \"development\";\nvar SUFFIX_CUSTOM_MARK = \"_mark\";\nvar RichText = (props) => {\n  var _a;\n  let value;\n  const contentRaw = (_a = props.content) != null ? _a : props.children;\n  if (contentRaw) {\n    if (Array.isArray(contentRaw)) {\n      value = contentRaw;\n    } else if (typeof contentRaw === \"object\" && contentRaw && \"content\" in contentRaw && // @ts-ignore\n    Array.isArray(contentRaw.content)) {\n      value = contentRaw.content;\n    }\n  }\n  const slugger = new GithubSlugger();\n  return /* @__PURE__ */ jsx(Fragment, { children: value == null ? void 0 : value.map((node, index) => {\n    return /* @__PURE__ */ jsx(\n      Node,\n      {\n        node,\n        components: props.components,\n        blocks: props.blocks,\n        slugger,\n        disableDefaultComponents: props.disableDefaultComponents\n      },\n      index\n    );\n  }) });\n};\nvar defaultHandlers = {\n  a: (props) => {\n    return /* @__PURE__ */ jsx(\"a\", __spreadValues({}, props));\n  },\n  p: ({ children }) => /* @__PURE__ */ jsx(\"p\", { children }),\n  b: ({ children }) => /* @__PURE__ */ jsx(\"b\", { children }),\n  highlight: ({ children }) => /* @__PURE__ */ jsx(\"span\", { children }),\n  em: ({ children }) => /* @__PURE__ */ jsx(\"em\", { children }),\n  s: ({ children }) => /* @__PURE__ */ jsx(\"s\", { children }),\n  kbd: ({ children }) => /* @__PURE__ */ jsx(\"kbd\", { children }),\n  code: ({ children }) => /* @__PURE__ */ jsx(\"code\", { children }),\n  ol: (_a) => {\n    var _b = _a, { children } = _b, rest = __objRest(_b, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"ol\", __spreadProps(__spreadValues({}, rest), { children }));\n  },\n  ul: (_c) => {\n    var _d = _c, { children } = _d, rest = __objRest(_d, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"ul\", __spreadProps(__spreadValues({}, rest), { children }));\n  },\n  li: (_e) => {\n    var _f = _e, { children } = _f, rest = __objRest(_f, [\"children\"]);\n    return /* @__PURE__ */ jsxs(\n      \"li\",\n      __spreadProps(__spreadValues({}, rest[\"data-is-task-list\"] ? { style: { display: \"flex\", alignItems: \"baseline\" } } : void 0), {\n        children: [\n          rest[\"data-is-task-list\"] ? /* @__PURE__ */ jsx(\"input\", { type: \"checkbox\", defaultChecked: rest[\"data-checked\"] }) : null,\n          children\n        ]\n      })\n    );\n  },\n  h1: (_g) => {\n    var _h = _g, { children } = _h, props = __objRest(_h, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h1\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  h2: (_i) => {\n    var _j = _i, { children } = _j, props = __objRest(_j, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h2\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  h3: (_k) => {\n    var _l = _k, { children } = _l, props = __objRest(_l, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h3\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  h4: (_m) => {\n    var _n = _m, { children } = _n, props = __objRest(_n, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h4\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  h5: (_o) => {\n    var _p = _o, { children } = _p, props = __objRest(_p, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h5\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  h6: (_q) => {\n    var _r = _q, { children } = _r, props = __objRest(_r, [\"children\"]);\n    return /* @__PURE__ */ jsx(\"h6\", __spreadProps(__spreadValues({}, props), { children }));\n  },\n  hr: () => /* @__PURE__ */ jsx(\"hr\", {}),\n  img: (_s) => {\n    var _t = _s, { caption, alt } = _t, rest = __objRest(_t, [\"caption\", \"alt\"]);\n    return /* @__PURE__ */ jsx(\n      \"img\",\n      __spreadValues(__spreadProps(__spreadValues({}, rest), {\n        alt: alt != null ? alt : caption\n      }), caption ? { [\"data-caption\"]: caption } : {})\n    );\n  },\n  video: (_u) => {\n    var _v = _u, { caption } = _v, rest = __objRest(_v, [\"caption\"]);\n    return /* @__PURE__ */ jsx(\n      \"video\",\n      __spreadValues(__spreadValues({\n        controls: true\n      }, rest), caption ? { [\"data-caption\"]: caption } : {})\n    );\n  },\n  blockquote: ({ children }) => /* @__PURE__ */ jsx(\"blockquote\", { children }),\n  pre: ({ children, language }) => /* @__PURE__ */ jsx(\"pre\", { \"data-language\": language, className: `language-${language}`, children }),\n  table: ({ children }) => /* @__PURE__ */ jsx(\"table\", { children }),\n  tr: ({ children }) => /* @__PURE__ */ jsx(\"tr\", { children }),\n  td: ({ children, colspan, rowspan }) => /* @__PURE__ */ jsx(\"td\", { colSpan: colspan, rowSpan: rowspan, children }),\n  th: ({ children, colspan, rowspan }) => /* @__PURE__ */ jsx(\"th\", { colSpan: colspan, rowSpan: rowspan, children }),\n  thead: ({ children }) => /* @__PURE__ */ jsx(\"thead\", { children }),\n  tbody: ({ children }) => /* @__PURE__ */ jsx(\"tbody\", { children }),\n  tfoot: ({ children }) => /* @__PURE__ */ jsx(\"tfoot\", { children }),\n  br: () => /* @__PURE__ */ jsx(\"br\", {}),\n  u: ({ children }) => /* @__PURE__ */ jsx(\"u\", { children })\n};\nvar Node = ({\n  node,\n  components,\n  blocks,\n  slugger,\n  disableDefaultComponents\n}) => {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _A, _B, _C, _D, _E, _F;\n  const children = (_a = node.content) == null ? void 0 : _a.map((childNode, index) => {\n    return /* @__PURE__ */ jsx(\n      Node,\n      {\n        node: childNode,\n        parent: node,\n        components,\n        blocks,\n        slugger,\n        disableDefaultComponents\n      },\n      index\n    );\n  });\n  let Handler;\n  let props;\n  switch (node.type) {\n    case \"paragraph\":\n      Handler = (_b = components == null ? void 0 : components.p) != null ? _b : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.p;\n      props = { children };\n      break;\n    case \"text\":\n      const clonedMarks = [...(_c = node.marks) != null ? _c : []];\n      Handler = ({ children: children2 }) => /* @__PURE__ */ jsx(\n        Marks,\n        {\n          marks: clonedMarks,\n          components,\n          blocks,\n          disableDefaultComponents,\n          children: children2\n        }\n      );\n      props = { children: node.text };\n      break;\n    case \"bulletList\":\n    case \"taskList\":\n      Handler = (_d = components == null ? void 0 : components.ul) != null ? _d : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.ul;\n      props = __spreadValues({\n        children\n      }, node.type === \"taskList\" ? { [\"data-is-task-list\"]: true } : {});\n      break;\n    case \"orderedList\":\n      Handler = (_e = components == null ? void 0 : components.ol) != null ? _e : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.ol;\n      props = {\n        children,\n        start: (_g = (_f = node.attrs) == null ? void 0 : _f.start) != null ? _g : 1\n      };\n      break;\n    case \"listItem\":\n      Handler = (_h = components == null ? void 0 : components.li) != null ? _h : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.li;\n      props = {\n        children\n      };\n      break;\n    case \"taskItem\":\n      Handler = (_i = components == null ? void 0 : components.li) != null ? _i : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.li;\n      props = {\n        children,\n        [\"data-is-task-list\"]: true,\n        [\"data-checked\"]: (_k = (_j = node.attrs) == null ? void 0 : _j.checked) != null ? _k : false\n      };\n      break;\n    case \"heading\":\n      const handlerTag = `h${node.attrs.level}`;\n      Handler = (_l = components == null ? void 0 : components[handlerTag]) != null ? _l : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers[handlerTag];\n      const id = slugger.slug(extractTextFromNode(node));\n      props = { children, id };\n      break;\n    case \"horizontalRule\":\n      Handler = (_m = components == null ? void 0 : components.hr) != null ? _m : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.hr;\n      break;\n    case \"hardBreak\":\n      Handler = (_n = components == null ? void 0 : components.br) != null ? _n : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.br;\n      break;\n    case \"blockquote\":\n      Handler = (_o = components == null ? void 0 : components.blockquote) != null ? _o : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.blockquote;\n      props = { children };\n      break;\n    case \"codeBlock\":\n      Handler = (_p = components == null ? void 0 : components.pre) != null ? _p : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.pre;\n      const code = (_r = (_q = node.content) == null ? void 0 : _q[0].text) != null ? _r : \"\";\n      props = {\n        children,\n        language: (_t = (_s = node.attrs) == null ? void 0 : _s.language) != null ? _t : \"text\",\n        code\n      };\n      break;\n    case \"table\":\n      Handler = (_u = components == null ? void 0 : components.table) != null ? _u : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.table;\n      const overridenChildren = /* @__PURE__ */ jsx(\n        Node,\n        {\n          node: { type: \"tableBody\", content: node.content },\n          parent: node,\n          components,\n          blocks,\n          slugger,\n          disableDefaultComponents\n        }\n      );\n      props = { children: overridenChildren };\n      break;\n    case \"tableRow\":\n      Handler = (_v = components == null ? void 0 : components.tr) != null ? _v : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.tr;\n      props = { children };\n      break;\n    case \"tableCell\":\n      Handler = (_w = components == null ? void 0 : components.td) != null ? _w : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.td;\n      props = {\n        children,\n        colspan: node.attrs.colspan,\n        rowspan: node.attrs.rowspan,\n        colwidth: (_x = node.attrs.colwidth) != null ? _x : void 0\n      };\n      break;\n    case \"tableHeader\":\n      Handler = (_y = components == null ? void 0 : components.th) != null ? _y : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.th;\n      props = {\n        children,\n        colspan: node.attrs.colspan,\n        rowspan: node.attrs.rowspan,\n        colwidth: (_z = node.attrs.colwidth) != null ? _z : void 0\n      };\n      break;\n    case \"tableFooter\":\n      Handler = (_A = components == null ? void 0 : components.tfoot) != null ? _A : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.tfoot;\n      props = { children };\n      break;\n    case \"tableBody\":\n      Handler = (_B = components == null ? void 0 : components.tbody) != null ? _B : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.tbody;\n      props = { children };\n      break;\n    case \"image\":\n      Handler = (_C = components == null ? void 0 : components.img) != null ? _C : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.img;\n      props = {\n        src: node.attrs.src,\n        width: node.attrs.width,\n        height: node.attrs.height,\n        alt: node.attrs.alt,\n        caption: node.attrs.caption\n      };\n      break;\n    case \"video\":\n      Handler = (_D = components == null ? void 0 : components.video) != null ? _D : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.video;\n      props = {\n        children,\n        src: node.attrs.src,\n        width: node.attrs.width,\n        height: node.attrs.height,\n        caption: node.attrs.caption\n      };\n      break;\n    case \"basehub-block\": {\n      const block = blocks == null ? void 0 : blocks.find((block2) => {\n        var _a2, _b2;\n        const typename = block2 == null ? void 0 : block2.__typename;\n        const keysLength = Object.keys(block2).length;\n        const id2 = (_b2 = block2 == null ? void 0 : block2._id) != null ? _b2 : (_a2 = block2 == null ? void 0 : block2._sys) == null ? void 0 : _a2.id;\n        if (typeof id2 !== \"string\" && (!typename || keysLength > 1)) {\n          if (isDev) {\n            console.warn(\n              `BaseHub RichText Error: make sure you send through the _id and the __typename for all custom blocks.\nReceived ${JSON.stringify(\n                block2,\n                null,\n                2\n              )}.`\n            );\n          }\n          return false;\n        }\n        return id2 === node.attrs.id;\n      });\n      if (!block) {\n        break;\n      }\n      Handler = (_E = components == null ? void 0 : components[block == null ? void 0 : block.__typename]) != null ? _E : () => /* @__PURE__ */ jsx(Fragment, {});\n      props = block;\n      break;\n    }\n    default:\n      Handler = (_F = components == null ? void 0 : components[node.type]) != null ? _F : () => /* @__PURE__ */ jsx(Fragment, {});\n      props = __spreadProps(__spreadValues({}, node.attrs), { children });\n      break;\n  }\n  if (!Handler) {\n    return /* @__PURE__ */ jsx(Fragment, {});\n  }\n  return /* @__PURE__ */ jsx(Handler, __spreadValues({}, props));\n};\nvar Marks = ({\n  marks,\n  children,\n  components,\n  blocks,\n  disableDefaultComponents\n}) => {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;\n  if (!marks)\n    return /* @__PURE__ */ jsx(Fragment, { children });\n  const marksClone = [...marks];\n  const mark = marksClone.pop();\n  if (!mark)\n    return /* @__PURE__ */ jsx(Fragment, { children });\n  let Handler;\n  let props;\n  switch (mark.type) {\n    case \"bold\":\n      Handler = (_a = components == null ? void 0 : components.b) != null ? _a : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.b;\n      props = { children };\n      break;\n    case \"highlight\":\n      Handler = (_b = components == null ? void 0 : components.highlight) != null ? _b : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.highlight;\n      props = { children };\n      break;\n    case \"italic\":\n      Handler = (_c = components == null ? void 0 : components.em) != null ? _c : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.em;\n      props = { children };\n      break;\n    case \"strike\":\n      Handler = (_d = components == null ? void 0 : components.s) != null ? _d : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.s;\n      props = { children };\n      break;\n    case \"kbd\":\n      Handler = (_e = components == null ? void 0 : components.kbd) != null ? _e : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.kbd;\n      props = { children };\n      break;\n    case \"code\":\n      Handler = (_f = components == null ? void 0 : components.code) != null ? _f : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.code;\n      props = {\n        children\n      };\n      break;\n    case \"underline\":\n      Handler = (_g = components == null ? void 0 : components.u) != null ? _g : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.u;\n      props = { children };\n      break;\n    case \"link\": {\n      if (mark.attrs.type === \"internal\") {\n        const block = blocks == null ? void 0 : blocks.find((block2) => {\n          var _a2, _b2;\n          const typename = block2 == null ? void 0 : block2.__typename;\n          const keysLength = Object.keys(block2).length;\n          const id = (_b2 = block2 == null ? void 0 : block2._id) != null ? _b2 : (_a2 = block2 == null ? void 0 : block2._sys) == null ? void 0 : _a2.id;\n          if (typeof id !== \"string\" && (!typename || keysLength > 1)) {\n            if (isDev) {\n              console.warn(\n                `BaseHub RichText Error: make sure you send through the _id and the __typename for all custom blocks.\nReceived ${JSON.stringify(\n                  block2,\n                  null,\n                  2\n                )}.`\n              );\n            }\n            return false;\n          }\n          return id === // @ts-ignore\n          mark.attrs.targetId;\n        });\n        if (!block) {\n          props = {\n            children,\n            href: \"#\",\n            target: (_h = mark.attrs.target) != null ? _h : void 0,\n            internal: void 0\n          };\n          break;\n        }\n        props = {\n          children,\n          href: \"#\",\n          target: (_i = mark.attrs.target) != null ? _i : void 0,\n          internal: block\n        };\n      } else {\n        props = {\n          children,\n          href: mark.attrs.href,\n          target: (_j = mark.attrs.target) != null ? _j : void 0,\n          internal: void 0,\n          rel: ((_k = mark.attrs.target) == null ? void 0 : _k.toLowerCase()) === \"_blank\" ? \"noopener noreferrer\" : void 0\n        };\n      }\n      Handler = (_l = components == null ? void 0 : components.a) != null ? _l : disableDefaultComponents ? () => /* @__PURE__ */ jsx(Fragment, {}) : defaultHandlers.a;\n      break;\n    }\n    case \"basehub-inline-block\": {\n      const block = blocks == null ? void 0 : blocks.find((block2) => {\n        var _a2, _b2;\n        const typename = block2 == null ? void 0 : block2.__typename;\n        const keysLength = Object.keys(block2).length;\n        const id = (_b2 = block2 == null ? void 0 : block2._id) != null ? _b2 : (_a2 = block2 == null ? void 0 : block2._sys) == null ? void 0 : _a2.id;\n        if (typeof id !== \"string\" && (!typename || keysLength > 1)) {\n          if (isDev) {\n            console.warn(\n              `BaseHub RichText Error: make sure you send through the _id and the __typename for all custom blocks.\nReceived ${JSON.stringify(\n                block2,\n                null,\n                2\n              )}.`\n            );\n          }\n          return false;\n        }\n        return id === mark.attrs.id;\n      });\n      if (!block) {\n        break;\n      }\n      Handler = // @ts-ignore\n      (_m = components == null ? void 0 : components[(block == null ? void 0 : block.__typename) + SUFFIX_CUSTOM_MARK]) != null ? _m : () => /* @__PURE__ */ jsx(Fragment, { children });\n      props = __spreadProps(__spreadValues({}, block), { children });\n      break;\n    }\n    default:\n      Handler = (_n = components == null ? void 0 : components[mark.type]) != null ? _n : () => /* @__PURE__ */ jsx(Fragment, {});\n      props = __spreadProps(__spreadValues({}, mark.attrs), { children });\n      break;\n  }\n  if (!Handler) {\n    return /* @__PURE__ */ jsx(Fragment, {});\n  }\n  return /* @__PURE__ */ jsx(\n    Marks,\n    {\n      marks: marksClone,\n      components,\n      blocks,\n      disableDefaultComponents,\n      children: /* @__PURE__ */ jsx(Handler, __spreadValues({}, props))\n    }\n  );\n};\nfunction createRichTextWithDefaultComponents(defaultComponents) {\n  return (props) => {\n    return /* @__PURE__ */ jsx(\n      RichText,\n      __spreadProps(__spreadValues({}, props), {\n        components: __spreadValues(__spreadValues({}, defaultComponents), props.components)\n      })\n    );\n  };\n}\nvar TOC = (props) => {\n  var _a;\n  const slugger = new GithubSlugger();\n  const value = (_a = props.content) != null ? _a : props.children;\n  return /* @__PURE__ */ jsx(Fragment, { children: value == null ? void 0 : value.map((node, index) => {\n    return /* @__PURE__ */ jsx(\n      Node,\n      {\n        node,\n        components: props.components,\n        slugger,\n        disableDefaultComponents: props.disableDefaultComponents\n      },\n      index\n    );\n  }) });\n};\nexport {\n  RichText,\n  TOC,\n  createRichTextWithDefaultComponents\n};\n"], "names": [], "mappings": ";;;;;AAAA;AAMA,oCAAoC;AACpC;AAiBA,oCAAoC;AACpC;;;AAhBA,yCAAyC;AACzC,SAAS,oBAAoB,IAAI;IAC/B,IAAI;IACJ,IAAI,cAAc;IAClB,CAAC,KAAK,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,OAAO,CAAC,CAAC;QACzE,IAAI,MAAM,IAAI,KAAK,QAAQ;YACzB,eAAe,MAAM,IAAI;QAC3B;QACA,IAAI,aAAa,SAAS,MAAM,OAAO,EAAE;YACvC,eAAe,oBAAoB;QACrC;IACF;IACA,OAAO;AACT;;AAIA,IAAI,QAAQ,oDAAyB;AACrC,IAAI,qBAAqB;AACzB,IAAI,WAAW,CAAC;IACd,IAAI;IACJ,IAAI;IACJ,MAAM,aAAa,CAAC,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,MAAM,QAAQ;IACrE,IAAI,YAAY;QACd,IAAI,MAAM,OAAO,CAAC,aAAa;YAC7B,QAAQ;QACV,OAAO,IAAI,OAAO,eAAe,YAAY,cAAc,aAAa,cAAc,aAAa;QACnG,MAAM,OAAO,CAAC,WAAW,OAAO,GAAG;YACjC,QAAQ,WAAW,OAAO;QAC5B;IACF;IACA,MAAM,UAAU,IAAI,wMAAA,CAAA,UAAa;IACjC,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE;QAAE,UAAU,SAAS,OAAO,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM;YACzF,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,MACA;gBACE;gBACA,YAAY,MAAM,UAAU;gBAC5B,QAAQ,MAAM,MAAM;gBACpB;gBACA,0BAA0B,MAAM,wBAAwB;YAC1D,GACA;QAEJ;IAAG;AACL;AACA,IAAI,kBAAkB;IACpB,GAAG,CAAC;QACF,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,KAAK,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG;IACrD;IACA,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE;QAAS;IACzD,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE;QAAS;IACzD,WAAW,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE;QAAS;IACpE,IAAI,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE;QAAS;IAC3D,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE;QAAS;IACzD,KAAK,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE;QAAS;IAC7D,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE;QAAS;IAC/D,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QACjE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,OAAO;YAAE;QAAS;IACtF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QACjE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,OAAO;YAAE;QAAS;IACtF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QACjE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,OAAI,AAAD,EACxB,MACA,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,GAAG;YAAE,OAAO;gBAAE,SAAS;gBAAQ,YAAY;YAAW;QAAE,IAAI,KAAK,IAAI;YAC7H,UAAU;gBACR,IAAI,CAAC,oBAAoB,GAAG,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS;oBAAE,MAAM;oBAAY,gBAAgB,IAAI,CAAC,eAAe;gBAAC,KAAK;gBACvH;aACD;QACH;IAEJ;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,CAAC;QACH,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,QAAQ,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAW;QAClE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YAAE;QAAS;IACvF;IACA,IAAI,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAC;IACrC,KAAK,CAAC;QACJ,IAAI,KAAK,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,OAAO,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;YAAW;SAAM;QAC3E,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,OACA,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,OAAO;YACrD,KAAK,OAAO,OAAO,MAAM;QAC3B,IAAI,UAAU;YAAE,CAAC,eAAe,EAAE;QAAQ,IAAI,CAAC;IAEnD;IACA,OAAO,CAAC;QACN,IAAI,KAAK,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,OAAO,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YAAC;SAAU;QAC/D,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,SACA,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE;YAC5B,UAAU;QACZ,GAAG,OAAO,UAAU;YAAE,CAAC,eAAe,EAAE;QAAQ,IAAI,CAAC;IAEzD;IACA,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,cAAc;YAAE;QAAS;IAC3E,KAAK,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE,iBAAiB;YAAU,WAAW,CAAC,SAAS,EAAE,UAAU;YAAE;QAAS;IACrI,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS;YAAE;QAAS;IACjE,IAAI,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE;QAAS;IAC3D,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE,SAAS;YAAS,SAAS;YAAS;QAAS;IACjH,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE,SAAS;YAAS,SAAS;YAAS;QAAS;IACjH,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS;YAAE;QAAS;IACjE,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS;YAAE;QAAS;IACjE,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS;YAAE;QAAS;IACjE,IAAI,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,MAAM,CAAC;IACrC,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE;QAAS;AAC3D;AACA,IAAI,OAAO,CAAC,EACV,IAAI,EACJ,UAAU,EACV,MAAM,EACN,OAAO,EACP,wBAAwB,EACzB;IACC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IAChI,MAAM,WAAW,CAAC,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,WAAW;QACzE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,MACA;YACE,MAAM;YACN,QAAQ;YACR;YACA;YACA;YACA;QACF,GACA;IAEJ;IACA,IAAI;IACJ,IAAI;IACJ,OAAQ,KAAK,IAAI;QACf,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,CAAC;YACjK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,MAAM,cAAc;mBAAI,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,EAAE;aAAC;YAC5D,UAAU,CAAC,EAAE,UAAU,SAAS,EAAE,GAAK,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvD,OACA;oBACE,OAAO;oBACP;oBACA;oBACA;oBACA,UAAU;gBACZ;YAEF,QAAQ;gBAAE,UAAU,KAAK,IAAI;YAAC;YAC9B;QACF,KAAK;QACL,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE;gBACrB;YACF,GAAG,KAAK,IAAI,KAAK,aAAa;gBAAE,CAAC,oBAAoB,EAAE;YAAK,IAAI,CAAC;YACjE;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBACN;gBACA,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,OAAO,KAAK;YAC7E;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBACN;YACF;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBACN;gBACA,CAAC,oBAAoB,EAAE;gBACvB,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,OAAO,KAAK;YAC1F;YACA;QACF,KAAK;YACH,MAAM,aAAa,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,EAAE;YACzC,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,WAAW,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,eAAe,CAAC,WAAW;YACrL,MAAM,KAAK,QAAQ,IAAI,CAAC,oBAAoB;YAC5C,QAAQ;gBAAE;gBAAU;YAAG;YACvB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,UAAU;YACnL,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,GAAG,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,GAAG;YACrK,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,KAAK;YACrF,QAAQ;gBACN;gBACA,UAAU,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,QAAQ,KAAK,OAAO,KAAK;gBACjF;YACF;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,KAAK;YACzK,MAAM,oBAAoB,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAC1C,MACA;gBACE,MAAM;oBAAE,MAAM;oBAAa,SAAS,KAAK,OAAO;gBAAC;gBACjD,QAAQ;gBACR;gBACA;gBACA;gBACA;YACF;YAEF,QAAQ;gBAAE,UAAU;YAAkB;YACtC;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBACN;gBACA,SAAS,KAAK,KAAK,CAAC,OAAO;gBAC3B,SAAS,KAAK,KAAK,CAAC,OAAO;gBAC3B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,KAAK,OAAO,KAAK,KAAK;YAC3D;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBACN;gBACA,SAAS,KAAK,KAAK,CAAC,OAAO;gBAC3B,SAAS,KAAK,KAAK,CAAC,OAAO;gBAC3B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,KAAK,OAAO,KAAK,KAAK;YAC3D;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,KAAK;YACzK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,KAAK;YACzK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,GAAG,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,GAAG;YACrK,QAAQ;gBACN,KAAK,KAAK,KAAK,CAAC,GAAG;gBACnB,OAAO,KAAK,KAAK,CAAC,KAAK;gBACvB,QAAQ,KAAK,KAAK,CAAC,MAAM;gBACzB,KAAK,KAAK,KAAK,CAAC,GAAG;gBACnB,SAAS,KAAK,KAAK,CAAC,OAAO;YAC7B;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,KAAK;YACzK,QAAQ;gBACN;gBACA,KAAK,KAAK,KAAK,CAAC,GAAG;gBACnB,OAAO,KAAK,KAAK,CAAC,KAAK;gBACvB,QAAQ,KAAK,KAAK,CAAC,MAAM;gBACzB,SAAS,KAAK,KAAK,CAAC,OAAO;YAC7B;YACA;QACF,KAAK;YAAiB;gBACpB,MAAM,QAAQ,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,CAAC,CAAC;oBACnD,IAAI,KAAK;oBACT,MAAM,WAAW,UAAU,OAAO,KAAK,IAAI,OAAO,UAAU;oBAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM;oBAC7C,MAAM,MAAM,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,EAAE;oBAChJ,IAAI,OAAO,QAAQ,YAAY,CAAC,CAAC,YAAY,aAAa,CAAC,GAAG;wBAC5D,wCAAW;4BACT,QAAQ,IAAI,CACV,CAAC;SACN,EAAE,KAAK,SAAS,CACT,QACA,MACA,GACA,CAAC,CAAC;wBAER;wBACA,OAAO;oBACT;oBACA,OAAO,QAAQ,KAAK,KAAK,CAAC,EAAE;gBAC9B;gBACA,IAAI,CAAC,OAAO;oBACV;gBACF;gBACA,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,UAAU,CAAC,KAAK,OAAO,KAAK,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC;gBACzJ,QAAQ;gBACR;YACF;QACA;YACE,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,OAAO,KAAK,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC;YACzH,QAAQ,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,KAAK,KAAK,GAAG;gBAAE;YAAS;YACjE;IACJ;IACA,IAAI,CAAC,SAAS;QACZ,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC;IACxC;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG;AACzD;AACA,IAAI,QAAQ,CAAC,EACX,KAAK,EACL,QAAQ,EACR,UAAU,EACV,MAAM,EACN,wBAAwB,EACzB;IACC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IACxD,IAAI,CAAC,OACH,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE;QAAE;IAAS;IAClD,MAAM,aAAa;WAAI;KAAM;IAC7B,MAAM,OAAO,WAAW,GAAG;IAC3B,IAAI,CAAC,MACH,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE;QAAE;IAAS;IAClD,IAAI;IACJ,IAAI;IACJ,OAAQ,KAAK,IAAI;QACf,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,CAAC;YACjK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,SAAS,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,SAAS;YACjL,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,EAAE;YACnK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,CAAC;YACjK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,GAAG,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,GAAG;YACrK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,IAAI;YACvK,QAAQ;gBACN;YACF;YACA;QACF,KAAK;YACH,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,CAAC;YACjK,QAAQ;gBAAE;YAAS;YACnB;QACF,KAAK;YAAQ;gBACX,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,YAAY;oBAClC,MAAM,QAAQ,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,CAAC,CAAC;wBACnD,IAAI,KAAK;wBACT,MAAM,WAAW,UAAU,OAAO,KAAK,IAAI,OAAO,UAAU;wBAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM;wBAC7C,MAAM,KAAK,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,EAAE;wBAC/I,IAAI,OAAO,OAAO,YAAY,CAAC,CAAC,YAAY,aAAa,CAAC,GAAG;4BAC3D,wCAAW;gCACT,QAAQ,IAAI,CACV,CAAC;SACR,EAAE,KAAK,SAAS,CACP,QACA,MACA,GACA,CAAC,CAAC;4BAER;4BACA,OAAO;wBACT;wBACA,OAAO,OAAO,aAAa;wBAC3B,KAAK,KAAK,CAAC,QAAQ;oBACrB;oBACA,IAAI,CAAC,OAAO;wBACV,QAAQ;4BACN;4BACA,MAAM;4BACN,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,KAAK,KAAK;4BACrD,UAAU,KAAK;wBACjB;wBACA;oBACF;oBACA,QAAQ;wBACN;wBACA,MAAM;wBACN,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,KAAK,KAAK;wBACrD,UAAU;oBACZ;gBACF,OAAO;oBACL,QAAQ;wBACN;wBACA,MAAM,KAAK,KAAK,CAAC,IAAI;wBACrB,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,KAAK,KAAK;wBACrD,UAAU,KAAK;wBACf,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,GAAG,WAAW,EAAE,MAAM,WAAW,wBAAwB,KAAK;oBAClH;gBACF;gBACA,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,OAAO,KAAK,2BAA2B,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC,KAAK,gBAAgB,CAAC;gBACjK;YACF;QACA,KAAK;YAAwB;gBAC3B,MAAM,QAAQ,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,CAAC,CAAC;oBACnD,IAAI,KAAK;oBACT,MAAM,WAAW,UAAU,OAAO,KAAK,IAAI,OAAO,UAAU;oBAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM;oBAC7C,MAAM,KAAK,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,EAAE;oBAC/I,IAAI,OAAO,OAAO,YAAY,CAAC,CAAC,YAAY,aAAa,CAAC,GAAG;wBAC3D,wCAAW;4BACT,QAAQ,IAAI,CACV,CAAC;SACN,EAAE,KAAK,SAAS,CACT,QACA,MACA,GACA,CAAC,CAAC;wBAER;wBACA,OAAO;oBACT;oBACA,OAAO,OAAO,KAAK,KAAK,CAAC,EAAE;gBAC7B;gBACA,IAAI,CAAC,OAAO;oBACV;gBACF;gBACA,UACA,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,UAAU,IAAI,mBAAmB,KAAK,OAAO,KAAK,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE;wBAAE;oBAAS;gBAChL,QAAQ,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;oBAAE;gBAAS;gBAC5D;YACF;QACA;YACE,UAAU,CAAC,KAAK,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,OAAO,KAAK,IAAM,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC;YACzH,QAAQ,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,KAAK,KAAK,GAAG;gBAAE;YAAS;YACjE;IACJ;IACA,IAAI,CAAC,SAAS;QACZ,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE,CAAC;IACxC;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,OACA;QACE,OAAO;QACP;QACA;QACA;QACA,UAAU,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,SAAS,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG;IAC5D;AAEJ;AACA,SAAS,oCAAoC,iBAAiB;IAC5D,OAAO,CAAC;QACN,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,UACA,CAAA,GAAA,6PAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,QAAQ;YACvC,YAAY,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,oBAAoB,MAAM,UAAU;QACpF;IAEJ;AACF;AACA,IAAI,MAAM,CAAC;IACT,IAAI;IACJ,MAAM,UAAU,IAAI,wMAAA,CAAA,UAAa;IACjC,MAAM,QAAQ,CAAC,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,MAAM,QAAQ;IAChE,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EAAE,sUAAA,CAAA,WAAQ,EAAE;QAAE,UAAU,SAAS,OAAO,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM;YACzF,OAAO,aAAa,GAAG,CAAA,GAAA,sUAAA,CAAA,MAAG,AAAD,EACvB,MACA;gBACE;gBACA,YAAY,MAAM,UAAU;gBAC5B;gBACA,0BAA0B,MAAM,wBAAwB;YAC1D,GACA;QAEJ;IAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/github-slugger%402.0.0/node_modules/github-slugger/regex.js"], "sourcesContent": ["// This module is generated by `script/`.\n/* eslint-disable no-control-regex, no-misleading-character-class, no-useless-escape */\nexport const regex = /[\\0-\\x1F!-,\\.\\/:-@\\[-\\^`\\{-\\xA9\\xAB-\\xB4\\xB6-\\xB9\\xBB-\\xBF\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0378\\u0379\\u037E\\u0380-\\u0385\\u0387\\u038B\\u038D\\u03A2\\u03F6\\u0482\\u0530\\u0557\\u0558\\u055A-\\u055F\\u0589-\\u0590\\u05BE\\u05C0\\u05C3\\u05C6\\u05C8-\\u05CF\\u05EB-\\u05EE\\u05F3-\\u060F\\u061B-\\u061F\\u066A-\\u066D\\u06D4\\u06DD\\u06DE\\u06E9\\u06FD\\u06FE\\u0700-\\u070F\\u074B\\u074C\\u07B2-\\u07BF\\u07F6-\\u07F9\\u07FB\\u07FC\\u07FE\\u07FF\\u082E-\\u083F\\u085C-\\u085F\\u086B-\\u089F\\u08B5\\u08C8-\\u08D2\\u08E2\\u0964\\u0965\\u0970\\u0984\\u098D\\u098E\\u0991\\u0992\\u09A9\\u09B1\\u09B3-\\u09B5\\u09BA\\u09BB\\u09C5\\u09C6\\u09C9\\u09CA\\u09CF-\\u09D6\\u09D8-\\u09DB\\u09DE\\u09E4\\u09E5\\u09F2-\\u09FB\\u09FD\\u09FF\\u0A00\\u0A04\\u0A0B-\\u0A0E\\u0A11\\u0A12\\u0A29\\u0A31\\u0A34\\u0A37\\u0A3A\\u0A3B\\u0A3D\\u0A43-\\u0A46\\u0A49\\u0A4A\\u0A4E-\\u0A50\\u0A52-\\u0A58\\u0A5D\\u0A5F-\\u0A65\\u0A76-\\u0A80\\u0A84\\u0A8E\\u0A92\\u0AA9\\u0AB1\\u0AB4\\u0ABA\\u0ABB\\u0AC6\\u0ACA\\u0ACE\\u0ACF\\u0AD1-\\u0ADF\\u0AE4\\u0AE5\\u0AF0-\\u0AF8\\u0B00\\u0B04\\u0B0D\\u0B0E\\u0B11\\u0B12\\u0B29\\u0B31\\u0B34\\u0B3A\\u0B3B\\u0B45\\u0B46\\u0B49\\u0B4A\\u0B4E-\\u0B54\\u0B58-\\u0B5B\\u0B5E\\u0B64\\u0B65\\u0B70\\u0B72-\\u0B81\\u0B84\\u0B8B-\\u0B8D\\u0B91\\u0B96-\\u0B98\\u0B9B\\u0B9D\\u0BA0-\\u0BA2\\u0BA5-\\u0BA7\\u0BAB-\\u0BAD\\u0BBA-\\u0BBD\\u0BC3-\\u0BC5\\u0BC9\\u0BCE\\u0BCF\\u0BD1-\\u0BD6\\u0BD8-\\u0BE5\\u0BF0-\\u0BFF\\u0C0D\\u0C11\\u0C29\\u0C3A-\\u0C3C\\u0C45\\u0C49\\u0C4E-\\u0C54\\u0C57\\u0C5B-\\u0C5F\\u0C64\\u0C65\\u0C70-\\u0C7F\\u0C84\\u0C8D\\u0C91\\u0CA9\\u0CB4\\u0CBA\\u0CBB\\u0CC5\\u0CC9\\u0CCE-\\u0CD4\\u0CD7-\\u0CDD\\u0CDF\\u0CE4\\u0CE5\\u0CF0\\u0CF3-\\u0CFF\\u0D0D\\u0D11\\u0D45\\u0D49\\u0D4F-\\u0D53\\u0D58-\\u0D5E\\u0D64\\u0D65\\u0D70-\\u0D79\\u0D80\\u0D84\\u0D97-\\u0D99\\u0DB2\\u0DBC\\u0DBE\\u0DBF\\u0DC7-\\u0DC9\\u0DCB-\\u0DCE\\u0DD5\\u0DD7\\u0DE0-\\u0DE5\\u0DF0\\u0DF1\\u0DF4-\\u0E00\\u0E3B-\\u0E3F\\u0E4F\\u0E5A-\\u0E80\\u0E83\\u0E85\\u0E8B\\u0EA4\\u0EA6\\u0EBE\\u0EBF\\u0EC5\\u0EC7\\u0ECE\\u0ECF\\u0EDA\\u0EDB\\u0EE0-\\u0EFF\\u0F01-\\u0F17\\u0F1A-\\u0F1F\\u0F2A-\\u0F34\\u0F36\\u0F38\\u0F3A-\\u0F3D\\u0F48\\u0F6D-\\u0F70\\u0F85\\u0F98\\u0FBD-\\u0FC5\\u0FC7-\\u0FFF\\u104A-\\u104F\\u109E\\u109F\\u10C6\\u10C8-\\u10CC\\u10CE\\u10CF\\u10FB\\u1249\\u124E\\u124F\\u1257\\u1259\\u125E\\u125F\\u1289\\u128E\\u128F\\u12B1\\u12B6\\u12B7\\u12BF\\u12C1\\u12C6\\u12C7\\u12D7\\u1311\\u1316\\u1317\\u135B\\u135C\\u1360-\\u137F\\u1390-\\u139F\\u13F6\\u13F7\\u13FE-\\u1400\\u166D\\u166E\\u1680\\u169B-\\u169F\\u16EB-\\u16ED\\u16F9-\\u16FF\\u170D\\u1715-\\u171F\\u1735-\\u173F\\u1754-\\u175F\\u176D\\u1771\\u1774-\\u177F\\u17D4-\\u17D6\\u17D8-\\u17DB\\u17DE\\u17DF\\u17EA-\\u180A\\u180E\\u180F\\u181A-\\u181F\\u1879-\\u187F\\u18AB-\\u18AF\\u18F6-\\u18FF\\u191F\\u192C-\\u192F\\u193C-\\u1945\\u196E\\u196F\\u1975-\\u197F\\u19AC-\\u19AF\\u19CA-\\u19CF\\u19DA-\\u19FF\\u1A1C-\\u1A1F\\u1A5F\\u1A7D\\u1A7E\\u1A8A-\\u1A8F\\u1A9A-\\u1AA6\\u1AA8-\\u1AAF\\u1AC1-\\u1AFF\\u1B4C-\\u1B4F\\u1B5A-\\u1B6A\\u1B74-\\u1B7F\\u1BF4-\\u1BFF\\u1C38-\\u1C3F\\u1C4A-\\u1C4C\\u1C7E\\u1C7F\\u1C89-\\u1C8F\\u1CBB\\u1CBC\\u1CC0-\\u1CCF\\u1CD3\\u1CFB-\\u1CFF\\u1DFA\\u1F16\\u1F17\\u1F1E\\u1F1F\\u1F46\\u1F47\\u1F4E\\u1F4F\\u1F58\\u1F5A\\u1F5C\\u1F5E\\u1F7E\\u1F7F\\u1FB5\\u1FBD\\u1FBF-\\u1FC1\\u1FC5\\u1FCD-\\u1FCF\\u1FD4\\u1FD5\\u1FDC-\\u1FDF\\u1FED-\\u1FF1\\u1FF5\\u1FFD-\\u203E\\u2041-\\u2053\\u2055-\\u2070\\u2072-\\u207E\\u2080-\\u208F\\u209D-\\u20CF\\u20F1-\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F-\\u215F\\u2189-\\u24B5\\u24EA-\\u2BFF\\u2C2F\\u2C5F\\u2CE5-\\u2CEA\\u2CF4-\\u2CFF\\u2D26\\u2D28-\\u2D2C\\u2D2E\\u2D2F\\u2D68-\\u2D6E\\u2D70-\\u2D7E\\u2D97-\\u2D9F\\u2DA7\\u2DAF\\u2DB7\\u2DBF\\u2DC7\\u2DCF\\u2DD7\\u2DDF\\u2E00-\\u2E2E\\u2E30-\\u3004\\u3008-\\u3020\\u3030\\u3036\\u3037\\u303D-\\u3040\\u3097\\u3098\\u309B\\u309C\\u30A0\\u30FB\\u3100-\\u3104\\u3130\\u318F-\\u319F\\u31C0-\\u31EF\\u3200-\\u33FF\\u4DC0-\\u4DFF\\u9FFD-\\u9FFF\\uA48D-\\uA4CF\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA62C-\\uA63F\\uA673\\uA67E\\uA6F2-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA7C0\\uA7C1\\uA7CB-\\uA7F4\\uA828-\\uA82B\\uA82D-\\uA83F\\uA874-\\uA87F\\uA8C6-\\uA8CF\\uA8DA-\\uA8DF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA954-\\uA95F\\uA97D-\\uA97F\\uA9C1-\\uA9CE\\uA9DA-\\uA9DF\\uA9FF\\uAA37-\\uAA3F\\uAA4E\\uAA4F\\uAA5A-\\uAA5F\\uAA77-\\uAA79\\uAAC3-\\uAADA\\uAADE\\uAADF\\uAAF0\\uAAF1\\uAAF7-\\uAB00\\uAB07\\uAB08\\uAB0F\\uAB10\\uAB17-\\uAB1F\\uAB27\\uAB2F\\uAB5B\\uAB6A-\\uAB6F\\uABEB\\uABEE\\uABEF\\uABFA-\\uABFF\\uD7A4-\\uD7AF\\uD7C7-\\uD7CA\\uD7FC-\\uD7FF\\uE000-\\uF8FF\\uFA6E\\uFA6F\\uFADA-\\uFAFF\\uFB07-\\uFB12\\uFB18-\\uFB1C\\uFB29\\uFB37\\uFB3D\\uFB3F\\uFB42\\uFB45\\uFBB2-\\uFBD2\\uFD3E-\\uFD4F\\uFD90\\uFD91\\uFDC8-\\uFDEF\\uFDFC-\\uFDFF\\uFE10-\\uFE1F\\uFE30-\\uFE32\\uFE35-\\uFE4C\\uFE50-\\uFE6F\\uFE75\\uFEFD-\\uFF0F\\uFF1A-\\uFF20\\uFF3B-\\uFF3E\\uFF40\\uFF5B-\\uFF65\\uFFBF-\\uFFC1\\uFFC8\\uFFC9\\uFFD0\\uFFD1\\uFFD8\\uFFD9\\uFFDD-\\uFFFF]|\\uD800[\\uDC0C\\uDC27\\uDC3B\\uDC3E\\uDC4E\\uDC4F\\uDC5E-\\uDC7F\\uDCFB-\\uDD3F\\uDD75-\\uDDFC\\uDDFE-\\uDE7F\\uDE9D-\\uDE9F\\uDED1-\\uDEDF\\uDEE1-\\uDEFF\\uDF20-\\uDF2C\\uDF4B-\\uDF4F\\uDF7B-\\uDF7F\\uDF9E\\uDF9F\\uDFC4-\\uDFC7\\uDFD0\\uDFD6-\\uDFFF]|\\uD801[\\uDC9E\\uDC9F\\uDCAA-\\uDCAF\\uDCD4-\\uDCD7\\uDCFC-\\uDCFF\\uDD28-\\uDD2F\\uDD64-\\uDDFF\\uDF37-\\uDF3F\\uDF56-\\uDF5F\\uDF68-\\uDFFF]|\\uD802[\\uDC06\\uDC07\\uDC09\\uDC36\\uDC39-\\uDC3B\\uDC3D\\uDC3E\\uDC56-\\uDC5F\\uDC77-\\uDC7F\\uDC9F-\\uDCDF\\uDCF3\\uDCF6-\\uDCFF\\uDD16-\\uDD1F\\uDD3A-\\uDD7F\\uDDB8-\\uDDBD\\uDDC0-\\uDDFF\\uDE04\\uDE07-\\uDE0B\\uDE14\\uDE18\\uDE36\\uDE37\\uDE3B-\\uDE3E\\uDE40-\\uDE5F\\uDE7D-\\uDE7F\\uDE9D-\\uDEBF\\uDEC8\\uDEE7-\\uDEFF\\uDF36-\\uDF3F\\uDF56-\\uDF5F\\uDF73-\\uDF7F\\uDF92-\\uDFFF]|\\uD803[\\uDC49-\\uDC7F\\uDCB3-\\uDCBF\\uDCF3-\\uDCFF\\uDD28-\\uDD2F\\uDD3A-\\uDE7F\\uDEAA\\uDEAD-\\uDEAF\\uDEB2-\\uDEFF\\uDF1D-\\uDF26\\uDF28-\\uDF2F\\uDF51-\\uDFAF\\uDFC5-\\uDFDF\\uDFF7-\\uDFFF]|\\uD804[\\uDC47-\\uDC65\\uDC70-\\uDC7E\\uDCBB-\\uDCCF\\uDCE9-\\uDCEF\\uDCFA-\\uDCFF\\uDD35\\uDD40-\\uDD43\\uDD48-\\uDD4F\\uDD74\\uDD75\\uDD77-\\uDD7F\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDFF\\uDE12\\uDE38-\\uDE3D\\uDE3F-\\uDE7F\\uDE87\\uDE89\\uDE8E\\uDE9E\\uDEA9-\\uDEAF\\uDEEB-\\uDEEF\\uDEFA-\\uDEFF\\uDF04\\uDF0D\\uDF0E\\uDF11\\uDF12\\uDF29\\uDF31\\uDF34\\uDF3A\\uDF45\\uDF46\\uDF49\\uDF4A\\uDF4E\\uDF4F\\uDF51-\\uDF56\\uDF58-\\uDF5C\\uDF64\\uDF65\\uDF6D-\\uDF6F\\uDF75-\\uDFFF]|\\uD805[\\uDC4B-\\uDC4F\\uDC5A-\\uDC5D\\uDC62-\\uDC7F\\uDCC6\\uDCC8-\\uDCCF\\uDCDA-\\uDD7F\\uDDB6\\uDDB7\\uDDC1-\\uDDD7\\uDDDE-\\uDDFF\\uDE41-\\uDE43\\uDE45-\\uDE4F\\uDE5A-\\uDE7F\\uDEB9-\\uDEBF\\uDECA-\\uDEFF\\uDF1B\\uDF1C\\uDF2C-\\uDF2F\\uDF3A-\\uDFFF]|\\uD806[\\uDC3B-\\uDC9F\\uDCEA-\\uDCFE\\uDD07\\uDD08\\uDD0A\\uDD0B\\uDD14\\uDD17\\uDD36\\uDD39\\uDD3A\\uDD44-\\uDD4F\\uDD5A-\\uDD9F\\uDDA8\\uDDA9\\uDDD8\\uDDD9\\uDDE2\\uDDE5-\\uDDFF\\uDE3F-\\uDE46\\uDE48-\\uDE4F\\uDE9A-\\uDE9C\\uDE9E-\\uDEBF\\uDEF9-\\uDFFF]|\\uD807[\\uDC09\\uDC37\\uDC41-\\uDC4F\\uDC5A-\\uDC71\\uDC90\\uDC91\\uDCA8\\uDCB7-\\uDCFF\\uDD07\\uDD0A\\uDD37-\\uDD39\\uDD3B\\uDD3E\\uDD48-\\uDD4F\\uDD5A-\\uDD5F\\uDD66\\uDD69\\uDD8F\\uDD92\\uDD99-\\uDD9F\\uDDAA-\\uDEDF\\uDEF7-\\uDFAF\\uDFB1-\\uDFFF]|\\uD808[\\uDF9A-\\uDFFF]|\\uD809[\\uDC6F-\\uDC7F\\uDD44-\\uDFFF]|[\\uD80A\\uD80B\\uD80E-\\uD810\\uD812-\\uD819\\uD824-\\uD82B\\uD82D\\uD82E\\uD830-\\uD833\\uD837\\uD839\\uD83D\\uD83F\\uD87B-\\uD87D\\uD87F\\uD885-\\uDB3F\\uDB41-\\uDBFF][\\uDC00-\\uDFFF]|\\uD80D[\\uDC2F-\\uDFFF]|\\uD811[\\uDE47-\\uDFFF]|\\uD81A[\\uDE39-\\uDE3F\\uDE5F\\uDE6A-\\uDECF\\uDEEE\\uDEEF\\uDEF5-\\uDEFF\\uDF37-\\uDF3F\\uDF44-\\uDF4F\\uDF5A-\\uDF62\\uDF78-\\uDF7C\\uDF90-\\uDFFF]|\\uD81B[\\uDC00-\\uDE3F\\uDE80-\\uDEFF\\uDF4B-\\uDF4E\\uDF88-\\uDF8E\\uDFA0-\\uDFDF\\uDFE2\\uDFE5-\\uDFEF\\uDFF2-\\uDFFF]|\\uD821[\\uDFF8-\\uDFFF]|\\uD823[\\uDCD6-\\uDCFF\\uDD09-\\uDFFF]|\\uD82C[\\uDD1F-\\uDD4F\\uDD53-\\uDD63\\uDD68-\\uDD6F\\uDEFC-\\uDFFF]|\\uD82F[\\uDC6B-\\uDC6F\\uDC7D-\\uDC7F\\uDC89-\\uDC8F\\uDC9A-\\uDC9C\\uDC9F-\\uDFFF]|\\uD834[\\uDC00-\\uDD64\\uDD6A-\\uDD6C\\uDD73-\\uDD7A\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDE41\\uDE45-\\uDFFF]|\\uD835[\\uDC55\\uDC9D\\uDCA0\\uDCA1\\uDCA3\\uDCA4\\uDCA7\\uDCA8\\uDCAD\\uDCBA\\uDCBC\\uDCC4\\uDD06\\uDD0B\\uDD0C\\uDD15\\uDD1D\\uDD3A\\uDD3F\\uDD45\\uDD47-\\uDD49\\uDD51\\uDEA6\\uDEA7\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3\\uDFCC\\uDFCD]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85-\\uDE9A\\uDEA0\\uDEB0-\\uDFFF]|\\uD838[\\uDC07\\uDC19\\uDC1A\\uDC22\\uDC25\\uDC2B-\\uDCFF\\uDD2D-\\uDD2F\\uDD3E\\uDD3F\\uDD4A-\\uDD4D\\uDD4F-\\uDEBF\\uDEFA-\\uDFFF]|\\uD83A[\\uDCC5-\\uDCCF\\uDCD7-\\uDCFF\\uDD4C-\\uDD4F\\uDD5A-\\uDFFF]|\\uD83B[\\uDC00-\\uDDFF\\uDE04\\uDE20\\uDE23\\uDE25\\uDE26\\uDE28\\uDE33\\uDE38\\uDE3A\\uDE3C-\\uDE41\\uDE43-\\uDE46\\uDE48\\uDE4A\\uDE4C\\uDE50\\uDE53\\uDE55\\uDE56\\uDE58\\uDE5A\\uDE5C\\uDE5E\\uDE60\\uDE63\\uDE65\\uDE66\\uDE6B\\uDE73\\uDE78\\uDE7D\\uDE7F\\uDE8A\\uDE9C-\\uDEA0\\uDEA4\\uDEAA\\uDEBC-\\uDFFF]|\\uD83C[\\uDC00-\\uDD2F\\uDD4A-\\uDD4F\\uDD6A-\\uDD6F\\uDD8A-\\uDFFF]|\\uD83E[\\uDC00-\\uDFEF\\uDFFA-\\uDFFF]|\\uD869[\\uDEDE-\\uDEFF]|\\uD86D[\\uDF35-\\uDF3F]|\\uD86E[\\uDC1E\\uDC1F]|\\uD873[\\uDEA2-\\uDEAF]|\\uD87A[\\uDFE1-\\uDFFF]|\\uD87E[\\uDE1E-\\uDFFF]|\\uD884[\\uDF4B-\\uDFFF]|\\uDB40[\\uDC00-\\uDCFF\\uDDF0-\\uDFFF]/g\n"], "names": [], "mappings": "AAAA,yCAAyC;AACzC,qFAAqF;;;AAC9E,MAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/github-slugger%402.0.0/node_modules/github-slugger/index.js"], "sourcesContent": ["import { regex } from './regex.js'\n\nconst own = Object.hasOwnProperty\n\n/**\n * Slugger.\n */\nexport default class BananaSlug {\n  /**\n   * Create a new slug class.\n   */\n  constructor () {\n    /** @type {Record<string, number>} */\n    // eslint-disable-next-line no-unused-expressions\n    this.occurrences\n\n    this.reset()\n  }\n\n  /**\n   * Generate a unique slug.\n  *\n  * Tracks previously generated slugs: repeated calls with the same value\n  * will result in different slugs.\n  * Use the `slug` function to get same slugs.\n   *\n   * @param  {string} value\n   *   String of text to slugify\n   * @param  {boolean} [maintainCase=false]\n   *   Keep the current case, otherwise make all lowercase\n   * @return {string}\n   *   A unique slug string\n   */\n  slug (value, maintainCase) {\n    const self = this\n    let result = slug(value, maintainCase === true)\n    const originalSlug = result\n\n    while (own.call(self.occurrences, result)) {\n      self.occurrences[originalSlug]++\n      result = originalSlug + '-' + self.occurrences[originalSlug]\n    }\n\n    self.occurrences[result] = 0\n\n    return result\n  }\n\n  /**\n   * Reset - Forget all previous slugs\n   *\n   * @return void\n   */\n  reset () {\n    this.occurrences = Object.create(null)\n  }\n}\n\n/**\n * Generate a slug.\n *\n * Does not track previously generated slugs: repeated calls with the same value\n * will result in the exact same slug.\n * Use the `GithubSlugger` class to get unique slugs.\n *\n * @param  {string} value\n *   String of text to slugify\n * @param  {boolean} [maintainCase=false]\n *   Keep the current case, otherwise make all lowercase\n * @return {string}\n *   A unique slug string\n */\nexport function slug (value, maintainCase) {\n  if (typeof value !== 'string') return ''\n  if (!maintainCase) value = value.toLowerCase()\n  return value.replace(regex, '').replace(/ /g, '-')\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,MAAM,OAAO,cAAc;AAKlB,MAAM;IACnB;;GAEC,GACD,aAAe;QACb,mCAAmC,GACnC,iDAAiD;QACjD,IAAI,CAAC,WAAW;QAEhB,IAAI,CAAC,KAAK;IACZ;IAEA;;;;;;;;;;;;;GAaC,GACD,KAAM,KAAK,EAAE,YAAY,EAAE;QACzB,MAAM,OAAO,IAAI;QACjB,IAAI,SAAS,KAAK,OAAO,iBAAiB;QAC1C,MAAM,eAAe;QAErB,MAAO,IAAI,IAAI,CAAC,KAAK,WAAW,EAAE,QAAS;YACzC,KAAK,WAAW,CAAC,aAAa;YAC9B,SAAS,eAAe,MAAM,KAAK,WAAW,CAAC,aAAa;QAC9D;QAEA,KAAK,WAAW,CAAC,OAAO,GAAG;QAE3B,OAAO;IACT;IAEA;;;;GAIC,GACD,QAAS;QACP,IAAI,CAAC,WAAW,GAAG,OAAO,MAAM,CAAC;IACnC;AACF;AAgBO,SAAS,KAAM,KAAK,EAAE,YAAY;IACvC,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,IAAI,CAAC,cAAc,QAAQ,MAAM,WAAW;IAC5C,OAAO,MAAM,OAAO,CAAC,wMAAA,CAAA,QAAK,EAAE,IAAI,OAAO,CAAC,MAAM;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/lodash.merge%404.6.2/node_modules/lodash.merge/index.js"], "sourcesContent": ["/**\n * Lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n    HOT_SPAN = 16;\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    asyncTag = '[object AsyncFunction]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    nullTag = '[object Null]',\n    objectTag = '[object Object]',\n    proxyTag = '[object Proxy]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    undefinedTag = '[object Undefined]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Used to identify `toStringTag` values of typed arrays. */\nvar typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] =\ntypedArrayTags[int8Tag] = typedArrayTags[int16Tag] =\ntypedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =\ntypedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =\ntypedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] =\ntypedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =\ntypedArrayTags[dataViewTag] = typedArrayTags[dateTag] =\ntypedArrayTags[errorTag] = typedArrayTags[funcTag] =\ntypedArrayTags[mapTag] = typedArrayTags[numberTag] =\ntypedArrayTags[objectTag] = typedArrayTags[regexpTag] =\ntypedArrayTags[setTag] = typedArrayTags[stringTag] =\ntypedArrayTags[weakMapTag] = false;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = (function() {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}());\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    Symbol = root.Symbol,\n    Uint8Array = root.Uint8Array,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined,\n    getPrototype = overArg(Object.getPrototypeOf, Object),\n    objectCreate = Object.create,\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\n    splice = arrayProto.splice,\n    symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,\n    nativeMax = Math.max,\n    nativeNow = Date.now;\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map'),\n    nativeCreate = getNative(Object, 'create');\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n\n  this.size = data.size;\n  return result;\n}\n\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray(value),\n      isArg = !isArr && isArguments(value),\n      isBuff = !isArr && !isArg && isBuffer(value),\n      isType = !isArr && !isArg && !isBuff && isTypedArray(value),\n      skipIndexes = isArr || isArg || isBuff || isType,\n      result = skipIndexes ? baseTimes(value.length, String) : [],\n      length = result.length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (\n           // Safari 9 has enumerable `arguments.length` in strict mode.\n           key == 'length' ||\n           // Node.js 0.10 has enumerable non-index properties on buffers.\n           (isBuff && (key == 'offset' || key == 'parent')) ||\n           // PhantomJS 2 has enumerable non-index properties on typed arrays.\n           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||\n           // Skip index properties.\n           isIndex(key, length)\n        ))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */\nfunction baseIsTypedArray(value) {\n  return isObjectLike(value) &&\n    isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function(func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\n/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function(object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n\n  return !!length &&\n    (type == 'number' ||\n      (type != 'symbol' && reIsUint.test(value))) &&\n        (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction nativeKeysIn(object) {\n  var result = [];\n  if (object != null) {\n    for (var key in Object(object)) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\n\n/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\n\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\nfunction shortOut(func) {\n  var count = 0,\n      lastCalled = 0;\n\n  return function() {\n    var stamp = nativeNow(),\n        remaining = HOT_SPAN - (stamp - lastCalled);\n\n    lastCalled = stamp;\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n    return func.apply(undefined, arguments);\n  };\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor == 'function' && Ctor instanceof Ctor &&\n    funcToString.call(Ctor) == objectCtorString;\n}\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */\nfunction toPlainObject(value) {\n  return copyObject(value, keysIn(value));\n}\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n\n/**\n * This method is like `_.assign` except that it recursively merges own and\n * inherited enumerable string keyed properties of source objects into the\n * destination object. Source properties that resolve to `undefined` are\n * skipped if a destination value exists. Array and plain object properties\n * are merged recursively. Other objects and value types are overridden by\n * assignment. Source objects are applied from left to right. Subsequent\n * sources overwrite property assignments of previous sources.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {\n *   'a': [{ 'b': 2 }, { 'd': 4 }]\n * };\n *\n * var other = {\n *   'a': [{ 'c': 3 }, { 'e': 5 }]\n * };\n *\n * _.merge(object, other);\n * // => { 'a': [{ 'b': 2, 'c': 3 }, { 'd': 4, 'e': 5 }] }\n */\nvar merge = createAssigner(function(object, source, srcIndex) {\n  baseMerge(object, source, srcIndex);\n});\n\n/**\n * Creates a function that returns `value`.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Util\n * @param {*} value The value to return from the new function.\n * @returns {Function} Returns the new constant function.\n * @example\n *\n * var objects = _.times(2, _.constant({ 'a': 1 }));\n *\n * console.log(objects);\n * // => [{ 'a': 1 }, { 'a': 1 }]\n *\n * console.log(objects[0] === objects[1]);\n * // => true\n */\nfunction constant(value) {\n  return function() {\n    return value;\n  };\n}\n\n/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = merge;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,0DAA0D,GAC1D,IAAI,mBAAmB;AAEvB,kDAAkD,GAClD,IAAI,iBAAiB;AAErB,mFAAmF,GACnF,IAAI,YAAY,KACZ,WAAW;AAEf,uDAAuD,GACvD,IAAI,mBAAmB;AAEvB,yCAAyC,GACzC,IAAI,UAAU,sBACV,WAAW,kBACX,WAAW,0BACX,UAAU,oBACV,UAAU,iBACV,WAAW,kBACX,UAAU,qBACV,SAAS,8BACT,SAAS,gBACT,YAAY,mBACZ,UAAU,iBACV,YAAY,mBACZ,WAAW,kBACX,YAAY,mBACZ,SAAS,gBACT,YAAY,mBACZ,eAAe,sBACf,aAAa;AAEjB,IAAI,iBAAiB,wBACjB,cAAc,qBACd,aAAa,yBACb,aAAa,yBACb,UAAU,sBACV,WAAW,uBACX,WAAW,uBACX,WAAW,uBACX,kBAAkB,8BAClB,YAAY,wBACZ,YAAY;AAEhB;;;CAGC,GACD,IAAI,eAAe;AAEnB,+CAA+C,GAC/C,IAAI,eAAe;AAEnB,4CAA4C,GAC5C,IAAI,WAAW;AAEf,2DAA2D,GAC3D,IAAI,iBAAiB,CAAC;AACtB,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,GACvD,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,SAAS,GAClD,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,GACnD,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,UAAU,GAC3D,cAAc,CAAC,UAAU,GAAG;AAC5B,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,SAAS,GAClD,cAAc,CAAC,eAAe,GAAG,cAAc,CAAC,QAAQ,GACxD,cAAc,CAAC,YAAY,GAAG,cAAc,CAAC,QAAQ,GACrD,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,QAAQ,GAClD,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,UAAU,GAClD,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GACrD,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,UAAU,GAClD,cAAc,CAAC,WAAW,GAAG;AAE7B,gDAAgD,GAChD,IAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,MAAM,KAAK,UAAU;AAEpF,iCAAiC,GACjC,IAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,MAAM,KAAK,UAAU;AAE5E,8CAA8C,GAC9C,IAAI,OAAO,cAAc,YAAY,SAAS;AAE9C,oCAAoC,GACpC,IAAI,cAAc,8CAAkB,YAAY,WAAW,CAAC,QAAQ,QAAQ,IAAI;AAEhF,mCAAmC,GACnC,IAAI,aAAa,eAAe,8CAAiB,YAAY,UAAU,CAAC,OAAO,QAAQ,IAAI;AAE3F,4DAA4D,GAC5D,IAAI,gBAAgB,cAAc,WAAW,OAAO,KAAK;AAEzD,iDAAiD,GACjD,IAAI,cAAc,iBAAiB,WAAW,OAAO;AAErD,2CAA2C,GAC3C,IAAI,WAAY;IACd,IAAI;QACF,oCAAoC;QACpC,IAAI,QAAQ,cAAc,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,QAAQ,KAAK;QAEhF,IAAI,OAAO;YACT,OAAO;QACT;QAEA,qDAAqD;QACrD,OAAO,eAAe,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC;IACnE,EAAE,OAAO,GAAG,CAAC;AACf;AAEA,8BAA8B,GAC9B,IAAI,mBAAmB,YAAY,SAAS,YAAY;AAExD;;;;;;;;;CASC,GACD,SAAS,MAAM,IAAI,EAAE,OAAO,EAAE,IAAI;IAChC,OAAQ,KAAK,MAAM;QACjB,KAAK;YAAG,OAAO,KAAK,IAAI,CAAC;QACzB,KAAK;YAAG,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE;QACzC,KAAK;YAAG,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QAClD,KAAK;YAAG,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAC7D;IACA,OAAO,KAAK,KAAK,CAAC,SAAS;AAC7B;AAEA;;;;;;;;CAQC,GACD,SAAS,UAAU,CAAC,EAAE,QAAQ;IAC5B,IAAI,QAAQ,CAAC,GACT,SAAS,MAAM;IAEnB,MAAO,EAAE,QAAQ,EAAG;QAClB,MAAM,CAAC,MAAM,GAAG,SAAS;IAC3B;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,UAAU,IAAI;IACrB,OAAO,SAAS,KAAK;QACnB,OAAO,KAAK;IACd;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,MAAM,EAAE,GAAG;IAC3B,OAAO,UAAU,OAAO,YAAY,MAAM,CAAC,IAAI;AACjD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI,EAAE,SAAS;IAC9B,OAAO,SAAS,GAAG;QACjB,OAAO,KAAK,UAAU;IACxB;AACF;AAEA,yCAAyC,GACzC,IAAI,aAAa,MAAM,SAAS,EAC5B,YAAY,SAAS,SAAS,EAC9B,cAAc,OAAO,SAAS;AAElC,+CAA+C,GAC/C,IAAI,aAAa,IAAI,CAAC,qBAAqB;AAE3C,wDAAwD,GACxD,IAAI,eAAe,UAAU,QAAQ;AAErC,8CAA8C,GAC9C,IAAI,iBAAiB,YAAY,cAAc;AAE/C,mDAAmD,GACnD,IAAI,aAAc;IAChB,IAAI,MAAM,SAAS,IAAI,CAAC,cAAc,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,QAAQ,IAAI;IACrF,OAAO,MAAO,mBAAmB,MAAO;AAC1C;AAEA;;;;CAIC,GACD,IAAI,uBAAuB,YAAY,QAAQ;AAE/C,4CAA4C,GAC5C,IAAI,mBAAmB,aAAa,IAAI,CAAC;AAEzC,0CAA0C,GAC1C,IAAI,aAAa,OAAO,MACtB,aAAa,IAAI,CAAC,gBAAgB,OAAO,CAAC,cAAc,QACvD,OAAO,CAAC,0DAA0D,WAAW;AAGhF,+BAA+B,GAC/B,IAAI,SAAS,gBAAgB,KAAK,MAAM,GAAG,WACvC,SAAS,KAAK,MAAM,EACpB,aAAa,KAAK,UAAU,EAC5B,cAAc,SAAS,OAAO,WAAW,GAAG,WAC5C,eAAe,QAAQ,OAAO,cAAc,EAAE,SAC9C,eAAe,OAAO,MAAM,EAC5B,uBAAuB,YAAY,oBAAoB,EACvD,SAAS,WAAW,MAAM,EAC1B,iBAAiB,SAAS,OAAO,WAAW,GAAG;AAEnD,IAAI,iBAAkB;IACpB,IAAI;QACF,IAAI,OAAO,UAAU,QAAQ;QAC7B,KAAK,CAAC,GAAG,IAAI,CAAC;QACd,OAAO;IACT,EAAE,OAAO,GAAG,CAAC;AACf;AAEA,sFAAsF,GACtF,IAAI,iBAAiB,SAAS,OAAO,QAAQ,GAAG,WAC5C,YAAY,KAAK,GAAG,EACpB,YAAY,KAAK,GAAG;AAExB,8DAA8D,GAC9D,IAAI,MAAM,UAAU,MAAM,QACtB,eAAe,UAAU,QAAQ;AAErC;;;;;;;CAOC,GACD,IAAI,aAAc;IAChB,SAAS,UAAU;IACnB,OAAO,SAAS,KAAK;QACnB,IAAI,CAAC,SAAS,QAAQ;YACpB,OAAO,CAAC;QACV;QACA,IAAI,cAAc;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,GAAG;QACnB,IAAI,SAAS,IAAI;QACjB,OAAO,SAAS,GAAG;QACnB,OAAO;IACT;AACF;AAEA;;;;;;CAMC,GACD,SAAS,KAAK,OAAO;IACnB,IAAI,QAAQ,CAAC,GACT,SAAS,WAAW,OAAO,IAAI,QAAQ,MAAM;IAEjD,IAAI,CAAC,KAAK;IACV,MAAO,EAAE,QAAQ,OAAQ;QACvB,IAAI,QAAQ,OAAO,CAAC,MAAM;QAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;IAC7B;AACF;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG,eAAe,aAAa,QAAQ,CAAC;IACrD,IAAI,CAAC,IAAI,GAAG;AACd;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,GAAG;IACrB,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;IACvD,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;IAC1B,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,QAAQ,GAAG;IAClB,IAAI,OAAO,IAAI,CAAC,QAAQ;IACxB,IAAI,cAAc;QAChB,IAAI,SAAS,IAAI,CAAC,IAAI;QACtB,OAAO,WAAW,iBAAiB,YAAY;IACjD;IACA,OAAO,eAAe,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,IAAI,GAAG;AACtD;AAEA;;;;;;;;CAQC,GACD,SAAS,QAAQ,GAAG;IAClB,IAAI,OAAO,IAAI,CAAC,QAAQ;IACxB,OAAO,eAAgB,IAAI,CAAC,IAAI,KAAK,YAAa,eAAe,IAAI,CAAC,MAAM;AAC9E;AAEA;;;;;;;;;CASC,GACD,SAAS,QAAQ,GAAG,EAAE,KAAK;IACzB,IAAI,OAAO,IAAI,CAAC,QAAQ;IACxB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI;IACjC,IAAI,CAAC,IAAI,GAAG,AAAC,gBAAgB,UAAU,YAAa,iBAAiB;IACrE,OAAO,IAAI;AACb;AAEA,yBAAyB;AACzB,KAAK,SAAS,CAAC,KAAK,GAAG;AACvB,KAAK,SAAS,CAAC,SAAS,GAAG;AAC3B,KAAK,SAAS,CAAC,GAAG,GAAG;AACrB,KAAK,SAAS,CAAC,GAAG,GAAG;AACrB,KAAK,SAAS,CAAC,GAAG,GAAG;AAErB;;;;;;CAMC,GACD,SAAS,UAAU,OAAO;IACxB,IAAI,QAAQ,CAAC,GACT,SAAS,WAAW,OAAO,IAAI,QAAQ,MAAM;IAEjD,IAAI,CAAC,KAAK;IACV,MAAO,EAAE,QAAQ,OAAQ;QACvB,IAAI,QAAQ,OAAO,CAAC,MAAM;QAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;IAC7B;AACF;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,GAAG;IAC1B,IAAI,OAAO,IAAI,CAAC,QAAQ,EACpB,QAAQ,aAAa,MAAM;IAE/B,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IACA,IAAI,YAAY,KAAK,MAAM,GAAG;IAC9B,IAAI,SAAS,WAAW;QACtB,KAAK,GAAG;IACV,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,OAAO;IAC3B;IACA,EAAE,IAAI,CAAC,IAAI;IACX,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAa,GAAG;IACvB,IAAI,OAAO,IAAI,CAAC,QAAQ,EACpB,QAAQ,aAAa,MAAM;IAE/B,OAAO,QAAQ,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,EAAE;AAC/C;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAa,GAAG;IACvB,OAAO,aAAa,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC7C;AAEA;;;;;;;;;CASC,GACD,SAAS,aAAa,GAAG,EAAE,KAAK;IAC9B,IAAI,OAAO,IAAI,CAAC,QAAQ,EACpB,QAAQ,aAAa,MAAM;IAE/B,IAAI,QAAQ,GAAG;QACb,EAAE,IAAI,CAAC,IAAI;QACX,KAAK,IAAI,CAAC;YAAC;YAAK;SAAM;IACxB,OAAO;QACL,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;IACnB;IACA,OAAO,IAAI;AACb;AAEA,8BAA8B;AAC9B,UAAU,SAAS,CAAC,KAAK,GAAG;AAC5B,UAAU,SAAS,CAAC,SAAS,GAAG;AAChC,UAAU,SAAS,CAAC,GAAG,GAAG;AAC1B,UAAU,SAAS,CAAC,GAAG,GAAG;AAC1B,UAAU,SAAS,CAAC,GAAG,GAAG;AAE1B;;;;;;CAMC,GACD,SAAS,SAAS,OAAO;IACvB,IAAI,QAAQ,CAAC,GACT,SAAS,WAAW,OAAO,IAAI,QAAQ,MAAM;IAEjD,IAAI,CAAC,KAAK;IACV,MAAO,EAAE,QAAQ,OAAQ;QACvB,IAAI,QAAQ,OAAO,CAAC,MAAM;QAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;IAC7B;AACF;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,QAAQ,GAAG;QACd,QAAQ,IAAI;QACZ,OAAO,IAAI,CAAC,OAAO,SAAS;QAC5B,UAAU,IAAI;IAChB;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,GAAG;IACzB,IAAI,SAAS,WAAW,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;IAC7C,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;IAC1B,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,YAAY,GAAG;IACtB,OAAO,WAAW,IAAI,EAAE,KAAK,GAAG,CAAC;AACnC;AAEA;;;;;;;;CAQC,GACD,SAAS,YAAY,GAAG;IACtB,OAAO,WAAW,IAAI,EAAE,KAAK,GAAG,CAAC;AACnC;AAEA;;;;;;;;;CASC,GACD,SAAS,YAAY,GAAG,EAAE,KAAK;IAC7B,IAAI,OAAO,WAAW,IAAI,EAAE,MACxB,OAAO,KAAK,IAAI;IAEpB,KAAK,GAAG,CAAC,KAAK;IACd,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI;IACrC,OAAO,IAAI;AACb;AAEA,6BAA6B;AAC7B,SAAS,SAAS,CAAC,KAAK,GAAG;AAC3B,SAAS,SAAS,CAAC,SAAS,GAAG;AAC/B,SAAS,SAAS,CAAC,GAAG,GAAG;AACzB,SAAS,SAAS,CAAC,GAAG,GAAG;AACzB,SAAS,SAAS,CAAC,GAAG,GAAG;AAEzB;;;;;;CAMC,GACD,SAAS,MAAM,OAAO;IACpB,IAAI,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;IACzC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;AACvB;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA;;;;;;;;CAQC,GACD,SAAS,YAAY,GAAG;IACtB,IAAI,OAAO,IAAI,CAAC,QAAQ,EACpB,SAAS,IAAI,CAAC,SAAS,CAAC;IAE5B,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;IACrB,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC3B;AAEA;;;;;;;;CAQC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC3B;AAEA;;;;;;;;;CASC,GACD,SAAS,SAAS,GAAG,EAAE,KAAK;IAC1B,IAAI,OAAO,IAAI,CAAC,QAAQ;IACxB,IAAI,gBAAgB,WAAW;QAC7B,IAAI,QAAQ,KAAK,QAAQ;QACzB,IAAI,CAAC,OAAQ,MAAM,MAAM,GAAG,mBAAmB,GAAI;YACjD,MAAM,IAAI,CAAC;gBAAC;gBAAK;aAAM;YACvB,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,IAAI;YACvB,OAAO,IAAI;QACb;QACA,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,SAAS;IACtC;IACA,KAAK,GAAG,CAAC,KAAK;IACd,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;IACrB,OAAO,IAAI;AACb;AAEA,0BAA0B;AAC1B,MAAM,SAAS,CAAC,KAAK,GAAG;AACxB,MAAM,SAAS,CAAC,SAAS,GAAG;AAC5B,MAAM,SAAS,CAAC,GAAG,GAAG;AACtB,MAAM,SAAS,CAAC,GAAG,GAAG;AACtB,MAAM,SAAS,CAAC,GAAG,GAAG;AAEtB;;;;;;;CAOC,GACD,SAAS,cAAc,KAAK,EAAE,SAAS;IACrC,IAAI,QAAQ,QAAQ,QAChB,QAAQ,CAAC,SAAS,YAAY,QAC9B,SAAS,CAAC,SAAS,CAAC,SAAS,SAAS,QACtC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,aAAa,QACrD,cAAc,SAAS,SAAS,UAAU,QAC1C,SAAS,cAAc,UAAU,MAAM,MAAM,EAAE,UAAU,EAAE,EAC3D,SAAS,OAAO,MAAM;IAE1B,IAAK,IAAI,OAAO,MAAO;QACrB,IAAI,CAAC,aAAa,eAAe,IAAI,CAAC,OAAO,IAAI,KAC7C,CAAC,CAAC,eAAe,CACd,6DAA6D;QAC7D,OAAO,YAEN,UAAU,CAAC,OAAO,YAAY,OAAO,QAAQ,KAE7C,UAAU,CAAC,OAAO,YAAY,OAAO,gBAAgB,OAAO,YAAY,KACzE,yBAAyB;QACzB,QAAQ,KAAK,OAChB,CAAC,GAAG;YACN,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,iBAAiB,MAAM,EAAE,GAAG,EAAE,KAAK;IAC1C,IAAI,AAAC,UAAU,aAAa,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,UACxC,UAAU,aAAa,CAAC,CAAC,OAAO,MAAM,GAAI;QAC7C,gBAAgB,QAAQ,KAAK;IAC/B;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,YAAY,MAAM,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,WAAW,MAAM,CAAC,IAAI;IAC1B,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,QAAQ,QAAQ,GAAG,UAAU,MAAM,KACxD,UAAU,aAAa,CAAC,CAAC,OAAO,MAAM,GAAI;QAC7C,gBAAgB,QAAQ,KAAK;IAC/B;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK,EAAE,GAAG;IAC9B,IAAI,SAAS,MAAM,MAAM;IACzB,MAAO,SAAU;QACf,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,CAAC;AACV;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,MAAM,EAAE,GAAG,EAAE,KAAK;IACzC,IAAI,OAAO,eAAe,gBAAgB;QACxC,eAAe,QAAQ,KAAK;YAC1B,gBAAgB;YAChB,cAAc;YACd,SAAS;YACT,YAAY;QACd;IACF,OAAO;QACL,MAAM,CAAC,IAAI,GAAG;IAChB;AACF;AAEA;;;;;;;;;;CAUC,GACD,IAAI,UAAU;AAEd;;;;;;CAMC,GACD,SAAS,WAAW,KAAK;IACvB,IAAI,SAAS,MAAM;QACjB,OAAO,UAAU,YAAY,eAAe;IAC9C;IACA,OAAO,AAAC,kBAAkB,kBAAkB,OAAO,SAC/C,UAAU,SACV,eAAe;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,KAAK;IAC5B,OAAO,aAAa,UAAU,WAAW,UAAU;AACrD;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,SAAS,UAAU,SAAS,QAAQ;QACvC,OAAO;IACT;IACA,IAAI,UAAU,WAAW,SAAS,aAAa;IAC/C,OAAO,QAAQ,IAAI,CAAC,SAAS;AAC/B;AAEA;;;;;;CAMC,GACD,SAAS,iBAAiB,KAAK;IAC7B,OAAO,aAAa,UAClB,SAAS,MAAM,MAAM,KAAK,CAAC,CAAC,cAAc,CAAC,WAAW,OAAO;AACjE;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,MAAM;IACxB,IAAI,CAAC,SAAS,SAAS;QACrB,OAAO,aAAa;IACtB;IACA,IAAI,UAAU,YAAY,SACtB,SAAS,EAAE;IAEf,IAAK,IAAI,OAAO,OAAQ;QACtB,IAAI,CAAC,CAAC,OAAO,iBAAiB,CAAC,WAAW,CAAC,eAAe,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG;YAC7E,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,QAAQ;QACrB;IACF;IACA,QAAQ,QAAQ,SAAS,QAAQ,EAAE,GAAG;QACpC,SAAS,CAAC,QAAQ,IAAI,KAAK;QAC3B,IAAI,SAAS,WAAW;YACtB,cAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY;QACtE,OACK;YACH,IAAI,WAAW,aACX,WAAW,QAAQ,QAAQ,MAAM,UAAW,MAAM,IAAK,QAAQ,QAAQ,SACvE;YAEJ,IAAI,aAAa,WAAW;gBAC1B,WAAW;YACb;YACA,iBAAiB,QAAQ,KAAK;QAChC;IACF,GAAG;AACL;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,cAAc,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK;IAChF,IAAI,WAAW,QAAQ,QAAQ,MAC3B,WAAW,QAAQ,QAAQ,MAC3B,UAAU,MAAM,GAAG,CAAC;IAExB,IAAI,SAAS;QACX,iBAAiB,QAAQ,KAAK;QAC9B;IACF;IACA,IAAI,WAAW,aACX,WAAW,UAAU,UAAW,MAAM,IAAK,QAAQ,QAAQ,SAC3D;IAEJ,IAAI,WAAW,aAAa;IAE5B,IAAI,UAAU;QACZ,IAAI,QAAQ,QAAQ,WAChB,SAAS,CAAC,SAAS,SAAS,WAC5B,UAAU,CAAC,SAAS,CAAC,UAAU,aAAa;QAEhD,WAAW;QACX,IAAI,SAAS,UAAU,SAAS;YAC9B,IAAI,QAAQ,WAAW;gBACrB,WAAW;YACb,OACK,IAAI,kBAAkB,WAAW;gBACpC,WAAW,UAAU;YACvB,OACK,IAAI,QAAQ;gBACf,WAAW;gBACX,WAAW,YAAY,UAAU;YACnC,OACK,IAAI,SAAS;gBAChB,WAAW;gBACX,WAAW,gBAAgB,UAAU;YACvC,OACK;gBACH,WAAW,EAAE;YACf;QACF,OACK,IAAI,cAAc,aAAa,YAAY,WAAW;YACzD,WAAW;YACX,IAAI,YAAY,WAAW;gBACzB,WAAW,cAAc;YAC3B,OACK,IAAI,CAAC,SAAS,aAAa,WAAW,WAAW;gBACpD,WAAW,gBAAgB;YAC7B;QACF,OACK;YACH,WAAW;QACb;IACF;IACA,IAAI,UAAU;QACZ,2EAA2E;QAC3E,MAAM,GAAG,CAAC,UAAU;QACpB,UAAU,UAAU,UAAU,UAAU,YAAY;QACpD,KAAK,CAAC,SAAS,CAAC;IAClB;IACA,iBAAiB,QAAQ,KAAK;AAChC;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK;IAC3B,OAAO,YAAY,SAAS,MAAM,OAAO,WAAW,OAAO;AAC7D;AAEA;;;;;;;CAOC,GACD,IAAI,kBAAkB,CAAC,iBAAiB,WAAW,SAAS,IAAI,EAAE,MAAM;IACtE,OAAO,eAAe,MAAM,YAAY;QACtC,gBAAgB;QAChB,cAAc;QACd,SAAS,SAAS;QAClB,YAAY;IACd;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,YAAY,MAAM,EAAE,MAAM;IACjC,IAAI,QAAQ;QACV,OAAO,OAAO,KAAK;IACrB;IACA,IAAI,SAAS,OAAO,MAAM,EACtB,SAAS,cAAc,YAAY,UAAU,IAAI,OAAO,WAAW,CAAC;IAExE,OAAO,IAAI,CAAC;IACZ,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,iBAAiB,WAAW;IACnC,IAAI,SAAS,IAAI,YAAY,WAAW,CAAC,YAAY,UAAU;IAC/D,IAAI,WAAW,QAAQ,GAAG,CAAC,IAAI,WAAW;IAC1C,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,UAAU,EAAE,MAAM;IACzC,IAAI,SAAS,SAAS,iBAAiB,WAAW,MAAM,IAAI,WAAW,MAAM;IAC7E,OAAO,IAAI,WAAW,WAAW,CAAC,QAAQ,WAAW,UAAU,EAAE,WAAW,MAAM;AACpF;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,QAAQ,CAAC,GACT,SAAS,OAAO,MAAM;IAE1B,SAAS,CAAC,QAAQ,MAAM,OAAO;IAC/B,MAAO,EAAE,QAAQ,OAAQ;QACvB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;IAC9B;IACA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU;IACnD,IAAI,QAAQ,CAAC;IACb,UAAU,CAAC,SAAS,CAAC,CAAC;IAEtB,IAAI,QAAQ,CAAC,GACT,SAAS,MAAM,MAAM;IAEzB,MAAO,EAAE,QAAQ,OAAQ;QACvB,IAAI,MAAM,KAAK,CAAC,MAAM;QAEtB,IAAI,WAAW,aACX,WAAW,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,QAAQ,UAClD;QAEJ,IAAI,aAAa,WAAW;YAC1B,WAAW,MAAM,CAAC,IAAI;QACxB;QACA,IAAI,OAAO;YACT,gBAAgB,QAAQ,KAAK;QAC/B,OAAO;YACL,YAAY,QAAQ,KAAK;QAC3B;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,QAAQ;IAC9B,OAAO,SAAS,SAAS,MAAM,EAAE,OAAO;QACtC,IAAI,QAAQ,CAAC,GACT,SAAS,QAAQ,MAAM,EACvB,aAAa,SAAS,IAAI,OAAO,CAAC,SAAS,EAAE,GAAG,WAChD,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,GAAG;QAEtC,aAAa,AAAC,SAAS,MAAM,GAAG,KAAK,OAAO,cAAc,aACtD,CAAC,UAAU,UAAU,IACrB;QAEJ,IAAI,SAAS,eAAe,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ;YAC1D,aAAa,SAAS,IAAI,YAAY;YACtC,SAAS;QACX;QACA,SAAS,OAAO;QAChB,MAAO,EAAE,QAAQ,OAAQ;YACvB,IAAI,SAAS,OAAO,CAAC,MAAM;YAC3B,IAAI,QAAQ;gBACV,SAAS,QAAQ,QAAQ,OAAO;YAClC;QACF;QACA,OAAO;IACT;AACF;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,SAAS;IAC9B,OAAO,SAAS,MAAM,EAAE,QAAQ,EAAE,QAAQ;QACxC,IAAI,QAAQ,CAAC,GACT,WAAW,OAAO,SAClB,QAAQ,SAAS,SACjB,SAAS,MAAM,MAAM;QAEzB,MAAO,SAAU;YACf,IAAI,MAAM,KAAK,CAAC,YAAY,SAAS,EAAE,MAAM;YAC7C,IAAI,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,cAAc,OAAO;gBACpD;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,GAAG,EAAE,GAAG;IAC1B,IAAI,OAAO,IAAI,QAAQ;IACvB,OAAO,UAAU,OACb,IAAI,CAAC,OAAO,OAAO,WAAW,WAAW,OAAO,GAChD,KAAK,GAAG;AACd;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,MAAM,EAAE,GAAG;IAC5B,IAAI,QAAQ,SAAS,QAAQ;IAC7B,OAAO,aAAa,SAAS,QAAQ;AACvC;AAEA;;;;;;CAMC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,QAAQ,eAAe,IAAI,CAAC,OAAO,iBACnC,MAAM,KAAK,CAAC,eAAe;IAE/B,IAAI;QACF,KAAK,CAAC,eAAe,GAAG;QACxB,IAAI,WAAW;IACjB,EAAE,OAAO,GAAG,CAAC;IAEb,IAAI,SAAS,qBAAqB,IAAI,CAAC;IACvC,wCAAc;QACZ,IAAI,OAAO;YACT,KAAK,CAAC,eAAe,GAAG;QAC1B,OAAO;YACL,OAAO,KAAK,CAAC,eAAe;QAC9B;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,MAAM;IAC7B,OAAO,AAAC,OAAO,OAAO,WAAW,IAAI,cAAc,CAAC,YAAY,UAC5D,WAAW,aAAa,WACxB,CAAC;AACP;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC5B,IAAI,OAAO,OAAO;IAClB,SAAS,UAAU,OAAO,mBAAmB;IAE7C,OAAO,CAAC,CAAC,UACP,CAAC,QAAQ,YACN,QAAQ,YAAY,SAAS,IAAI,CAAC,MAAO,KACvC,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACjD;AAEA;;;;;;;;;CASC,GACD,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IAC1C,IAAI,CAAC,SAAS,SAAS;QACrB,OAAO;IACT;IACA,IAAI,OAAO,OAAO;IAClB,IAAI,QAAQ,WACH,YAAY,WAAW,QAAQ,OAAO,OAAO,MAAM,IACnD,QAAQ,YAAY,SAAS,QAChC;QACJ,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE;IAC3B;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,OAAO,OAAO;IAClB,OAAO,AAAC,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YACvE,UAAU,cACV,UAAU;AACjB;AAEA;;;;;;CAMC,GACD,SAAS,SAAS,IAAI;IACpB,OAAO,CAAC,CAAC,cAAe,cAAc;AACxC;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,KAAK;IACxB,IAAI,OAAO,SAAS,MAAM,WAAW,EACjC,QAAQ,AAAC,OAAO,QAAQ,cAAc,KAAK,SAAS,IAAK;IAE7D,OAAO,UAAU;AACnB;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAa,MAAM;IAC1B,IAAI,SAAS,EAAE;IACf,IAAI,UAAU,MAAM;QAClB,IAAK,IAAI,OAAO,OAAO,QAAS;YAC9B,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,qBAAqB,IAAI,CAAC;AACnC;AAEA;;;;;;;;CAQC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;IACtC,QAAQ,UAAU,UAAU,YAAa,KAAK,MAAM,GAAG,IAAK,OAAO;IACnE,OAAO;QACL,IAAI,OAAO,WACP,QAAQ,CAAC,GACT,SAAS,UAAU,KAAK,MAAM,GAAG,OAAO,IACxC,QAAQ,MAAM;QAElB,MAAO,EAAE,QAAQ,OAAQ;YACvB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,MAAM;QACpC;QACA,QAAQ,CAAC;QACT,IAAI,YAAY,MAAM,QAAQ;QAC9B,MAAO,EAAE,QAAQ,MAAO;YACtB,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAChC;QACA,SAAS,CAAC,MAAM,GAAG,UAAU;QAC7B,OAAO,MAAM,MAAM,IAAI,EAAE;IAC3B;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,MAAM,EAAE,GAAG;IAC1B,IAAI,QAAQ,iBAAiB,OAAO,MAAM,CAAC,IAAI,KAAK,YAAY;QAC9D;IACF;IAEA,IAAI,OAAO,aAAa;QACtB;IACF;IAEA,OAAO,MAAM,CAAC,IAAI;AACpB;AAEA;;;;;;;CAOC,GACD,IAAI,cAAc,SAAS;AAE3B;;;;;;;;CAQC,GACD,SAAS,SAAS,IAAI;IACpB,IAAI,QAAQ,GACR,aAAa;IAEjB,OAAO;QACL,IAAI,QAAQ,aACR,YAAY,WAAW,CAAC,QAAQ,UAAU;QAE9C,aAAa;QACb,IAAI,YAAY,GAAG;YACjB,IAAI,EAAE,SAAS,WAAW;gBACxB,OAAO,SAAS,CAAC,EAAE;YACrB;QACF,OAAO;YACL,QAAQ;QACV;QACA,OAAO,KAAK,KAAK,CAAC,WAAW;IAC/B;AACF;AAEA;;;;;;CAMC,GACD,SAAS,SAAS,IAAI;IACpB,IAAI,QAAQ,MAAM;QAChB,IAAI;YACF,OAAO,aAAa,IAAI,CAAC;QAC3B,EAAE,OAAO,GAAG,CAAC;QACb,IAAI;YACF,OAAQ,OAAO;QACjB,EAAE,OAAO,GAAG,CAAC;IACf;IACA,OAAO;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GACD,SAAS,GAAG,KAAK,EAAE,KAAK;IACtB,OAAO,UAAU,SAAU,UAAU,SAAS,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,IAAI,cAAc,gBAAgB;IAAa,OAAO;AAAW,OAAO,kBAAkB,SAAS,KAAK;IACtG,OAAO,aAAa,UAAU,eAAe,IAAI,CAAC,OAAO,aACvD,CAAC,qBAAqB,IAAI,CAAC,OAAO;AACtC;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,IAAI,UAAU,MAAM,OAAO;AAE3B;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,QAAQ,SAAS,MAAM,MAAM,KAAK,CAAC,WAAW;AAChE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,kBAAkB,KAAK;IAC9B,OAAO,aAAa,UAAU,YAAY;AAC5C;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,IAAI,WAAW,kBAAkB;AAEjC;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,WAAW,KAAK;IACvB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IACA,wEAAwE;IACxE,8EAA8E;IAC9E,IAAI,MAAM,WAAW;IACrB,OAAO,OAAO,WAAW,OAAO,UAAU,OAAO,YAAY,OAAO;AACtE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACrB,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC7C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,OAAO;IAClB,OAAO,SAAS,QAAQ,CAAC,QAAQ,YAAY,QAAQ,UAAU;AACjE;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,SAAS,QAAQ,OAAO,SAAS;AAC1C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa,UAAU,WAAW,UAAU,WAAW;QAC1D,OAAO;IACT;IACA,IAAI,QAAQ,aAAa;IACzB,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,eAAe,IAAI,CAAC,OAAO,kBAAkB,MAAM,WAAW;IACzE,OAAO,OAAO,QAAQ,cAAc,gBAAgB,QAClD,aAAa,IAAI,CAAC,SAAS;AAC/B;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,IAAI,eAAe,mBAAmB,UAAU,oBAAoB;AAEpE;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,cAAc,KAAK;IAC1B,OAAO,WAAW,OAAO,OAAO;AAClC;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,OAAO,MAAM;IACpB,OAAO,YAAY,UAAU,cAAc,QAAQ,QAAQ,WAAW;AACxE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,IAAI,QAAQ,eAAe,SAAS,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC1D,UAAU,QAAQ,QAAQ;AAC5B;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO;QACL,OAAO;IACT;AACF;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO;AACT;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS;IACP,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}