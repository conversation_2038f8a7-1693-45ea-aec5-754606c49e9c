{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n\"use client\";\nimport {\n  __commonJS,\n  __toESM\n} from \"./chunk-YSQDPG26.js\";\n\n// ../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\nvar require_lodash = __commonJS({\n  \"../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\"(exports, module) {\n    var FUNC_ERROR_TEXT = \"Expected a function\";\n    var NAN = 0 / 0;\n    var symbolTag = \"[object Symbol]\";\n    var reTrim = /^\\s+|\\s+$/g;\n    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n    var reIsBinary = /^0b[01]+$/i;\n    var reIsOctal = /^0o[0-7]+$/i;\n    var freeParseInt = parseInt;\n    var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n    var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n    var root = freeGlobal || freeSelf || Function(\"return this\")();\n    var objectProto = Object.prototype;\n    var objectToString = objectProto.toString;\n    var nativeMax = Math.max;\n    var nativeMin = Math.min;\n    var now = function() {\n      return root.Date.now();\n    };\n    function debounce3(func, wait, options) {\n      var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;\n      if (typeof func != \"function\") {\n        throw new TypeError(FUNC_ERROR_TEXT);\n      }\n      wait = toNumber(wait) || 0;\n      if (isObject(options)) {\n        leading = !!options.leading;\n        maxing = \"maxWait\" in options;\n        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n        trailing = \"trailing\" in options ? !!options.trailing : trailing;\n      }\n      function invokeFunc(time) {\n        var args = lastArgs, thisArg = lastThis;\n        lastArgs = lastThis = void 0;\n        lastInvokeTime = time;\n        result = func.apply(thisArg, args);\n        return result;\n      }\n      function leadingEdge(time) {\n        lastInvokeTime = time;\n        timerId = setTimeout(timerExpired, wait);\n        return leading ? invokeFunc(time) : result;\n      }\n      function remainingWait(time) {\n        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, result2 = wait - timeSinceLastCall;\n        return maxing ? nativeMin(result2, maxWait - timeSinceLastInvoke) : result2;\n      }\n      function shouldInvoke(time) {\n        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;\n        return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;\n      }\n      function timerExpired() {\n        var time = now();\n        if (shouldInvoke(time)) {\n          return trailingEdge(time);\n        }\n        timerId = setTimeout(timerExpired, remainingWait(time));\n      }\n      function trailingEdge(time) {\n        timerId = void 0;\n        if (trailing && lastArgs) {\n          return invokeFunc(time);\n        }\n        lastArgs = lastThis = void 0;\n        return result;\n      }\n      function cancel() {\n        if (timerId !== void 0) {\n          clearTimeout(timerId);\n        }\n        lastInvokeTime = 0;\n        lastArgs = lastCallTime = lastThis = timerId = void 0;\n      }\n      function flush() {\n        return timerId === void 0 ? result : trailingEdge(now());\n      }\n      function debounced() {\n        var time = now(), isInvoking = shouldInvoke(time);\n        lastArgs = arguments;\n        lastThis = this;\n        lastCallTime = time;\n        if (isInvoking) {\n          if (timerId === void 0) {\n            return leadingEdge(lastCallTime);\n          }\n          if (maxing) {\n            timerId = setTimeout(timerExpired, wait);\n            return invokeFunc(lastCallTime);\n          }\n        }\n        if (timerId === void 0) {\n          timerId = setTimeout(timerExpired, wait);\n        }\n        return result;\n      }\n      debounced.cancel = cancel;\n      debounced.flush = flush;\n      return debounced;\n    }\n    function isObject(value) {\n      var type = typeof value;\n      return !!value && (type == \"object\" || type == \"function\");\n    }\n    function isObjectLike(value) {\n      return !!value && typeof value == \"object\";\n    }\n    function isSymbol(value) {\n      return typeof value == \"symbol\" || isObjectLike(value) && objectToString.call(value) == symbolTag;\n    }\n    function toNumber(value) {\n      if (typeof value == \"number\") {\n        return value;\n      }\n      if (isSymbol(value)) {\n        return NAN;\n      }\n      if (isObject(value)) {\n        var other = typeof value.valueOf == \"function\" ? value.valueOf() : value;\n        value = isObject(other) ? other + \"\" : other;\n      }\n      if (typeof value != \"string\") {\n        return value === 0 ? value : +value;\n      }\n      value = value.replace(reTrim, \"\");\n      var isBinary = reIsBinary.test(value);\n      return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;\n    }\n    module.exports = debounce3;\n  }\n});\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\nimport * as React4 from \"react\";\n\n// esbuild-scss-modules-plugin:./toolbar.module.scss\nvar digest = \"dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7\";\nvar classes = { \"wrapper\": \"_wrapper_ypbb5_1\", \"branch\": \"_branch_ypbb5_32\", \"in\": \"_in_ypbb5_1\", \"root\": \"_root_ypbb5_53\", \"draft\": \"_draft_ypbb5_67\", \"breathe\": \"_breathe_ypbb5_1\", \"tooltipWrapper\": \"_tooltipWrapper_ypbb5_122\", \"tooltip\": \"_tooltip_ypbb5_122\", \"dragHandle\": \"_dragHandle_ypbb5_131\", \"dragging\": \"_dragging_ypbb5_135\", \"forceVisible\": \"_forceVisible_ypbb5_158\", \"top\": \"_top_ypbb5_161\", \"bottom\": \"_bottom_ypbb5_172\", \"right\": \"_right_ypbb5_182\", \"left\": \"_left_ypbb5_193\", \"branchSelect\": \"_branchSelect_ypbb5_219\", \"branchSelectIcon\": \"_branchSelectIcon_ypbb5_245\" };\nvar css = `._wrapper_ypbb5_1 {\n  box-sizing: border-box;\n  font-size: 16px;\n}\n._wrapper_ypbb5_1 *,\n._wrapper_ypbb5_1 *:before,\n._wrapper_ypbb5_1 *:after {\n  box-sizing: inherit;\n}\n._wrapper_ypbb5_1 h1,\n._wrapper_ypbb5_1 h2,\n._wrapper_ypbb5_1 h3,\n._wrapper_ypbb5_1 h4,\n._wrapper_ypbb5_1 h5,\n._wrapper_ypbb5_1 h6,\n._wrapper_ypbb5_1 p,\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  margin: 0;\n  padding: 0;\n  font-weight: normal;\n}\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  list-style: none;\n}\n._wrapper_ypbb5_1 img {\n  max-width: 100%;\n  height: auto;\n}\n\n._branch_ypbb5_32 {\n  padding-left: 9px;\n  padding-right: 12px;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  font-weight: 500;\n  user-select: none;\n}\n\n._wrapper_ypbb5_1 {\n  position: fixed;\n  bottom: 32px;\n  right: 32px;\n  background: #0c0c0c;\n  z-index: 1000;\n  border-radius: 7px;\n  animation: _in_ypbb5_1 0.3s ease-out;\n  display: flex;\n}\n\n._root_ypbb5_53 {\n  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,\n    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;\n  border-radius: 6px;\n  height: 36px;\n  color: white;\n  display: flex;\n  border: 1px solid #303030;\n  font-family: var(--font-family);\n}\n._root_ypbb5_53[data-draft-active=true] {\n  border-color: #ff6c02;\n  background-color: rgba(255, 108, 2, 0.15);\n}\n._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {\n  border-color: #ff8b35;\n}\n\n._draft_ypbb5_67 {\n  all: unset;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 10px;\n  cursor: pointer;\n  color: #646464;\n  border-left: 1px solid #303030;\n  border-radius: 0 5px 5px 0;\n  margin: -1px;\n}\n._draft_ypbb5_67:disabled:hover {\n  cursor: not-allowed;\n}\n._draft_ypbb5_67[data-active=true] {\n  border-color: #ff6c02;\n}\n._draft_ypbb5_67[data-active=true]:enabled:hover {\n  border-color: #ff8b35;\n  background-color: #ff8b35;\n}\n._draft_ypbb5_67[data-active=false] {\n  border: 1px solid #303030;\n}\n._draft_ypbb5_67[data-active=false]:enabled:hover {\n  background-color: #0c0c0c;\n}\n._draft_ypbb5_67:focus-visible {\n  outline: 1px solid;\n  outline-offset: -1px;\n  outline-color: #303030;\n  border-radius: 0 6px 6px 0;\n}\n._draft_ypbb5_67[data-active=true] {\n  color: #f3f3f3;\n  background-color: #ff6c02;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {\n  transition: color 0.2s, background-color 0.2s;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {\n  color: #fff;\n}\n._draft_ypbb5_67[data-loading=true] {\n  cursor: wait !important;\n}\n._draft_ypbb5_67[data-loading=true] svg {\n  animation: _breathe_ypbb5_1 1s infinite;\n}\n\n._tooltipWrapper_ypbb5_122 {\n  position: relative;\n  display: flex;\n  height: 100%;\n}\n._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {\n  visibility: visible;\n}\n\n._dragHandle_ypbb5_131 {\n  all: unset;\n  cursor: grab;\n}\n._dragHandle_ypbb5_131._dragging_ypbb5_135 {\n  cursor: grabbing;\n}\n._dragHandle_ypbb5_131:active {\n  cursor: grabbing;\n}\n\n._tooltip_ypbb5_122 {\n  position: absolute;\n  bottom: 40px;\n  left: 50%;\n  transform: translateX(-50%) translateY(0);\n  background-color: #0c0c0c;\n  border: 1px solid #303030;\n  color: white;\n  border-radius: 4px;\n  max-width: 250px;\n  width: max-content;\n  font-size: 14px;\n  z-index: 1000;\n  visibility: hidden;\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._forceVisible_ypbb5_158 {\n  visibility: visible;\n}\n._tooltip_ypbb5_122._top_ypbb5_161 {\n  top: 40px;\n  bottom: unset;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._top_ypbb5_161:before {\n  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  top: -4.5px;\n  bottom: unset;\n  transform: translateX(var(--translate-x)) rotate(45deg);\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172 {\n  bottom: unset;\n  top: -40px;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172:before {\n  bottom: -4.5px;\n  top: unset;\n  transform: translateX(0) rotate(45deg);\n}\n._tooltip_ypbb5_122._right_ypbb5_182 {\n  right: 0;\n  left: unset;\n  transform: translateX(0);\n  --translate-x: 0;\n}\n._tooltip_ypbb5_122._right_ypbb5_182:before {\n  right: 8px;\n  left: unset;\n  transform: translateX(--translate-x) rotate(45deg);\n}\n._tooltip_ypbb5_122._left_ypbb5_193 {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%);\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._left_ypbb5_193:before {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%) rotate(45deg);\n}\n._tooltip_ypbb5_122:before {\n  z-index: -1;\n  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  content: \"\";\n  position: absolute;\n  bottom: -4.5px;\n  left: 50%;\n  width: 20px;\n  height: 20px;\n  background-color: #0c0c0c;\n  transform: rotate(45deg) translateX(-50%);\n  border-radius: 2px;\n  border: 1px solid #303030;\n}\n\n._branchSelect_ypbb5_219 {\n  height: 100%;\n  background: none;\n  border: none;\n  font-weight: 500;\n  font-size: 16px;\n  padding-right: 8px;\n  padding-bottom: 0px;\n  padding-top: 0px;\n  margin-bottom: 2px;\n  min-width: 80px;\n  max-width: 250px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  outline: none;\n  color: inherit;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  opacity: 1;\n  font-family: var(--font-family);\n  appearance: none;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n._branchSelectIcon_ypbb5_245 {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: 0;\n  pointer-events: none;\n}\n\n@keyframes _in_ypbb5_1 {\n  0% {\n    opacity: 0;\n    transform: translateY(4px) scale(0.98);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n@keyframes _breathe_ypbb5_1 {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.45;\n  }\n  100% {\n    opacity: 1;\n  }\n}`;\n(function() {\n  if (typeof document !== \"undefined\" && !document.getElementById(digest)) {\n    var ele = document.createElement(\"style\");\n    ele.id = digest;\n    ele.textContent = css;\n    document.head.appendChild(ele);\n  }\n})();\nvar toolbar_module_default = classes;\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/tooltip.tsx\nvar import_lodash = __toESM(require_lodash(), 1);\nimport * as React from \"react\";\nvar Tooltip = React.forwardRef(\n  ({\n    children,\n    content,\n    forceVisible\n  }, ref) => {\n    const tooltipContentRef = React.useRef(null);\n    const checkOverflow = React.useCallback(\n      (0, import_lodash.default)(() => {\n        if (tooltipContentRef.current) {\n          const rect = tooltipContentRef.current.getBoundingClientRect();\n          const paddingInline = tooltipContentRef.current.classList.contains(\n            toolbar_module_default.left\n          ) ? 0 : rect.width / 2;\n          const paddingBlock = rect.height;\n          const tooltipOffset = 40 * 2;\n          const isAlreadyToTop = tooltipContentRef.current.classList.contains(\n            toolbar_module_default.bottom\n          );\n          if ((isAlreadyToTop ? rect.top : rect.top - tooltipOffset - paddingBlock) <= 0) {\n            tooltipContentRef.current.classList.remove(toolbar_module_default.bottom);\n            tooltipContentRef.current.classList.add(toolbar_module_default.top);\n          } else {\n            tooltipContentRef.current.classList.remove(toolbar_module_default.top);\n            tooltipContentRef.current.classList.add(toolbar_module_default.bottom);\n          }\n          if (rect.right + paddingInline > window.innerWidth) {\n            tooltipContentRef.current.classList.remove(toolbar_module_default.left);\n            tooltipContentRef.current.classList.add(toolbar_module_default.right);\n          } else {\n            tooltipContentRef.current.classList.remove(toolbar_module_default.right);\n            tooltipContentRef.current.classList.add(toolbar_module_default.left);\n          }\n        }\n      }, 100),\n      []\n    );\n    React.useEffect(() => {\n      checkOverflow();\n      window.addEventListener(\"resize\", checkOverflow);\n      return () => {\n        window.removeEventListener(\"resize\", checkOverflow);\n      };\n    }, [checkOverflow]);\n    React.useImperativeHandle(ref, () => ({ checkOverflow }), [checkOverflow]);\n    return /* @__PURE__ */ React.createElement(\"div\", { className: toolbar_module_default.tooltipWrapper }, /* @__PURE__ */ React.createElement(\n      \"p\",\n      {\n        ref: tooltipContentRef,\n        style: { padding: \"3px 8px\" },\n        className: forceVisible ? `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left} ${toolbar_module_default.forceVisible}` : `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left}`\n      },\n      content\n    ), children);\n  }\n);\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/drag-handle.tsx\nimport * as React2 from \"react\";\nvar DragHandle = React2.forwardRef(\n  ({\n    onDrag,\n    children\n  }, ref) => {\n    const [isDragging, setIsDragging] = React2.useState(false);\n    const initialPointer = React2.useRef({ x: 0, y: 0 });\n    const initialToolbar = React2.useRef({ x: 0, y: 0 });\n    const hasDragged = React2.useRef(false);\n    React2.useImperativeHandle(ref, () => ({\n      hasDragged: hasDragged.current\n    }));\n    const handleDrag = React2.useCallback(\n      (e) => {\n        if (!isDragging)\n          return;\n        const deltaX = e.clientX - initialPointer.current.x;\n        const deltaY = e.clientY - initialPointer.current.y;\n        const newToolbarX = initialToolbar.current.x + deltaX;\n        const newToolbarY = initialToolbar.current.y + deltaY;\n        if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {\n          hasDragged.current = true;\n        }\n        onDrag({ x: newToolbarX, y: newToolbarY });\n      },\n      [isDragging, onDrag]\n    );\n    React2.useLayoutEffect(() => {\n      if (!isDragging)\n        return;\n      window.addEventListener(\"pointermove\", handleDrag);\n      return () => {\n        window.removeEventListener(\"pointermove\", handleDrag);\n      };\n    }, [isDragging, onDrag, handleDrag]);\n    React2.useLayoutEffect(() => {\n      if (!isDragging) {\n        hasDragged.current = false;\n        return;\n      }\n      const handlePointerUp = () => {\n        setIsDragging(false);\n      };\n      window.addEventListener(\"pointerup\", handlePointerUp);\n      return () => {\n        window.removeEventListener(\"pointerup\", handlePointerUp);\n      };\n    }, [isDragging]);\n    return /* @__PURE__ */ React2.createElement(\n      \"span\",\n      {\n        draggable: true,\n        className: `${toolbar_module_default.dragHandle} ${isDragging ? toolbar_module_default.dragging : \"\"}`,\n        onPointerDown: (e) => {\n          if (e.target instanceof HTMLElement && (e.target.nodeName.toLowerCase() === \"select\" || e.target.closest(\"select\"))) {\n            return;\n          }\n          const handle = e.currentTarget;\n          if (!handle)\n            return;\n          e.stopPropagation();\n          e.preventDefault();\n          initialPointer.current = { x: e.clientX, y: e.clientY };\n          const rect = handle.getBoundingClientRect();\n          initialToolbar.current.x = rect.left;\n          initialToolbar.current.y = rect.top;\n          setIsDragging(true);\n        },\n        onPointerUp: () => {\n          setIsDragging(false);\n        }\n      },\n      children\n    );\n  }\n);\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/branch-swticher.tsx\nimport * as React3 from \"react\";\nvar BranchSwitcher = ({\n  isForcedDraft,\n  draft,\n  apiRref,\n  latestBranches,\n  onRefChange,\n  getAndSetLatestBranches\n}) => {\n  const shadowRef = React3.useRef(null);\n  const selectRef = React3.useRef(null);\n  const sortedLatestBranches = React3.useMemo(() => {\n    return [...latestBranches].sort((a, b) => {\n      if (a.isDefault)\n        return -1;\n      if (b.isDefault)\n        return 1;\n      return a.name.localeCompare(b.name);\n    });\n  }, [latestBranches]);\n  const refOptions = React3.useMemo(() => {\n    const options = new Set(sortedLatestBranches.map((branch) => branch.name));\n    options.add(apiRref);\n    return Array.from(options);\n  }, [sortedLatestBranches, apiRref]);\n  const [refetchLatestBranches, setRefetchLatestBranches] = React3.useState(false);\n  React3.useEffect(() => {\n    if (refetchLatestBranches) {\n      getAndSetLatestBranches().then(() => {\n        setRefetchLatestBranches(false);\n      });\n    }\n  }, [refetchLatestBranches, getAndSetLatestBranches]);\n  React3.useEffect(() => {\n    const shadow = shadowRef.current;\n    const select = selectRef.current;\n    if (!shadow || !select)\n      return;\n    const updateSelectWidth = () => {\n      const width = shadow.offsetWidth;\n      select.style.width = `${width + 20}px`;\n    };\n    updateSelectWidth();\n    window.addEventListener(\"resize\", updateSelectWidth);\n    return () => {\n      window.removeEventListener(\"resize\", updateSelectWidth);\n      if (select) {\n        select.style.removeProperty(\"width\");\n      }\n    };\n  }, [apiRref]);\n  const isDraftActive = isForcedDraft || draft;\n  return /* @__PURE__ */ React3.createElement(\n    \"div\",\n    {\n      className: toolbar_module_default.branch,\n      \"data-draft-active\": isDraftActive,\n      onMouseEnter: () => {\n        setRefetchLatestBranches(true);\n      }\n    },\n    /* @__PURE__ */ React3.createElement(BranchIcon, null),\n    \"\\xA0\",\n    /* @__PURE__ */ React3.createElement(\n      Tooltip,\n      {\n        content: !isDraftActive ? \"Switch branch and enter draft mode\" : \"Switch branch\"\n      },\n      /* @__PURE__ */ React3.createElement(\n        \"select\",\n        {\n          ref: selectRef,\n          value: apiRref,\n          onChange: (e) => onRefChange(e.target.value, { enableDraftMode: !isDraftActive }),\n          className: toolbar_module_default.branchSelect,\n          onMouseDown: (e) => {\n            e.stopPropagation();\n          },\n          onClick: (e) => {\n            e.stopPropagation();\n            setRefetchLatestBranches(true);\n          }\n        },\n        refOptions.map((r) => {\n          return /* @__PURE__ */ React3.createElement(\"option\", { key: r, value: r }, r);\n        })\n      ),\n      /* @__PURE__ */ React3.createElement(\n        \"svg\",\n        {\n          width: \"15\",\n          height: \"15\",\n          viewBox: \"0 0 15 15\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: toolbar_module_default.branchSelectIcon\n        },\n        /* @__PURE__ */ React3.createElement(\n          \"path\",\n          {\n            d: \"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z\",\n            fill: \"currentColor\",\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\"\n          }\n        )\n      )\n    ),\n    /* @__PURE__ */ React3.createElement(\n      \"span\",\n      {\n        className: toolbar_module_default.branchSelect,\n        style: {\n          visibility: \"hidden\",\n          opacity: 0,\n          pointerEvents: \"none\",\n          position: \"absolute\",\n          top: 0,\n          left: 0\n        },\n        \"aria-hidden\": \"true\",\n        ref: shadowRef\n      },\n      apiRref\n    )\n  );\n};\nvar BranchIcon = () => {\n  return /* @__PURE__ */ React3.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", width: \"18\", height: \"18\", fill: \"none\" }, /* @__PURE__ */ React3.createElement(\n    \"path\",\n    {\n      fill: \"#F3F3F3\",\n      fillRule: \"evenodd\",\n      d: \"M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z\",\n      clipRule: \"evenodd\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\n    \"path\",\n    {\n      fill: \"#F3F3F3\",\n      fillRule: \"evenodd\",\n      d: \"M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z\",\n      clipRule: \"evenodd\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\n    \"path\",\n    {\n      fill: \"#F3F3F3\",\n      fillRule: \"evenodd\",\n      d: \"M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z\",\n      clipRule: \"evenodd\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\n    \"path\",\n    {\n      fill: \"#F3F3F3\",\n      fillRule: \"evenodd\",\n      d: \"M5.767 9.022V5.374H4.702v3.648h1.065Z\",\n      clipRule: \"evenodd\"\n    }\n  ));\n};\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\nvar import_lodash2 = __toESM(require_lodash(), 1);\nimport { usePathname } from \"next/navigation\";\nvar TOOLBAR_POSITION_STORAGE_KEY = \"bshb_toolbar_pos\";\nvar ClientToolbar = ({\n  draft,\n  isForcedDraft,\n  enableDraftMode,\n  disableDraftMode,\n  bshbPreviewToken,\n  shouldAutoEnableDraft,\n  seekAndStoreBshbPreviewToken,\n  resolvedRef,\n  getLatestBranches\n}) => {\n  const [toolbarRef, setToolbarRef] = React4.useState(\n    null\n  );\n  const dragHandleRef = React4.useRef(null);\n  const tooltipRef = React4.useRef(null);\n  const [message, setMessage] = React4.useState(\"\");\n  const [loading, setLoading] = React4.useState(false);\n  const [previewRef, _setPreviewRef] = React4.useState(resolvedRef.ref);\n  const [isDefaultRefSelected, setIsDefaultRefSelected] = React4.useState(true);\n  const [isLoadingRef, setIsLoadingRef] = React4.useState(true);\n  const [latestBranches, setLatestBranches] = React4.useState(\n    []\n  );\n  const currentMessageTimeout = React4.useRef(0);\n  const displayMessage = React4.useCallback(\n    (message2) => {\n      window.clearTimeout(currentMessageTimeout.current);\n      setMessage(message2);\n      currentMessageTimeout.current = window.setTimeout(\n        () => setMessage(\"\"),\n        5e3\n      );\n    },\n    [setMessage]\n  );\n  const triggerDraftMode = React4.useCallback(\n    (previewToken) => {\n      setLoading(true);\n      enableDraftMode({ bshbPreviewToken: previewToken }).then(({ status, response }) => {\n        if (status === 200) {\n          setLatestBranches((p) => response.latestBranches ?? p);\n          window.location.reload();\n        } else if (\"error\" in response) {\n          displayMessage(`Draft mode activation error: ${response.error}`);\n        } else {\n          displayMessage(\"Draft mode activation error\");\n        }\n      }).finally(() => setLoading(false));\n    },\n    [enableDraftMode, displayMessage]\n  );\n  const bshbPreviewRefCookieName = `bshb-preview-ref-${resolvedRef.repoHash}`;\n  const previewRefCookieManager = React4.useMemo(\n    () => ({\n      set: (ref) => {\n        document.cookie = `${bshbPreviewRefCookieName}=${ref}; path=/; Max-Age=${60 * 60 * 24 * 30 * 365}`;\n      },\n      clear: () => {\n        document.cookie = `${bshbPreviewRefCookieName}=; path=/; Max-Age=-1`;\n      },\n      get: () => {\n        return document.cookie.split(\"; \").find((row) => row.startsWith(bshbPreviewRefCookieName))?.split(\"=\")[1] ?? null;\n      }\n    }),\n    [bshbPreviewRefCookieName]\n  );\n  const [hasAutoEnabledDraftOnce, setHasAutoEnabledDraftOnce] = React4.useState(false);\n  React4.useLayoutEffect(() => {\n    if (draft || hasAutoEnabledDraftOnce || !shouldAutoEnableDraft || isForcedDraft || !bshbPreviewToken) {\n      return;\n    }\n    triggerDraftMode(bshbPreviewToken);\n    setHasAutoEnabledDraftOnce(true);\n  }, [\n    isForcedDraft,\n    enableDraftMode,\n    seekAndStoreBshbPreviewToken,\n    bshbPreviewToken,\n    displayMessage,\n    triggerDraftMode,\n    draft,\n    shouldAutoEnableDraft,\n    hasAutoEnabledDraftOnce\n  ]);\n  const getAndSetLatestBranches = React4.useCallback(async () => {\n    let result = [];\n    const res = await getLatestBranches({ bshbPreviewToken });\n    if (!res)\n      return;\n    if (Array.isArray(res.response)) {\n      result = res.response;\n    } else if (\"error\" in res.response) {\n      console.error(`BaseHub Toolbar Error: ${res.response.error}`);\n    }\n    setLatestBranches(result);\n  }, [bshbPreviewToken, getLatestBranches]);\n  React4.useEffect(() => {\n    async function effect() {\n      while (true) {\n        try {\n          getAndSetLatestBranches();\n          await new Promise((resolve) => setTimeout(resolve, 3e4));\n        } catch (error) {\n          console.error(`BaseHub Toolbar Error: ${error}`);\n          break;\n        }\n      }\n    }\n    effect();\n  }, [getAndSetLatestBranches]);\n  const setRefWithEvents = React4.useCallback(\n    (ref) => {\n      _setPreviewRef(ref);\n      window.__bshb_ref = ref;\n      window.dispatchEvent(new CustomEvent(\"__bshb_ref_changed\"));\n      previewRefCookieManager.set(ref);\n      setIsDefaultRefSelected(ref === resolvedRef.ref);\n    },\n    [previewRefCookieManager, resolvedRef.ref]\n  );\n  React4.useEffect(() => {\n    const url = new URL(window.location.href);\n    let previewRef2 = url.searchParams.get(\"bshb-preview-ref\");\n    if (!previewRef2) {\n      previewRef2 = previewRefCookieManager.get();\n    }\n    setIsLoadingRef(false);\n    if (!previewRef2)\n      return;\n    setRefWithEvents(previewRef2);\n  }, [previewRefCookieManager, setRefWithEvents, resolvedRef.repoHash]);\n  React4.useEffect(() => {\n    if (isLoadingRef)\n      return;\n    setIsDefaultRefSelected(previewRef === resolvedRef.ref);\n  }, [previewRef, resolvedRef.ref, isLoadingRef]);\n  React4.useEffect(() => {\n    if (isLoadingRef)\n      return;\n    if (isDefaultRefSelected) {\n      setRefWithEvents(resolvedRef.ref);\n      previewRefCookieManager.clear();\n      const url = new URL(window.location.href);\n      url.searchParams.delete(\"bshb-preview-ref\");\n      window.history.replaceState(null, \"\", url.toString());\n    }\n  }, [\n    isDefaultRefSelected,\n    isLoadingRef,\n    previewRefCookieManager,\n    resolvedRef.ref,\n    setRefWithEvents\n  ]);\n  React4.useLayoutEffect(() => {\n    tooltipRef.current?.checkOverflow();\n  }, [message]);\n  const getStoredToolbarPosition = React4.useCallback(() => {\n    if (!toolbarRef)\n      return;\n    if (typeof window === \"undefined\" || !window.sessionStorage)\n      return;\n    const toolbarPositionStored = window.sessionStorage.getItem(\n      TOOLBAR_POSITION_STORAGE_KEY\n    );\n    if (!toolbarPositionStored)\n      return;\n    const toolbarPosition = JSON.parse(toolbarPositionStored);\n    if (!(\"x\" in toolbarPosition))\n      return;\n    if (!(\"y\" in toolbarPosition))\n      return;\n    return toolbarPosition;\n  }, [toolbarRef]);\n  const updateToolbarStoredPositionDebounced = React4.useCallback(\n    (0, import_lodash2.default)((position) => {\n      if (typeof window === \"undefined\" || !window.sessionStorage)\n        return;\n      const storedPosition = getStoredToolbarPosition() ?? { x: 0, y: 0 };\n      window.sessionStorage.setItem(\n        TOOLBAR_POSITION_STORAGE_KEY,\n        JSON.stringify({ ...storedPosition, ...position })\n      );\n    }, 250),\n    []\n  );\n  const dragToolbar = React4.useCallback(\n    (position) => {\n      const toolbar = toolbarRef;\n      if (!toolbar)\n        return;\n      const rect = toolbar.getBoundingClientRect();\n      const padding = 32;\n      const newPositionForStore = {};\n      if (position.x - padding < 0) {\n        toolbar.style.left = `${padding}px`;\n        toolbar.style.right = \"unset\";\n        newPositionForStore.x = padding;\n      } else if (position.x + rect.width + padding > window.innerWidth) {\n        toolbar.style.right = `${padding}px`;\n        toolbar.style.left = \"unset\";\n        newPositionForStore.x = padding;\n      } else {\n        toolbar.style.right = \"unset\";\n        toolbar.style.left = `${position.x}px`;\n        newPositionForStore.x = position.x;\n      }\n      if (position.y - padding < 0) {\n        toolbar.style.bottom = \"unset\";\n        toolbar.style.top = `${padding}px`;\n        newPositionForStore.y = padding;\n      } else if (position.y + rect.height + padding > window.innerHeight) {\n        toolbar.style.top = \"unset\";\n        toolbar.style.bottom = `${padding}px`;\n        newPositionForStore.y = padding;\n      } else {\n        toolbar.style.bottom = \"unset\";\n        toolbar.style.top = `${position.y}px`;\n        newPositionForStore.x = position.y;\n      }\n      updateToolbarStoredPositionDebounced({ x: position.x, y: position.y });\n    },\n    [toolbarRef, updateToolbarStoredPositionDebounced]\n  );\n  React4.useEffect(() => {\n    if (typeof window === \"undefined\")\n      return;\n    const repositionToolbar = () => {\n      const pos = getStoredToolbarPosition();\n      if (!pos)\n        return;\n      dragToolbar(pos);\n      tooltipRef.current?.checkOverflow();\n    };\n    repositionToolbar();\n    window.addEventListener(\"resize\", repositionToolbar);\n    return () => {\n      window.removeEventListener(\"resize\", repositionToolbar);\n    };\n  }, [getStoredToolbarPosition, dragToolbar]);\n  React4.useEffect(() => {\n    if (!latestBranches)\n      return;\n    const fromCookie = previewRefCookieManager.get();\n    if (!fromCookie)\n      return;\n    if (!latestBranches.find((branch) => branch.name === fromCookie)) {\n      previewRefCookieManager.clear();\n    }\n  }, [latestBranches, previewRefCookieManager]);\n  const tooltip = isForcedDraft ? \"Draft enforced by dev env\" : `${draft ? \"Disable\" : \"Enable\"} draft mode`;\n  return /* @__PURE__ */ React4.createElement(\"div\", { className: toolbar_module_default.wrapper, ref: setToolbarRef }, /* @__PURE__ */ React4.createElement(\n    DragHandle,\n    {\n      ref: dragHandleRef,\n      onDrag: (pos) => {\n        dragToolbar(pos);\n        tooltipRef.current?.checkOverflow();\n      }\n    },\n    /* @__PURE__ */ React4.createElement(\"div\", { className: toolbar_module_default.root, \"data-draft-active\": isForcedDraft || draft }, /* @__PURE__ */ React4.createElement(\n      BranchSwitcher,\n      {\n        isForcedDraft,\n        draft,\n        apiRref: previewRef,\n        latestBranches,\n        onRefChange: (newRef, opts) => {\n          const url = new URL(window.location.href);\n          url.searchParams.set(\"bshb-preview-ref\", newRef);\n          window.history.replaceState(null, \"\", url.toString());\n          setRefWithEvents(newRef);\n          if (opts.enableDraftMode) {\n            const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();\n            if (!previewToken) {\n              return displayMessage(\"Preview token not found\");\n            }\n            triggerDraftMode(previewToken);\n          }\n        },\n        getAndSetLatestBranches\n      }\n    ), /* @__PURE__ */ React4.createElement(\n      AutoAddRefToUrlOnPathChangeIfRefIsNotDefault,\n      {\n        previewRef,\n        resolvedRef,\n        isDraftModeEnabled: isForcedDraft || draft\n      }\n    ), /* @__PURE__ */ React4.createElement(\n      Tooltip,\n      {\n        content: message || tooltip,\n        ref: tooltipRef,\n        forceVisible: Boolean(message)\n      },\n      /* @__PURE__ */ React4.createElement(\n        \"button\",\n        {\n          className: toolbar_module_default.draft,\n          \"data-active\": isForcedDraft || draft,\n          \"aria-label\": `${draft ? \"Disable\" : \"Enable\"} draft mode`,\n          \"data-loading\": loading,\n          disabled: isForcedDraft || loading,\n          onClick: () => {\n            if (loading || dragHandleRef.current?.hasDragged)\n              return;\n            if (draft) {\n              setLoading(true);\n              disableDraftMode().then(() => {\n                const url = new URL(window.location.href);\n                url.searchParams.delete(\"bshb-preview\");\n                url.searchParams.delete(\"__vercel_draft\");\n                window.location.href = url.toString();\n              }).finally(() => setLoading(false));\n            } else {\n              const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();\n              if (!previewToken) {\n                return displayMessage(\"Preview token not found\");\n              }\n              triggerDraftMode(previewToken);\n            }\n          }\n        },\n        draft || isForcedDraft ? /* @__PURE__ */ React4.createElement(EyeIcon, null) : /* @__PURE__ */ React4.createElement(EyeDashedIcon, null)\n      )\n    ))\n  ));\n};\nvar AutoAddRefToUrlOnPathChangeIfRefIsNotDefault = ({\n  previewRef,\n  resolvedRef,\n  isDraftModeEnabled\n}) => {\n  const pathname = usePathname();\n  const [initialPathname, setInitialPathname] = React4.useState(pathname);\n  React4.useEffect(() => {\n    if (initialPathname)\n      return;\n    setInitialPathname(pathname);\n  }, [pathname, initialPathname]);\n  React4.useEffect(() => {\n    if (isDraftModeEnabled)\n      return;\n    if (initialPathname === pathname) {\n      return;\n    }\n    if (previewRef !== resolvedRef.ref) {\n      const url = new URL(window.location.href);\n      url.searchParams.set(\"bshb-preview-ref\", previewRef);\n      window.history.replaceState(null, \"\", url.toString());\n    }\n  }, [\n    isDraftModeEnabled,\n    previewRef,\n    resolvedRef.ref,\n    pathname,\n    initialPathname\n  ]);\n  return null;\n};\nvar EyeDashedIcon = () => {\n  return /* @__PURE__ */ React4.createElement(\n    \"svg\",\n    {\n      \"data-testid\": \"geist-icon\",\n      height: \"16\",\n      strokeLinejoin: \"round\",\n      viewBox: \"0 0 16 16\",\n      width: \"16\",\n      style: { color: \"currentcolor\" }\n    },\n    /* @__PURE__ */ React4.createElement(\n      \"path\",\n      {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n      }\n    )\n  );\n};\nvar EyeIcon = () => {\n  return /* @__PURE__ */ React4.createElement(\n    \"svg\",\n    {\n      \"data-testid\": \"geist-icon\",\n      height: \"16\",\n      strokeLinejoin: \"round\",\n      viewBox: \"0 0 16 16\",\n      width: \"16\",\n      style: { color: \"currentcolor\" }\n    },\n    /* @__PURE__ */ React4.createElement(\n      \"path\",\n      {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n      }\n    )\n  );\n};\nexport {\n  ClientToolbar\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;AAGlB;AA0IA,iJAAiJ;AACjJ;AAilBA;;AA7tBA;;AAMA,uFAAuF;AACvF,IAAI,iBAAiB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE;IAC9B,wFAAuF,OAAO,EAAE,MAAM;QACpG,IAAI,kBAAkB;QACtB,IAAI,MAAM,IAAI;QACd,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,aAAa;QACjB,IAAI,aAAa;QACjB,IAAI,YAAY;QAChB,IAAI,eAAe;QACnB,IAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,MAAM,KAAK,UAAU;QACpF,IAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,MAAM,KAAK,UAAU;QAC5E,IAAI,OAAO,cAAc,YAAY,SAAS;QAC9C,IAAI,cAAc,OAAO,SAAS;QAClC,IAAI,iBAAiB,YAAY,QAAQ;QACzC,IAAI,YAAY,KAAK,GAAG;QACxB,IAAI,YAAY,KAAK,GAAG;QACxB,IAAI,MAAM;YACR,OAAO,KAAK,IAAI,CAAC,GAAG;QACtB;QACA,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,OAAO;YACpC,IAAI,UAAU,UAAU,SAAS,QAAQ,SAAS,cAAc,iBAAiB,GAAG,UAAU,OAAO,SAAS,OAAO,WAAW;YAChI,IAAI,OAAO,QAAQ,YAAY;gBAC7B,MAAM,IAAI,UAAU;YACtB;YACA,OAAO,SAAS,SAAS;YACzB,IAAI,SAAS,UAAU;gBACrB,UAAU,CAAC,CAAC,QAAQ,OAAO;gBAC3B,SAAS,aAAa;gBACtB,UAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,QAAQ;gBACrE,WAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,QAAQ,GAAG;YAC1D;YACA,SAAS,WAAW,IAAI;gBACtB,IAAI,OAAO,UAAU,UAAU;gBAC/B,WAAW,WAAW,KAAK;gBAC3B,iBAAiB;gBACjB,SAAS,KAAK,KAAK,CAAC,SAAS;gBAC7B,OAAO;YACT;YACA,SAAS,YAAY,IAAI;gBACvB,iBAAiB;gBACjB,UAAU,WAAW,cAAc;gBACnC,OAAO,UAAU,WAAW,QAAQ;YACtC;YACA,SAAS,cAAc,IAAI;gBACzB,IAAI,oBAAoB,OAAO,cAAc,sBAAsB,OAAO,gBAAgB,UAAU,OAAO;gBAC3G,OAAO,SAAS,UAAU,SAAS,UAAU,uBAAuB;YACtE;YACA,SAAS,aAAa,IAAI;gBACxB,IAAI,oBAAoB,OAAO,cAAc,sBAAsB,OAAO;gBAC1E,OAAO,iBAAiB,KAAK,KAAK,qBAAqB,QAAQ,oBAAoB,KAAK,UAAU,uBAAuB;YAC3H;YACA,SAAS;gBACP,IAAI,OAAO;gBACX,IAAI,aAAa,OAAO;oBACtB,OAAO,aAAa;gBACtB;gBACA,UAAU,WAAW,cAAc,cAAc;YACnD;YACA,SAAS,aAAa,IAAI;gBACxB,UAAU,KAAK;gBACf,IAAI,YAAY,UAAU;oBACxB,OAAO,WAAW;gBACpB;gBACA,WAAW,WAAW,KAAK;gBAC3B,OAAO;YACT;YACA,SAAS;gBACP,IAAI,YAAY,KAAK,GAAG;oBACtB,aAAa;gBACf;gBACA,iBAAiB;gBACjB,WAAW,eAAe,WAAW,UAAU,KAAK;YACtD;YACA,SAAS;gBACP,OAAO,YAAY,KAAK,IAAI,SAAS,aAAa;YACpD;YACA,SAAS;gBACP,IAAI,OAAO,OAAO,aAAa,aAAa;gBAC5C,WAAW;gBACX,WAAW,IAAI;gBACf,eAAe;gBACf,IAAI,YAAY;oBACd,IAAI,YAAY,KAAK,GAAG;wBACtB,OAAO,YAAY;oBACrB;oBACA,IAAI,QAAQ;wBACV,UAAU,WAAW,cAAc;wBACnC,OAAO,WAAW;oBACpB;gBACF;gBACA,IAAI,YAAY,KAAK,GAAG;oBACtB,UAAU,WAAW,cAAc;gBACrC;gBACA,OAAO;YACT;YACA,UAAU,MAAM,GAAG;YACnB,UAAU,KAAK,GAAG;YAClB,OAAO;QACT;QACA,SAAS,SAAS,KAAK;YACrB,IAAI,OAAO,OAAO;YAClB,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,YAAY,QAAQ,UAAU;QAC3D;QACA,SAAS,aAAa,KAAK;YACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;QACpC;QACA,SAAS,SAAS,KAAK;YACrB,OAAO,OAAO,SAAS,YAAY,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;QAC1F;QACA,SAAS,SAAS,KAAK;YACrB,IAAI,OAAO,SAAS,UAAU;gBAC5B,OAAO;YACT;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO;YACT;YACA,IAAI,SAAS,QAAQ;gBACnB,IAAI,QAAQ,OAAO,MAAM,OAAO,IAAI,aAAa,MAAM,OAAO,KAAK;gBACnE,QAAQ,SAAS,SAAS,QAAQ,KAAK;YACzC;YACA,IAAI,OAAO,SAAS,UAAU;gBAC5B,OAAO,UAAU,IAAI,QAAQ,CAAC;YAChC;YACA,QAAQ,MAAM,OAAO,CAAC,QAAQ;YAC9B,IAAI,WAAW,WAAW,IAAI,CAAC;YAC/B,OAAO,YAAY,UAAU,IAAI,CAAC,SAAS,aAAa,MAAM,KAAK,CAAC,IAAI,WAAW,IAAI,KAAK,WAAW,IAAI,CAAC,SAAS,MAAM,CAAC;QAC9H;QACA,OAAO,OAAO,GAAG;IACnB;AACF;;AAKA,oDAAoD;AACpD,IAAI,SAAS;AACb,IAAI,UAAU;IAAE,WAAW;IAAoB,UAAU;IAAoB,MAAM;IAAe,QAAQ;IAAkB,SAAS;IAAmB,WAAW;IAAoB,kBAAkB;IAA6B,WAAW;IAAsB,cAAc;IAAyB,YAAY;IAAuB,gBAAgB;IAA2B,OAAO;IAAkB,UAAU;IAAqB,SAAS;IAAoB,QAAQ;IAAmB,gBAAgB;IAA2B,oBAAoB;AAA8B;AACzkB,IAAI,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgRV,CAAC;AACF,CAAC;IACC,IAAI,OAAO,aAAa,eAAe,CAAC,SAAS,cAAc,CAAC,SAAS;QACvE,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,EAAE,GAAG;QACT,IAAI,WAAW,GAAG;QAClB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF,CAAC;AACD,IAAI,yBAAyB;AAE7B,qJAAqJ;AACrJ,IAAI,gBAAgB,CAAA,GAAA,uKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;;AAE9C,IAAI,wBAAU,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,KAC3B,CAAC,EACC,QAAQ,EACR,OAAO,EACP,YAAY,EACb,EAAE;;IACD,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IACvC,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD,EACpC,CAAC,GAAG,cAAc,OAAO;8CAAE;YACzB,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,MAAM,OAAO,kBAAkB,OAAO,CAAC,qBAAqB;gBAC5D,MAAM,gBAAgB,kBAAkB,OAAO,CAAC,SAAS,CAAC,QAAQ,CAChE,uBAAuB,IAAI,IACzB,IAAI,KAAK,KAAK,GAAG;gBACrB,MAAM,eAAe,KAAK,MAAM;gBAChC,MAAM,gBAAgB,KAAK;gBAC3B,MAAM,iBAAiB,kBAAkB,OAAO,CAAC,SAAS,CAAC,QAAQ,CACjE,uBAAuB,MAAM;gBAE/B,IAAI,CAAC,iBAAiB,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,gBAAgB,YAAY,KAAK,GAAG;oBAC9E,kBAAkB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,MAAM;oBACxE,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,GAAG;gBACpE,OAAO;oBACL,kBAAkB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,GAAG;oBACrE,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,MAAM;gBACvE;gBACA,IAAI,KAAK,KAAK,GAAG,gBAAgB,OAAO,UAAU,EAAE;oBAClD,kBAAkB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,IAAI;oBACtE,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,KAAK;gBACtE,OAAO;oBACL,kBAAkB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,KAAK;oBACvE,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,IAAI;gBACrE;YACF;QACF;6CAAG,MACH,EAAE;IAEJ,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;6BAAE;YACd;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;qCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;4BAAG;QAAC;KAAc;IAClB,CAAA,GAAA,4QAAA,CAAA,sBAAyB,AAAD,EAAE;uCAAK,IAAM,CAAC;gBAAE;YAAc,CAAC;sCAAG;QAAC;KAAc;IACzE,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QAAE,WAAW,uBAAuB,cAAc;IAAC,GAAG,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EACxI,KACA;QACE,KAAK;QACL,OAAO;YAAE,SAAS;QAAU;QAC5B,WAAW,eAAe,GAAG,uBAAuB,OAAO,CAAC,CAAC,EAAE,uBAAuB,MAAM,CAAC,CAAC,EAAE,uBAAuB,IAAI,CAAC,CAAC,EAAE,uBAAuB,YAAY,EAAE,GAAG,GAAG,uBAAuB,OAAO,CAAC,CAAC,EAAE,uBAAuB,MAAM,CAAC,CAAC,EAAE,uBAAuB,IAAI,EAAE;IAC5Q,GACA,UACC;AACL;KAtDE;;AA2DJ,IAAI,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAiB,AAAD,MAC/B,CAAC,EACC,MAAM,EACN,QAAQ,EACT,EAAE;;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IACpD,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAClD,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAClD,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IACjC,CAAA,GAAA,4QAAA,CAAA,sBAA0B,AAAD,EAAE;0CAAK,IAAM,CAAC;gBACrC,YAAY,WAAW,OAAO;YAChC,CAAC;;IACD,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;8CAClC,CAAC;YACC,IAAI,CAAC,YACH;YACF,MAAM,SAAS,EAAE,OAAO,GAAG,eAAe,OAAO,CAAC,CAAC;YACnD,MAAM,SAAS,EAAE,OAAO,GAAG,eAAe,OAAO,CAAC,CAAC;YACnD,MAAM,cAAc,eAAe,OAAO,CAAC,CAAC,GAAG;YAC/C,MAAM,cAAc,eAAe,OAAO,CAAC,CAAC,GAAG;YAC/C,IAAI,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK,GAAG,CAAC,UAAU,GAAG;gBAChD,WAAW,OAAO,GAAG;YACvB;YACA,OAAO;gBAAE,GAAG;gBAAa,GAAG;YAAY;QAC1C;6CACA;QAAC;QAAY;KAAO;IAEtB,CAAA,GAAA,4QAAA,CAAA,kBAAsB,AAAD;sCAAE;YACrB,IAAI,CAAC,YACH;YACF,OAAO,gBAAgB,CAAC,eAAe;YACvC;8CAAO;oBACL,OAAO,mBAAmB,CAAC,eAAe;gBAC5C;;QACF;qCAAG;QAAC;QAAY;QAAQ;KAAW;IACnC,CAAA,GAAA,4QAAA,CAAA,kBAAsB,AAAD;sCAAE;YACrB,IAAI,CAAC,YAAY;gBACf,WAAW,OAAO,GAAG;gBACrB;YACF;YACA,MAAM;8DAAkB;oBACtB,cAAc;gBAChB;;YACA,OAAO,gBAAgB,CAAC,aAAa;YACrC;8CAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;gBAC1C;;QACF;qCAAG;QAAC;KAAW;IACf,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACxC,QACA;QACE,WAAW;QACX,WAAW,GAAG,uBAAuB,UAAU,CAAC,CAAC,EAAE,aAAa,uBAAuB,QAAQ,GAAG,IAAI;QACtG,eAAe,CAAC;YACd,IAAI,EAAE,MAAM,YAAY,eAAe,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,WAAW,OAAO,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG;gBACnH;YACF;YACA,MAAM,SAAS,EAAE,aAAa;YAC9B,IAAI,CAAC,QACH;YACF,EAAE,eAAe;YACjB,EAAE,cAAc;YAChB,eAAe,OAAO,GAAG;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YACtD,MAAM,OAAO,OAAO,qBAAqB;YACzC,eAAe,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI;YACpC,eAAe,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG;YACnC,cAAc;QAChB;QACA,aAAa;YACX,cAAc;QAChB;IACF,GACA;AAEJ;MA1EE;;AA+EJ,IAAI,iBAAiB,CAAC,EACpB,aAAa,EACb,KAAK,EACL,OAAO,EACP,cAAc,EACd,WAAW,EACX,uBAAuB,EACxB;;IACC,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IAChC,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IAChC,MAAM,uBAAuB,CAAA,GAAA,4QAAA,CAAA,UAAc,AAAD;wDAAE;YAC1C,OAAO;mBAAI;aAAe,CAAC,IAAI;gEAAC,CAAC,GAAG;oBAClC,IAAI,EAAE,SAAS,EACb,OAAO,CAAC;oBACV,IAAI,EAAE,SAAS,EACb,OAAO;oBACT,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACpC;;QACF;uDAAG;QAAC;KAAe;IACnB,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,UAAc,AAAD;8CAAE;YAChC,MAAM,UAAU,IAAI,IAAI,qBAAqB,GAAG;sDAAC,CAAC,SAAW,OAAO,IAAI;;YACxE,QAAQ,GAAG,CAAC;YACZ,OAAO,MAAM,IAAI,CAAC;QACpB;6CAAG;QAAC;QAAsB;KAAQ;IAClC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IAC1E,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;oCAAE;YACf,IAAI,uBAAuB;gBACzB,0BAA0B,IAAI;gDAAC;wBAC7B,yBAAyB;oBAC3B;;YACF;QACF;mCAAG;QAAC;QAAuB;KAAwB;IACnD,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;oCAAE;YACf,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,UAAU,CAAC,QACd;YACF,MAAM;8DAAoB;oBACxB,MAAM,QAAQ,OAAO,WAAW;oBAChC,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC;gBACxC;;YACA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,QAAQ;wBACV,OAAO,KAAK,CAAC,cAAc,CAAC;oBAC9B;gBACF;;QACF;mCAAG;QAAC;KAAQ;IACZ,MAAM,gBAAgB,iBAAiB;IACvC,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACxC,OACA;QACE,WAAW,uBAAuB,MAAM;QACxC,qBAAqB;QACrB,cAAc;YACZ,yBAAyB;QAC3B;IACF,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,YAAY,OACjD,QACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,SACA;QACE,SAAS,CAAC,gBAAgB,uCAAuC;IACnE,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,UACA;QACE,KAAK;QACL,OAAO;QACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK,EAAE;gBAAE,iBAAiB,CAAC;YAAc;QAC/E,WAAW,uBAAuB,YAAY;QAC9C,aAAa,CAAC;YACZ,EAAE,eAAe;QACnB;QACA,SAAS,CAAC;YACR,EAAE,eAAe;YACjB,yBAAyB;QAC3B;IACF,GACA,WAAW,GAAG,CAAC,CAAC;QACd,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,UAAU;YAAE,KAAK;YAAG,OAAO;QAAE,GAAG;IAC9E,KAEF,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,OACA;QACE,OAAO;QACP,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,WAAW,uBAAuB,gBAAgB;IACpD,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,QACA;QACE,GAAG;QACH,MAAM;QACN,UAAU;QACV,UAAU;IACZ,MAIN,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,QACA;QACE,WAAW,uBAAuB,YAAY;QAC9C,OAAO;YACL,YAAY;YACZ,SAAS;YACT,eAAe;YACf,UAAU;YACV,KAAK;YACL,MAAM;QACR;QACA,eAAe;QACf,KAAK;IACP,GACA;AAGN;IA7HI;MAAA;AA8HJ,IAAI,aAAa;IACf,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,OAAO;QAAM,QAAQ;QAAM,MAAM;IAAO,GAAG,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACtK,QACA;QACE,MAAM;QACN,UAAU;QACV,GAAG;QACH,UAAU;IACZ,IACC,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACpC,QACA;QACE,MAAM;QACN,UAAU;QACV,GAAG;QACH,UAAU;IACZ,IACC,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACpC,QACA;QACE,MAAM;QACN,UAAU;QACV,GAAG;QACH,UAAU;IACZ,IACC,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACpC,QACA;QACE,MAAM;QACN,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AAEJ;MAlCI;AAoCJ,iJAAiJ;AACjJ,IAAI,iBAAiB,CAAA,GAAA,uKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;;AAE/C,IAAI,+BAA+B;AACnC,IAAI,gBAAgB,CAAC,EACnB,KAAK,EACL,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,4BAA4B,EAC5B,WAAW,EACX,iBAAiB,EAClB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAChD;IAEF,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IACpC,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IAC9C,MAAM,CAAC,YAAY,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE,YAAY,GAAG;IACpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EACxD,EAAE;IAEJ,MAAM,wBAAwB,CAAA,GAAA,4QAAA,CAAA,SAAa,AAAD,EAAE;IAC5C,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;qDACtC,CAAC;YACC,OAAO,YAAY,CAAC,sBAAsB,OAAO;YACjD,WAAW;YACX,sBAAsB,OAAO,GAAG,OAAO,UAAU;6DAC/C,IAAM,WAAW;4DACjB;QAEJ;oDACA;QAAC;KAAW;IAEd,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;uDACxC,CAAC;YACC,WAAW;YACX,gBAAgB;gBAAE,kBAAkB;YAAa,GAAG,IAAI;+DAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;oBAC5E,IAAI,WAAW,KAAK;wBAClB;2EAAkB,CAAC,IAAM,SAAS,cAAc,IAAI;;wBACpD,OAAO,QAAQ,CAAC,MAAM;oBACxB,OAAO,IAAI,WAAW,UAAU;wBAC9B,eAAe,CAAC,6BAA6B,EAAE,SAAS,KAAK,EAAE;oBACjE,OAAO;wBACL,eAAe;oBACjB;gBACF;8DAAG,OAAO;+DAAC,IAAM,WAAW;;QAC9B;sDACA;QAAC;QAAiB;KAAe;IAEnC,MAAM,2BAA2B,CAAC,iBAAiB,EAAE,YAAY,QAAQ,EAAE;IAC3E,MAAM,0BAA0B,CAAA,GAAA,4QAAA,CAAA,UAAc,AAAD;0DAC3C,IAAM,CAAC;gBACL,GAAG;sEAAE,CAAC;wBACJ,SAAS,MAAM,GAAG,GAAG,yBAAyB,CAAC,EAAE,IAAI,kBAAkB,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK;oBACpG;;gBACA,KAAK;sEAAE;wBACL,SAAS,MAAM,GAAG,GAAG,yBAAyB,qBAAqB,CAAC;oBACtE;;gBACA,GAAG;sEAAE;wBACH,OAAO,SAAS,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI;8EAAC,CAAC,MAAQ,IAAI,UAAU,CAAC;8EAA4B,MAAM,IAAI,CAAC,EAAE,IAAI;oBAC/G;;YACF,CAAC;yDACD;QAAC;KAAyB;IAE5B,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IAC9E,CAAA,GAAA,4QAAA,CAAA,kBAAsB,AAAD;yCAAE;YACrB,IAAI,SAAS,2BAA2B,CAAC,yBAAyB,iBAAiB,CAAC,kBAAkB;gBACpG;YACF;YACA,iBAAiB;YACjB,2BAA2B;QAC7B;wCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,0BAA0B,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;8DAAE;YACjD,IAAI,SAAS,EAAE;YACf,MAAM,MAAM,MAAM,kBAAkB;gBAAE;YAAiB;YACvD,IAAI,CAAC,KACH;YACF,IAAI,MAAM,OAAO,CAAC,IAAI,QAAQ,GAAG;gBAC/B,SAAS,IAAI,QAAQ;YACvB,OAAO,IAAI,WAAW,IAAI,QAAQ,EAAE;gBAClC,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC9D;YACA,kBAAkB;QACpB;6DAAG;QAAC;QAAkB;KAAkB;IACxC,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,eAAe;gBACb,MAAO,KAAM;oBACX,IAAI;wBACF;wBACA,MAAM,IAAI;8DAAQ,CAAC,UAAY,WAAW,SAAS;;oBACrD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO;wBAC/C;oBACF;gBACF;YACF;YACA;QACF;kCAAG;QAAC;KAAwB;IAC5B,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;uDACxC,CAAC;YACC,eAAe;YACf,OAAO,UAAU,GAAG;YACpB,OAAO,aAAa,CAAC,IAAI,YAAY;YACrC,wBAAwB,GAAG,CAAC;YAC5B,wBAAwB,QAAQ,YAAY,GAAG;QACjD;sDACA;QAAC;QAAyB,YAAY,GAAG;KAAC;IAE5C,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YACxC,IAAI,cAAc,IAAI,YAAY,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,aAAa;gBAChB,cAAc,wBAAwB,GAAG;YAC3C;YACA,gBAAgB;YAChB,IAAI,CAAC,aACH;YACF,iBAAiB;QACnB;kCAAG;QAAC;QAAyB;QAAkB,YAAY,QAAQ;KAAC;IACpE,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,IAAI,cACF;YACF,wBAAwB,eAAe,YAAY,GAAG;QACxD;kCAAG;QAAC;QAAY,YAAY,GAAG;QAAE;KAAa;IAC9C,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,IAAI,cACF;YACF,IAAI,sBAAsB;gBACxB,iBAAiB,YAAY,GAAG;gBAChC,wBAAwB,KAAK;gBAC7B,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;gBACxC,IAAI,YAAY,CAAC,MAAM,CAAC;gBACxB,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,QAAQ;YACpD;QACF;kCAAG;QACD;QACA;QACA;QACA,YAAY,GAAG;QACf;KACD;IACD,CAAA,GAAA,4QAAA,CAAA,kBAAsB,AAAD;yCAAE;YACrB,WAAW,OAAO,EAAE;QACtB;wCAAG;QAAC;KAAQ;IACZ,MAAM,2BAA2B,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;+DAAE;YAClD,IAAI,CAAC,YACH;YACF,IAAI,aAAkB,eAAe,CAAC,OAAO,cAAc,EACzD;YACF,MAAM,wBAAwB,OAAO,cAAc,CAAC,OAAO,CACzD;YAEF,IAAI,CAAC,uBACH;YACF,MAAM,kBAAkB,KAAK,KAAK,CAAC;YACnC,IAAI,CAAC,CAAC,OAAO,eAAe,GAC1B;YACF,IAAI,CAAC,CAAC,OAAO,eAAe,GAC1B;YACF,OAAO;QACT;8DAAG;QAAC;KAAW;IACf,MAAM,uCAAuC,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD,EAC5D,CAAC,GAAG,eAAe,OAAO;2EAAE,CAAC;YAC3B,IAAI,aAAkB,eAAe,CAAC,OAAO,cAAc,EACzD;YACF,MAAM,iBAAiB,8BAA8B;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAClE,OAAO,cAAc,CAAC,OAAO,CAC3B,8BACA,KAAK,SAAS,CAAC;gBAAE,GAAG,cAAc;gBAAE,GAAG,QAAQ;YAAC;QAEpD;0EAAG,MACH,EAAE;IAEJ,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,cAAkB,AAAD;kDACnC,CAAC;YACC,MAAM,UAAU;YAChB,IAAI,CAAC,SACH;YACF,MAAM,OAAO,QAAQ,qBAAqB;YAC1C,MAAM,UAAU;YAChB,MAAM,sBAAsB,CAAC;YAC7B,IAAI,SAAS,CAAC,GAAG,UAAU,GAAG;gBAC5B,QAAQ,KAAK,CAAC,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;gBACnC,QAAQ,KAAK,CAAC,KAAK,GAAG;gBACtB,oBAAoB,CAAC,GAAG;YAC1B,OAAO,IAAI,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,UAAU,OAAO,UAAU,EAAE;gBAChE,QAAQ,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,EAAE,CAAC;gBACpC,QAAQ,KAAK,CAAC,IAAI,GAAG;gBACrB,oBAAoB,CAAC,GAAG;YAC1B,OAAO;gBACL,QAAQ,KAAK,CAAC,KAAK,GAAG;gBACtB,QAAQ,KAAK,CAAC,IAAI,GAAG,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;gBACtC,oBAAoB,CAAC,GAAG,SAAS,CAAC;YACpC;YACA,IAAI,SAAS,CAAC,GAAG,UAAU,GAAG;gBAC5B,QAAQ,KAAK,CAAC,MAAM,GAAG;gBACvB,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,QAAQ,EAAE,CAAC;gBAClC,oBAAoB,CAAC,GAAG;YAC1B,OAAO,IAAI,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG,UAAU,OAAO,WAAW,EAAE;gBAClE,QAAQ,KAAK,CAAC,GAAG,GAAG;gBACpB,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;gBACrC,oBAAoB,CAAC,GAAG;YAC1B,OAAO;gBACL,QAAQ,KAAK,CAAC,MAAM,GAAG;gBACvB,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;gBACrC,oBAAoB,CAAC,GAAG,SAAS,CAAC;YACpC;YACA,qCAAqC;gBAAE,GAAG,SAAS,CAAC;gBAAE,GAAG,SAAS,CAAC;YAAC;QACtE;iDACA;QAAC;QAAY;KAAqC;IAEpD,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,uCACE;;YAAM;YACR,MAAM;6DAAoB;oBACxB,MAAM,MAAM;oBACZ,IAAI,CAAC,KACH;oBACF,YAAY;oBACZ,WAAW,OAAO,EAAE;gBACtB;;YACA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;2CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;kCAAG;QAAC;QAA0B;KAAY;IAC1C,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;mCAAE;YACf,IAAI,CAAC,gBACH;YACF,MAAM,aAAa,wBAAwB,GAAG;YAC9C,IAAI,CAAC,YACH;YACF,IAAI,CAAC,eAAe,IAAI;2CAAC,CAAC,SAAW,OAAO,IAAI,KAAK;2CAAa;gBAChE,wBAAwB,KAAK;YAC/B;QACF;kCAAG;QAAC;QAAgB;KAAwB;IAC5C,MAAM,UAAU,gBAAgB,8BAA8B,GAAG,QAAQ,YAAY,SAAS,WAAW,CAAC;IAC1G,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,OAAO;QAAE,WAAW,uBAAuB,OAAO;QAAE,KAAK;IAAc,GAAG,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACvJ,YACA;QACE,KAAK;QACL,QAAQ,CAAC;YACP,YAAY;YACZ,WAAW,OAAO,EAAE;QACtB;IACF,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,OAAO;QAAE,WAAW,uBAAuB,IAAI;QAAE,qBAAqB,iBAAiB;IAAM,GAAG,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACtK,gBACA;QACE;QACA;QACA,SAAS;QACT;QACA,aAAa,CAAC,QAAQ;YACpB,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YACxC,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB;YACzC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,QAAQ;YAClD,iBAAiB;YACjB,IAAI,KAAK,eAAe,EAAE;gBACxB,MAAM,eAAe,oBAAoB;gBACzC,IAAI,CAAC,cAAc;oBACjB,OAAO,eAAe;gBACxB;gBACA,iBAAiB;YACnB;QACF;QACA;IACF,IACC,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACpC,8CACA;QACE;QACA;QACA,oBAAoB,iBAAiB;IACvC,IACC,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACpC,SACA;QACE,SAAS,WAAW;QACpB,KAAK;QACL,cAAc,QAAQ;IACxB,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,UACA;QACE,WAAW,uBAAuB,KAAK;QACvC,eAAe,iBAAiB;QAChC,cAAc,GAAG,QAAQ,YAAY,SAAS,WAAW,CAAC;QAC1D,gBAAgB;QAChB,UAAU,iBAAiB;QAC3B,SAAS;YACP,IAAI,WAAW,cAAc,OAAO,EAAE,YACpC;YACF,IAAI,OAAO;gBACT,WAAW;gBACX,mBAAmB,IAAI,CAAC;oBACtB,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;oBACxC,IAAI,YAAY,CAAC,MAAM,CAAC;oBACxB,IAAI,YAAY,CAAC,MAAM,CAAC;oBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI,QAAQ;gBACrC,GAAG,OAAO,CAAC,IAAM,WAAW;YAC9B,OAAO;gBACL,MAAM,eAAe,oBAAoB;gBACzC,IAAI,CAAC,cAAc;oBACjB,OAAO,eAAe;gBACxB;gBACA,iBAAiB;YACnB;QACF;IACF,GACA,SAAS,gBAAgB,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,SAAS,QAAQ,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EAAE,eAAe;AAI3I;IAxUI;MAAA;AAyUJ,IAAI,+CAA+C,CAAC,EAClD,UAAU,EACV,WAAW,EACX,kBAAkB,EACnB;;IACC,MAAM,WAAW,CAAA,GAAA,oPAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAe,AAAD,EAAE;IAC9D,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;kEAAE;YACf,IAAI,iBACF;YACF,mBAAmB;QACrB;iEAAG;QAAC;QAAU;KAAgB;IAC9B,CAAA,GAAA,4QAAA,CAAA,YAAgB,AAAD;kEAAE;YACf,IAAI,oBACF;YACF,IAAI,oBAAoB,UAAU;gBAChC;YACF;YACA,IAAI,eAAe,YAAY,GAAG,EAAE;gBAClC,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;gBACxC,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB;gBACzC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,QAAQ;YACpD;QACF;iEAAG;QACD;QACA;QACA,YAAY,GAAG;QACf;QACA;KACD;IACD,OAAO;AACT;IA/BI;;QAKe,oPAAA,CAAA,cAAW;;;MAL1B;AAgCJ,IAAI,gBAAgB;IAClB,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACxC,OACA;QACE,eAAe;QACf,QAAQ;QACR,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;IACjC,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,QACA;QACE,UAAU;QACV,UAAU;QACV,GAAG;QACH,MAAM;IACR;AAGN;MArBI;AAsBJ,IAAI,UAAU;IACZ,OAAO,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACxC,OACA;QACE,eAAe;QACf,QAAQ;QACR,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;IACjC,GACA,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAoB,AAAD,EACjC,QACA;QACE,UAAU;QACV,UAAU;QACV,GAAG;QACH,MAAM;IACR;AAGN;MArBI", "debugId": null}}]}