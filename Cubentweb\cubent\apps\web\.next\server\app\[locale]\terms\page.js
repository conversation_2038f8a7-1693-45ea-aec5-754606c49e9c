(()=>{var e={};e.id=4477,e.ids=[4477],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},8142:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l});var s=t(57864),a=t(94327),i=t(73391),n=t.n(i),o=t(17984),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let l={children:["",{children:["[locale]",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92164)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\terms\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\terms\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/terms/page",pathname:"/[locale]/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89259:()=>{},91102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a93fb4bd742ae20ac4f2efc682a9dd8b873c54ce":()=>s.z2,"40f08c3d29a2f32c36fa37e5a9ba2c32897f96adb3":()=>a.x,"600ae2011570e2df1d46454ad223098fc4baa55559":()=>s.qr,"60800baff73db42f3be2da01face57da531e4ef986":()=>s.q2,"608b2f8eca36791b674ce9740d60d899091a017080":()=>s.xK});var s=t(22589),a=t(18362)},92164:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>a});var s=t(94752);t(23233);let a={title:"Terms of Service | Cubent",description:"Terms of Service for Cubent AI coding assistant"};function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-950/20 via-black to-orange-900/10 text-white",children:(0,s.jsx)("div",{className:"container mx-auto px-6 py-20 max-w-4xl",children:(0,s.jsxs)("div",{className:"bg-black/40 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-8 md:p-12",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold mb-6 bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent",children:"Terms of Service"}),(0,s.jsx)("p",{className:"text-orange-300/80 mb-12 text-lg",children:"Last Updated: January 2, 2025"}),(0,s.jsxs)("div",{className:"space-y-8 text-gray-100",children:[(0,s.jsxs)("div",{className:"bg-orange-500/10 border border-orange-500/30 rounded-xl p-6",children:[(0,s.jsx)("p",{className:"text-lg leading-relaxed text-orange-100 mb-4",children:'Welcome to Cubent, an AI-powered coding assistant. These Terms of Service ("Terms") govern your access to and use of Cubent\'s software platform, APIs, documentation, and related tools (collectively, the "Service"). By using the Service, you agree to these Terms.'}),(0,s.jsxs)("p",{className:"text-orange-200 leading-relaxed",children:["Please also read our ",(0,s.jsx)("a",{href:"/privacy",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"Privacy Policy"}),", which explains how we collect, use, disclose, and process personal data."]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"1. Access and Use"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"1.1 Provision of Access"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"Cubent is an AI-powered coding assistant that provides intelligent code suggestions, completions, and development tools to help developers write code more efficiently. Subject to your compliance with these Terms, Cubent grants you a limited right to access and use the Service."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"1.2 Content"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:'You may provide inputs to the Service ("Inputs") and receive code suggestions, outputs, or other functions based on your Inputs (collectively, "Suggestions"). Inputs and Suggestions are collectively referred to as "Content". We may use Content to provide the Service, comply with applicable law, enforce our terms and policies, and keep the Service safe.'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"1.3 Model Training"}),(0,s.jsx)("div",{className:"bg-orange-500/10 border border-orange-500/30 rounded-xl p-6",children:(0,s.jsxs)("p",{className:"text-orange-100 leading-relaxed",children:[(0,s.jsx)("strong",{children:"CUBENT WILL NOT USE CONTENT TO TRAIN, OR ALLOW ANY THIRD PARTY TO TRAIN, ANY AI MODELS, UNLESS YOU'VE EXPLICITLY AGREED TO THE USE OF CONTENT FOR TRAINING."}),"You can manage your preferences regarding the use of Inputs and Suggestions for training in your account settings."]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"1.4 Limitations for Suggestions"}),(0,s.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed",children:"You acknowledge that Suggestions are generated automatically by AI technology and may be similar to suggestions provided to other users. You understand that AI models have limitations, including:"}),(0,s.jsxs)("ul",{className:"list-disc pl-6 space-y-2 text-gray-300 mb-4",children:[(0,s.jsx)("li",{children:"Suggestions may contain errors or misleading information"}),(0,s.jsx)("li",{children:"AI models may produce repetitive or formulaic content"}),(0,s.jsx)("li",{children:"AI models may struggle with complex reasoning and decision-making"}),(0,s.jsx)("li",{children:"Training data may contain biases or inaccuracies"})]}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"You agree that you are responsible for evaluating and bearing all risks associated with using any Suggestions, including reliance on their accuracy, completeness, or usefulness."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"1.5 Use Restrictions"}),(0,s.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed",children:"You may not:"}),(0,s.jsxs)("ul",{className:"list-disc pl-6 space-y-2 text-gray-300",children:[(0,s.jsx)("li",{children:"Reverse engineer, disassemble, or attempt to derive the source code of the Service"}),(0,s.jsx)("li",{children:"Reproduce, modify, or create derivative works of the Service"}),(0,s.jsx)("li",{children:"Rent, lease, lend, or sell the Service"}),(0,s.jsx)("li",{children:"Use the Service to develop competing AI models or engage in model extraction"}),(0,s.jsx)("li",{children:"Probe, scan, or attempt to penetrate the Service's security"}),(0,s.jsx)("li",{children:"Use the Service in ways that violate applicable laws or third-party rights"}),(0,s.jsx)("li",{children:"Provide regulated data subject to specific legal protections (e.g., HIPAA, PCI DSS)"})]})]})]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"2. Eligibility"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"You must be at least 18 years old or the age of majority in your jurisdiction to use the Service. By agreeing to these Terms, you represent that you meet these age requirements and have not previously been suspended or removed from the Service."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"3. Account Registration"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"To access most features of the Service, you must register for an account. You agree to provide accurate, complete, and current information and to keep your account information up to date. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"4. Payment Terms"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"4.1 Paid Services"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"Certain features of the Service may require payment of fees. All fees are in U.S. Dollars and are non-refundable except as required by law. We reserve the right to change pricing with advance notice."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"4.2 Subscription Service"}),(0,s.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Subscription plans automatically renew for successive periods unless cancelled. You must cancel at least 24 hours before renewal to avoid being charged for the next period. You can cancel through your account settings or by contacting us at"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"<EMAIL>"}),"."]})]})]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"5. Ownership and Licenses"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"5.1 Service Ownership"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"Cubent and its licensors own all rights to the Service, including all improvements and intellectual property rights. No implied licenses are granted under these Terms."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"5.2 Your Content"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"You retain all rights to your Inputs. Cubent assigns to you all rights in any Suggestions generated by the Service."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium mb-3 text-orange-200",children:"5.3 Feedback"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"If you provide feedback about the Service, you grant Cubent the right to use that feedback without restriction or compensation."})]})]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"6. Third-Party Services"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"The Service may include optional third-party services such as extensions and plugins. Your use of third-party services is subject to their respective terms, and Cubent makes no representations or warranties regarding such services."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"7. Termination"}),(0,s.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["You may stop using the Service at any time. We may suspend or terminate your access for violations of these Terms or other reasons. Upon termination, we may delete Content associated with your account. If you believe your account was terminated in error, contact us at ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"<EMAIL>"}),"."]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"8. Privacy"}),(0,s.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Please read our ",(0,s.jsx)("a",{href:"/privacy",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"Privacy Policy"}),"for information about how we collect, use, and protect your personal data."]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"9. Disclaimers"}),(0,s.jsx)("div",{className:"bg-red-900/20 border border-red-600/30 rounded-xl p-6",children:(0,s.jsxs)("p",{className:"text-red-100 leading-relaxed",children:[(0,s.jsx)("strong",{children:'THE SERVICE AND SUGGESTIONS ARE PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND.'}),"Cubent disclaims all warranties, including implied warranties of merchantability, fitness for a particular purpose, and non-infringement. We do not warrant that the Service will be uninterrupted, secure, or error-free."]})})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"10. Limitation of Liability"}),(0,s.jsx)("div",{className:"bg-red-900/20 border border-red-600/30 rounded-xl p-6",children:(0,s.jsxs)("p",{className:"text-red-100 leading-relaxed",children:[(0,s.jsx)("strong",{children:"TO THE FULLEST EXTENT PERMITTED BY LAW, CUBENT'S LIABILITY IS LIMITED TO THE GREATER OF:"}),"(A) the amount you paid for the Service in the six months prior to the claim, or (B) $100. Cubent will not be liable for indirect, incidental, special, consequential, or punitive damages."]})})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"11. Dispute Resolution"}),(0,s.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Any disputes will be resolved through binding arbitration rather than in court, except for small claims court matters. You may opt out of arbitration within 30 days of account creation by emailing ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"<EMAIL>"}),"."]})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"12. General Terms"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:"These Terms constitute the entire agreement between you and Cubent regarding the Service. California law governs these Terms. We may update these Terms from time to time with notice."})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-semibold mb-6 text-orange-300",children:"13. Contact Information"}),(0,s.jsxs)("div",{className:"bg-orange-500/10 border border-orange-500/30 rounded-xl p-6",children:[(0,s.jsx)("p",{className:"text-orange-100 mb-4 leading-relaxed",children:"For questions about these Terms, please contact us at:"}),(0,s.jsxs)("div",{className:"space-y-2 text-orange-200",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"text-orange-300 font-medium",children:"Email:"})," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"<EMAIL>"})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"text-orange-300 font-medium",children:"Legal:"})," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300",children:"<EMAIL>"})]})]})]})]})]})]})})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,3396,415,2644,365,9752,6270],()=>t(8142));module.exports=s})();