{"greeting": "Hi, I'm <PERSON><PERSON><PERSON>!", "introduction": "<strong><PERSON><PERSON><PERSON> is the premiere autonomous coding agent.</strong> Get ready to architect, code, debug, and boost your productivity like you've never seen before. You can configure an API key now or skip and set it up later.", "notice": "To get started, this extension needs an API provider.", "start": "Continue", "chooseProvider": "Choose an API provider to get started:", "routers": {"requesty": {"description": "Your optimized LLM router", "incentive": "$1 free credit"}, "openrouter": {"description": "A unified interface for LLMs"}}, "startRouter": "Express Setup Through a Router", "startCustom": "Bring Your Own API Key", "telemetry": {"title": "Help Improve cubent Code", "anonymousTelemetry": "Send anonymous error and usage data to help us fix bugs and improve the extension. No code, prompts, or personal information is ever sent.", "changeSettings": "You can always change this at the bottom of the <settingsLink>settings</settingsLink>", "settings": "settings", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "or": "or", "importSettings": "Import Settings"}