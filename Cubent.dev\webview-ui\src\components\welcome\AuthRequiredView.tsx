import { useState, useEffect } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { User, Shield, AlertTriangle, ExternalLink } from "lucide-react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuthStore, useIsAuthenticated, useIsAuthenticating, useAuthError } from "@src/stores/authStore"
import { Button } from "@src/components/ui"

const AuthRequiredView = () => {
	const { t } = useAppTranslation()
	const isAuthenticated = useIsAuthenticated()
	const isAuthenticating = useIsAuthenticating()
	const authError = useAuthError()
	const [showRetry, setShowRetry] = useState(false)
	const [imagesBaseUri] = useState(() => {
		const w = window as any
		return w.IMAGES_BASE_URI || ""
	})

	useEffect(() => {
		if (authError) {
			setShowRetry(true)
		}
	}, [authError])

	const handleSignIn = () => {
		setShowRetry(false)
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleRetry = () => {
		setShowRetry(false)
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	if (isAuthenticated) {
		return null // This component should not render when authenticated
	}

	return (
		<div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
			<div className="max-w-md mx-auto space-y-8">
				{/* Authentication Required Title */}
				<h2 className="text-xl font-semibold text-foreground">Authentication Required</h2>

				{/* Description */}
				<div className="space-y-4 text-muted-foreground text-center">
					<p className="text-base leading-relaxed">
						To use Cubent Coder, you need to sign in with your account. This ensures secure access to AI
						models and your personalized settings.
					</p>
					<p className="text-sm leading-relaxed">
						Your authentication will be saved securely and persist across VS Code sessions until you
						manually sign out.
					</p>
				</div>

				{/* Error Message */}
				{authError && (
					<div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
						<div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
							<AlertTriangle className="w-5 h-5" />
							<span className="font-medium">Authentication Failed</span>
						</div>
						<p className="mt-2 text-sm text-red-600 dark:text-red-300">{authError}</p>
					</div>
				)}

				{/* Sign In Button */}
				<div className="space-y-3">
					{!isAuthenticating && !showRetry && (
						<Button
							onClick={handleSignIn}
							className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
							<User className="w-5 h-5 mr-2" />
							Sign In to Continue
						</Button>
					)}

					{isAuthenticating && (
						<div className="space-y-3">
							<Button
								disabled
								className="w-full py-3 px-6 bg-gray-400 text-white font-medium rounded-lg cursor-not-allowed">
								<div className="flex items-center justify-center space-x-2">
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
									<span>Authenticating...</span>
								</div>
							</Button>
							<p className="text-sm text-muted-foreground">
								Please complete the authentication in your browser
							</p>
						</div>
					)}

					{showRetry && (
						<Button
							onClick={handleRetry}
							className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
							Try Again
						</Button>
					)}
				</div>

				{/* Help Text */}
				<div className="pt-4 border-t border-border">
					<p className="text-sm text-muted-foreground">
						Need help? Visit our{" "}
						<button
							onClick={() => vscode.postMessage({ type: "openExternal", url: "https://docs.cubent.com" })}
							className="text-blue-600 dark:text-blue-400 hover:underline inline-flex items-center">
							documentation
							<ExternalLink className="w-3 h-3 ml-1" />
						</button>
					</p>
				</div>
			</div>
		</div>
	)
}

export default AuthRequiredView
