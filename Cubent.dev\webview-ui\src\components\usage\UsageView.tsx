import React, { useState, useEffect } from "react"
import { Bar<PERSON>hart3, Refresh<PERSON>w, Download, Upload, Trash2, <PERSON>ertCircle, CheckCircle } from "lucide-react"

import { Tab, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabContent } from "../common/Tab"
import { Button } from "../ui/button"
import { useAuthStore } from "../../stores/authStore"
import {
	getUsageStats,
	getUsageStatsForPeriod,
	getUsageByModel,
	clearUsageStats,
	exportUsageData,
	importUsageData,
	cleanupOldData,
} from "../../utils/usage-tracking"
import { vscode } from "../../utils/vscode"

interface UsageViewProps {
	onDone: () => void
}

interface UsageEntry {
	timestamp: number
	modelId: string
	cubentUnits: number
	messageCount: number
	provider: string
	configName: string
}

interface UsageStats {
	totalCubentUnits: number
	totalMessages: number
	entries: UsageEntry[]
	lastUpdated: number
}

export const UsageView: React.FC<UsageViewProps> = ({ onDone }) => {
	// Get authentication state from the auth store
	const { isAuthenticated, hasActiveSession, userInfo } = useAuthStore()

	const [usageStats, setUsageStats] = useState<UsageStats>({
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	})
	const [serverUsageStats, setServerUsageStats] = useState<{
		totalCubentUnits: number
		totalMessages: number
		userLimit: number
		isAuthenticated: boolean
	}>({
		totalCubentUnits: 0,
		totalMessages: 0,
		userLimit: 50,
		isAuthenticated: false,
	})
	const [selectedPeriod, setSelectedPeriod] = useState<number | null>(null) // null = all time
	const [modelBreakdown, setModelBreakdown] = useState<Record<string, { cubentUnits: number; messages: number }>>({})
	const [isLoading, setIsLoading] = useState(false)
	const [debugInfo, setDebugInfo] = useState<string>("")

	// Get current user ID for user-specific data
	const getCurrentUserId = (): string | null => {
		if (isAuthenticated && hasActiveSession && userInfo?.email) {
			return userInfo.email
		}
		return null
	}

	// Load usage stats from localStorage using user-specific functions
	const loadUsageStats = () => {
		console.log("UsageView: Loading usage stats...")

		try {
			const currentUserId = getCurrentUserId()
			console.log("UsageView: currentUserId =", currentUserId)

			// Use the user-specific functions from usage-tracking.ts
			const stats = selectedPeriod
				? getUsageStatsForPeriod(selectedPeriod, currentUserId || undefined)
				: getUsageStats(currentUserId || undefined)

			console.log("UsageView: loaded stats =", stats)
			setUsageStats(stats)

			// Calculate model breakdown using user-specific function
			const breakdown = getUsageByModel(selectedPeriod || undefined, currentUserId || undefined)
			console.log("UsageView: model breakdown =", breakdown)
			setModelBreakdown(breakdown)
		} catch (error) {
			console.error("UsageView: Error loading usage stats:", error)
			setUsageStats({
				totalCubentUnits: 0,
				totalMessages: 0,
				entries: [],
				lastUpdated: Date.now(),
			})
			setModelBreakdown({})
		}
	}

	// Load stats on component mount and when period changes
	useEffect(() => {
		// Clean up old mixed data for authenticated users
		if (isAuthenticated && hasActiveSession) {
			cleanupOldData()
		}
		loadUsageStats()
		loadServerUsageStats()
	}, [selectedPeriod, isAuthenticated, hasActiveSession])

	// Update server usage stats when authentication state changes
	useEffect(() => {
		// Update the authentication status in serverUsageStats
		setServerUsageStats((prev) => ({
			...prev,
			isAuthenticated: isAuthenticated && hasActiveSession,
		}))

		// Reload server stats when auth state changes
		if (isAuthenticated && hasActiveSession) {
			loadServerUsageStats()
		}
	}, [isAuthenticated, hasActiveSession])

	// Function to load server-based usage stats
	const loadServerUsageStats = async () => {
		try {
			const currentUserId = getCurrentUserId()
			// Always get local stats first using user-specific function
			const localStats = getUsageStats(currentUserId || undefined)

			if (isAuthenticated && hasActiveSession) {
				// Request server stats from extension for authenticated users
				console.log("📊 Requesting server usage stats from extension...")
				vscode.postMessage({ type: "getServerUsageStats" })

				// Set local stats as fallback while waiting for server response
				setServerUsageStats({
					totalCubentUnits: localStats.totalCubentUnits,
					totalMessages: localStats.totalMessages,
					userLimit: 50,
					isAuthenticated: true,
				})
			} else {
				// Use local data when not authenticated (no user ID)
				setServerUsageStats({
					totalCubentUnits: localStats.totalCubentUnits,
					totalMessages: localStats.totalMessages,
					userLimit: 50,
					isAuthenticated: false,
				})
				console.log("📊 Using local stats (not authenticated):", {
					totalCubentUnits: localStats.totalCubentUnits,
					totalMessages: localStats.totalMessages,
				})
			}
		} catch (error) {
			console.error("Failed to load server usage stats:", error)
		}
	}

	// Listen for localStorage changes and set up periodic refresh
	useEffect(() => {
		const handleStorageChange = (e: StorageEvent) => {
			// Listen for any cubent usage stats changes (user-specific keys)
			if (e.key && e.key.startsWith("cubent-usage-stats")) {
				console.log("UsageView: localStorage changed, refreshing data")
				loadUsageStats()
			}
		}

		// Listen for messages from extension
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			if (message.type === "serverUsageStats") {
				console.log("UsageView: Received server usage stats:", message.data)

				// Only use server data if it has meaningful values, otherwise keep local data
				const hasServerData = message.data.totalCubentUnits > 0 || message.data.totalMessages > 0

				if (hasServerData) {
					setServerUsageStats((prev) => ({
						...message.data,
						// Override with actual auth state from store
						isAuthenticated: isAuthenticated && hasActiveSession,
					}))
					console.log("📊 Using server stats (has data):", message.data)
				} else {
					console.log("📊 Server returned empty data, keeping local stats")
					// Keep current local stats but update auth status
					setServerUsageStats((prev) => ({
						...prev,
						isAuthenticated: isAuthenticated && hasActiveSession,
					}))
				}
			} else if (message.type === "usageTrackingStatus") {
				console.log("🔍 Usage tracking status:", message.data)
				setDebugInfo(`Status: ${JSON.stringify(message.data, null, 2)}`)
			} else if (message.type === "usageQueueProcessed") {
				console.log("🔄 Usage queue processed:", message.data)
				setDebugInfo(`Queue processed: ${message.data.processed} items, ${message.data.remaining} remaining`)
				// Refresh stats after processing
				loadServerUsageStats()
			}
		}

		// Listen for storage events (from other tabs/windows)
		window.addEventListener("storage", handleStorageChange)
		window.addEventListener("message", handleMessage)

		// Also set up a periodic refresh to catch changes in the same tab
		const interval = setInterval(() => {
			loadUsageStats()
			loadServerUsageStats()
		}, 2000) // Refresh every 2 seconds

		return () => {
			window.removeEventListener("storage", handleStorageChange)
			window.removeEventListener("message", handleMessage)
			clearInterval(interval)
		}
	}, [selectedPeriod])

	// Refresh stats
	const handleRefresh = () => {
		setIsLoading(true)
		setTimeout(() => {
			loadUsageStats()
			setIsLoading(false)
		}, 100)
	}

	// Clear all usage data
	const handleClearData = () => {
		if (window.confirm("Are you sure you want to clear all usage data? This action cannot be undone.")) {
			try {
				const currentUserId = getCurrentUserId()
				clearUsageStats(currentUserId || undefined) // This will clear data for the current user
				loadUsageStats()
			} catch (error) {
				console.error("Failed to clear usage stats:", error)
			}
		}
	}

	// Export usage data
	const handleExport = () => {
		try {
			const currentUserId = getCurrentUserId()
			const data = exportUsageData(currentUserId || undefined) // This will export data for the current user
			const blob = new Blob([data], { type: "application/json" })
			const url = URL.createObjectURL(blob)
			const a = document.createElement("a")
			a.href = url
			a.download = `cubent-usage-${new Date().toISOString().split("T")[0]}.json`
			document.body.appendChild(a)
			a.click()
			document.body.removeChild(a)
			URL.revokeObjectURL(url)
		} catch (error) {
			console.error("Failed to export usage data:", error)
		}
	}

	// Import usage data
	const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (file) {
			const reader = new FileReader()
			reader.onload = (e) => {
				const content = e.target?.result as string
				if (content) {
					try {
						const currentUserId = getCurrentUserId()
						if (importUsageData(content, currentUserId || undefined)) {
							loadUsageStats()
							alert("Usage data imported successfully!")
						} else {
							alert("Invalid file format. Please check the file structure.")
						}
					} catch (error) {
						console.error("Failed to import usage data:", error)
						alert("Failed to import usage data. Please check the file format.")
					}
				}
			}
			reader.readAsText(file)
		}
		// Reset the input
		event.target.value = ""
	}

	// Format date
	const formatDate = (timestamp: number) => {
		return new Date(timestamp).toLocaleDateString()
	}

	// Get top models by usage
	const topModels = Object.entries(modelBreakdown)
		.sort(([, a], [, b]) => b.cubentUnits - a.cubentUnits)
		.slice(0, 5)

	// DEBUG: Get usage tracking status
	const checkTrackingStatus = () => {
		console.log("🔍 CHECKING: Usage tracking status...")
		setDebugInfo("Checking status...")
		vscode.postMessage({
			type: "getUsageTrackingStatus",
			messageId: `status-${Date.now()}`,
		})
	}

	// DEBUG: Force process usage queue
	const forceProcessQueue = () => {
		console.log("🔄 FORCING: Process usage queue...")
		setDebugInfo("Processing queue...")
		vscode.postMessage({
			type: "forceProcessUsageQueue",
		})
	}

	// DEBUG: Test server tracking
	const testServerTracking = () => {
		console.log("🧪 TESTING: Server tracking...")
		setDebugInfo("Testing server...")
		vscode.postMessage({
			type: "trackUserUsage",
			data: {
				modelId: "test-model",
				provider: "test-provider",
				configName: "test-config",
				messageCount: 1,
				timestamp: Date.now(),
			},
		})
	}

	return (
		<Tab>
			<TabHeader className="flex justify-between items-center">
				<div className="flex items-center gap-2">
					<BarChart3 className="w-4 h-4" />
					<h3 className="text-vscode-foreground m-0">Cubent Units Usage</h3>
				</div>
				<Button onClick={onDone}>Done</Button>
			</TabHeader>

			<TabContent>
				<div className="space-y-6">
					{/* Overview Stats */}
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<h4 className="text-sm font-medium text-vscode-foreground">Usage Overview</h4>
							<div className="flex items-center gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={handleRefresh}
									disabled={isLoading}
									className="flex items-center gap-1">
									<RefreshCw className={`w-3 h-3 ${isLoading ? "animate-spin" : ""}`} />
									Refresh
								</Button>
							</div>
						</div>

						{/* Period Selector */}
						<div className="flex items-center gap-2 text-sm">
							<span className="text-vscode-descriptionForeground">Time period:</span>
							<select
								value={selectedPeriod || "all"}
								onChange={(e) =>
									setSelectedPeriod(e.target.value === "all" ? null : parseInt(e.target.value))
								}
								className="bg-vscode-input-background text-vscode-input-foreground border border-vscode-input-border rounded px-2 py-1">
								<option value="all">All time</option>
								<option value="1">Last 24 hours</option>
								<option value="7">Last 7 days</option>
								<option value="30">Last 30 days</option>
							</select>
						</div>

						{/* Authentication Status */}
						<div className="mb-4">
							<div
								className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
									isAuthenticated && hasActiveSession
										? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
										: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
								}`}>
								{isAuthenticated && hasActiveSession
									? "✅ User-based tracking (synced across devices)"
									: "⚠️ Local tracking only (sign in for user-based tracking)"}
							</div>
						</div>

						{/* Stats Cards */}
						<div className="grid grid-cols-2 gap-4">
							<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
								<div className="text-2xl font-bold text-vscode-foreground">
									{isAuthenticated && hasActiveSession
										? serverUsageStats.totalCubentUnits.toFixed(2)
										: usageStats.totalCubentUnits.toFixed(2)}
								</div>
								<div className="text-sm text-vscode-descriptionForeground">
									{isAuthenticated && hasActiveSession ? "Server Cubent Units" : "Local Cubent Units"}
								</div>
								{isAuthenticated && hasActiveSession && (
									<div className="text-xs text-vscode-descriptionForeground mt-1">
										Limit: {serverUsageStats.userLimit}
									</div>
								)}
							</div>
							<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
								<div className="text-2xl font-bold text-vscode-foreground">
									{isAuthenticated && hasActiveSession
										? serverUsageStats.totalMessages.toLocaleString()
										: usageStats.totalMessages.toLocaleString()}
								</div>
								<div className="text-sm text-vscode-descriptionForeground">
									{isAuthenticated && hasActiveSession ? "Server Messages" : "Local Messages"}
								</div>
							</div>
						</div>

						{/* Top Models */}
						{topModels.length > 0 && (
							<div>
								<h5 className="text-sm font-medium text-vscode-foreground mb-2">Top Models by Usage</h5>
								<div className="space-y-2">
									{topModels.map(([modelId, stats]) => (
										<div key={modelId} className="flex items-center justify-between text-sm">
											<span className="text-vscode-foreground font-mono">{modelId}</span>
											<div className="flex items-center gap-4 text-vscode-descriptionForeground">
												<span>{stats.cubentUnits.toFixed(2)} units</span>
												<span>{stats.messages} msgs</span>
											</div>
										</div>
									))}
								</div>
							</div>
						)}

						{/* Last Updated */}
						{usageStats.lastUpdated && (
							<div className="text-xs text-vscode-descriptionForeground">
								Last updated: {formatDate(usageStats.lastUpdated)}
							</div>
						)}
					</div>

					{/* Debug Tools */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium text-vscode-foreground">Debug Tools</h4>

						<div className="flex items-center gap-2 flex-wrap">
							<Button
								variant="outline"
								size="sm"
								onClick={testServerTracking}
								className="flex items-center gap-1 text-blue-600 hover:text-blue-700">
								🧪 Test Server
							</Button>

							<Button
								variant="outline"
								size="sm"
								onClick={checkTrackingStatus}
								className="flex items-center gap-1 text-green-600 hover:text-green-700">
								🔍 Check Status
							</Button>

							<Button
								variant="outline"
								size="sm"
								onClick={forceProcessQueue}
								className="flex items-center gap-1 text-orange-600 hover:text-orange-700">
								🔄 Force Sync
							</Button>
						</div>

						{debugInfo && (
							<div className="bg-vscode-editor-background border border-vscode-panel-border rounded p-3">
								<div className="text-xs font-mono text-vscode-foreground whitespace-pre-wrap">
									{debugInfo}
								</div>
							</div>
						)}

						<div className="text-xs text-vscode-descriptionForeground">
							Use these tools to debug usage tracking issues. Check console for detailed logs.
						</div>
					</div>

					{/* Data Management */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium text-vscode-foreground">Data Management</h4>

						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={handleExport}
								className="flex items-center gap-1">
								<Download className="w-3 h-3" />
								Export Data
							</Button>

							<label className="cursor-pointer">
								<Button variant="outline" size="sm" className="flex items-center gap-1" asChild>
									<span>
										<Upload className="w-3 h-3" />
										Import Data
									</span>
								</Button>
								<input type="file" accept=".json" onChange={handleImport} className="hidden" />
							</label>

							<Button
								variant="outline"
								size="sm"
								onClick={handleClearData}
								className="flex items-center gap-1 text-red-600 hover:text-red-700">
								<Trash2 className="w-3 h-3" />
								Clear Data
							</Button>
						</div>

						<div className="text-xs text-vscode-descriptionForeground">
							Usage data is stored locally in your browser. Export your data to back it up or transfer it
							to another device.
						</div>
					</div>
				</div>
			</TabContent>
		</Tab>
	)
}
