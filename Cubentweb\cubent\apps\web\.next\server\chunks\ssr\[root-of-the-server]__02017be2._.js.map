{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/components/sidebar.tsx"], "sourcesContent": ["import { capitalize } from '@repo/design-system/lib/utils';\nimport type { ReactNode } from 'react';\n\ntype SidebarProperties = {\n  readonly date: Date;\n  readonly readingTime: string;\n  readonly tags?: string[];\n  readonly toc?: ReactNode;\n};\n\nexport const Sidebar = async ({\n  date,\n  readingTime,\n  tags,\n  toc: Toc,\n}: SidebarProperties) => (\n  <div className=\"col-span-4 flex w-72 flex-col items-start gap-8 border-foreground/10 border-l px-6 lg:col-span-2\">\n    <div className=\"grid gap-2\">\n      <p className=\"text-muted-foreground text-sm\">Published</p>\n      <p className=\"rounded-sm text-foreground text-sm\">\n        {new Intl.DateTimeFormat('en-US', {\n          month: 'short',\n          day: 'numeric',\n          year: 'numeric',\n          timeZone: 'America/New_York',\n        }).format(date)}\n      </p>\n    </div>\n    <div className=\"grid gap-2\">\n      <p className=\"text-muted-foreground text-sm\">Reading Time</p>\n      <p className=\"rounded-sm text-foreground text-sm\">{readingTime}</p>\n    </div>\n    {tags && (\n      <div className=\"grid gap-2\">\n        <p className=\"text-muted-foreground text-sm\">Tags</p>\n        <p className=\"rounded-sm text-foreground text-sm\">\n          {tags.map(capitalize).join(', ')}\n        </p>\n      </div>\n    )}\n    {Toc ? (\n      <div className=\"-mx-2\">\n        <div className=\"grid gap-2 p-2\">\n          <p className=\"text-muted-foreground text-sm\">Sections</p>\n          {Toc}\n        </div>\n      </div>\n    ) : undefined}\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;;;AAUO,MAAM,UAAU,OAAO,EAC5B,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,KAAK,GAAG,EACU,iBAClB,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCACV,IAAI,KAAK,cAAc,CAAC,SAAS;4BAChC,OAAO;4BACP,KAAK;4BACL,MAAM;4BACN,UAAU;wBACZ,GAAG,MAAM,CAAC;;;;;;;;;;;;0BAGd,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;YAEpD,sBACC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6VAAC;wBAAE,WAAU;kCACV,KAAK,GAAG,CAAC,4IAAA,CAAA,aAAU,EAAE,IAAI,CAAC;;;;;;;;;;;;YAIhC,oBACC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAE,WAAU;sCAAgC;;;;;;wBAC5C;;;;;;;;;;;uBAGH", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/body.tsx"], "sourcesContent": ["import { RichText } from '../.basehub/react-rich-text';\n\nexport const Body = RichText;\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,MAAM,OAAO,iQAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/toc.tsx"], "sourcesContent": ["import { RichText } from '../.basehub/react-rich-text';\nimport type { ComponentProps } from 'react';\n\ntype TableOfContentsProperties = Omit<\n  ComponentProps<typeof RichText>,\n  'children'\n> & {\n  readonly data: ComponentProps<typeof RichText>['children'];\n};\n\nexport const TableOfContents = ({\n  data,\n  ...props\n}: TableOfContentsProperties) => (\n  <div>\n    <RichText\n      // @ts-expect-error \"idk\"\n      components={{\n        ol: ({ children }) => (\n          <ol className=\"flex list-none flex-col gap-2 text-sm\">{children}</ol>\n        ),\n        ul: ({ children }) => (\n          <ul className=\"flex list-none flex-col gap-2 text-sm\">{children}</ul>\n        ),\n        li: ({ children }) => <li className=\"pl-3\">{children}</li>,\n        a: ({ children, href }) => (\n          <a\n            className=\"line-clamp-3 flex rounded-sm text-foreground text-sm underline decoration-foreground/0 transition-colors hover:decoration-foreground/50\"\n            href={`#${href?.split('#').at(1)}`}\n          >\n            {children}\n          </a>\n        ),\n      }}\n      {...props}\n    >\n      {data}\n    </RichText>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAUO,MAAM,kBAAkB,CAAC,EAC9B,IAAI,EACJ,GAAG,OACuB,iBAC1B,6VAAC;kBACC,cAAA,6VAAC,iQAAA,CAAA,WAAQ;YACP,yBAAyB;YACzB,YAAY;gBACV,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6VAAC;wBAAG,WAAU;kCAAyC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6VAAC;wBAAG,WAAU;kCAAyC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6VAAC;wBAAG,WAAU;kCAAQ;;;;;;gBAC5C,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,6VAAC;wBACC,WAAU;wBACV,MAAM,CAAC,CAAC,EAAE,MAAM,MAAM,KAAK,GAAG,IAAI;kCAEjC;;;;;;YAGP;YACC,GAAG,KAAK;sBAER", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'next-forge';\nconst author: Metadata['authors'] = {\n  name: 'Vercel',\n  url: 'https://vercel.com/',\n};\nconst publisher = 'Vercel';\nconst twitterHandle = '@vercel';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/legal/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Sidebar } from '@/components/sidebar';\nimport { legal } from '@repo/cms';\nimport { Body } from '@repo/cms/components/body';\nimport { Feed } from '@repo/cms/components/feed';\nimport { TableOfContents } from '@repo/cms/components/toc';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\n\ntype LegalPageProperties = {\n  readonly params: Promise<{\n    slug: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: LegalPageProperties): Promise<Metadata> => {\n  const { slug } = await params;\n  const post = await legal.getPost(slug);\n\n  if (!post) {\n    return {};\n  }\n\n  return createMetadata({\n    title: post._title,\n    description: post.description,\n  });\n};\n\nexport const generateStaticParams = async (): Promise<{ slug: string }[]> => {\n  const posts = await legal.getPosts();\n\n  return posts.map(({ _slug }) => ({ slug: _slug }));\n};\n\nconst LegalPage = async ({ params }: LegalPageProperties) => {\n  const { slug } = await params;\n\n  return (\n    <Feed queries={[legal.postQuery(slug)]}>\n      {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n      {async ([data]) => {\n        'use server';\n\n        const page = data.legalPages.item;\n\n        if (!page) {\n          notFound();\n        }\n\n        return (\n          <div className=\"min-h-screen bg-gradient-to-b from-orange-50/30 to-transparent\">\n            <div className=\"container mx-auto max-w-6xl py-16 px-4\">\n              <div className=\"flex flex-col items-start gap-8 lg:flex-row\">\n              <div className=\"flex-1 max-w-none lg:max-w-3xl mx-auto lg:mx-0\">\n                <div className=\"prose prose-neutral dark:prose-invert max-w-none\">\n                  <Body content={page.body.json.content} />\n                </div>\n              </div>\n              <div className=\"sticky top-24 hidden shrink-0 lg:block lg:w-64\">\n                <Sidebar\n                  toc={<TableOfContents data={page.body.json.toc} />}\n                  readingTime={`${page.body.readingTime} min read`}\n                  date={new Date()}\n                />\n              </div>\n            </div>\n            </div>\n          </div>\n        );\n      }}\n    </Feed>\n  );\n};\n\nexport default LegalPage;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAAA;;;;;;;;;;;AAQO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACc;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,wHAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAEjC,IAAI,CAAC,MAAM;QACT,OAAO,CAAC;IACV;IAEA,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;QACpB,OAAO,KAAK,MAAM;QAClB,aAAa,KAAK,WAAW;IAC/B;AACF;AAEO,MAAM,uBAAuB;IAClC,MAAM,QAAQ,MAAM,wHAAA,CAAA,QAAK,CAAC,QAAQ;IAElC,OAAO,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,MAAM;QAAM,CAAC;AAClD;MAQO,gDAAO,CAAC,KAAK;IAGZ,MAAM,OAAO,KAAK,UAAU,CAAC,IAAI;IAEjC,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,oSAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACf,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,sIAAA,CAAA,OAAI;gCAAC,SAAS,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;kCAGzC,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,qIAAA,CAAA,UAAO;4BACN,mBAAK,6VAAC,qIAAA,CAAA,kBAAe;gCAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG;;;;;;4BAC9C,aAAa,GAAG,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;4BAChD,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;AAnCN,MAAM,YAAY,OAAO,EAAE,MAAM,EAAuB;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,qBACE,6VAAC,sLAAA,CAAA,OAAI;QAAC,SAAS;YAAC,wHAAA,CAAA,QAAK,CAAC,SAAS,CAAC;SAAM;kBAEnC,8VAAA;;;;;;AAgCP;uCAEe", "debugId": null}}]}