# Test Show Diff Functionality

This file is to test the new "Show diff" button functionality.

## What was implemented:

1. **Changed "Open file" button to "Show diff" button** for edited files in the CodeAccordian component
2. **Added new message type "showDiff"** to handle diff view requests
3. **Enhanced webview message handler** to process diff content and show persistent diff views
4. **Used existing DiffViewProvider** to display the diff in a VS Code diff editor

## How it works:

1. When you see an edited file in the chat, click the **diff icon** (instead of the external link icon)
2. This opens a **persistent diff view** that shows:
    - Original file content on the left
    - Modified content on the right
    - Line-by-line differences highlighted
3. The diff view **stays open** even after you approve or reject the edit
4. You can **interact with the diff** before and after making decisions

## Key features:

- **Persistent diff view**: Doesn't close when you approve/reject
- **Real diff application**: Uses the diff strategy to apply changes properly
- **Proper error handling**: Shows meaningful error messages if something goes wrong
- **Integration with existing systems**: Uses the same DiffViewProvider as other tools

## Testing:

To test this, you would need to:

1. Start a conversation with <PERSON>uben<PERSON>
2. Ask it to edit a file
3. Look for the diff icon (instead of external link icon) on edited files
4. Click it to see the persistent diff view

The diff view will show the actual changes and remain accessible for review!
