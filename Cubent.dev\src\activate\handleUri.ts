import * as vscode from "vscode"

import { CloudService } from "@cubent/cloud"

import { ClineProvider } from "../core/webview/ClineProvider"

export const handleUri = async (uri: vscode.Uri) => {
	const path = uri.path
	const query = new URLSearchParams(uri.query.replace(/\+/g, "%2B"))
	const visibleProvider = ClineProvider.getVisibleInstance()

	if (!visibleProvider) {
		return
	}

	switch (path) {
		case "/glama": {
			const code = query.get("code")
			if (code) {
				await visibleProvider.handleGlamaCallback(code)
			}
			break
		}
		case "/openrouter": {
			const code = query.get("code")
			if (code) {
				await visibleProvider.handleOpenRouterCallback(code)
			}
			break
		}
		case "/requesty": {
			const code = query.get("code")
			if (code) {
				await visibleProvider.handleRequestyCallback(code)
			}
			break
		}
		case "/auth/clerk/callback": {
			const code = query.get("code")
			const state = query.get("state")
			await CloudService.instance.handleAuthCallback(code, state)
			break
		}
		case "/auth/callback": {
			const token = query.get("token")
			if (token) {
				try {
					await CloudService.instance.handleDeviceCallback(token)
					vscode.window.showInformationMessage("Successfully authenticated with Cubent!")
				} catch (error) {
					console.error("[handleUri] Device OAuth callback failed:", error)
					vscode.window.showErrorMessage("Authentication failed. Please try again.")
				}
			}
			break
		}
		default:
			break
	}
}
