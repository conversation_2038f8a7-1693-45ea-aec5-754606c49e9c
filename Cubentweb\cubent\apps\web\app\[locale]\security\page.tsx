import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Security | Cubent',
  description: 'Security practices and policies for Cubent AI coding assistant',
};

export default function SecurityPolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-950/20 via-black to-orange-900/10 text-white">
      <div className="container mx-auto px-6 py-20 max-w-4xl">
        <div className="bg-black/40 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-8 md:p-12">
          <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent">
            Security
          </h1>
          <p className="text-orange-300/80 mb-12 text-lg">Last updated: January 2, 2025</p>

          <div className="space-y-8 text-gray-100">
            <div className="bg-orange-500/10 border border-orange-500/30 rounded-xl p-6">
              <p className="text-lg leading-relaxed text-orange-100">
                Keeping your source code and developer environment secure is our top priority.
                This page outlines how we approach security for Cubent.
              </p>
            </div>

            <div className="bg-orange-500/10 border border-orange-500/30 rounded-xl p-6">
              <p className="text-orange-200">
                <span className="text-orange-300 font-medium">Security Contact:</span> For security-related questions or to report vulnerabilities,
                please contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300">
                  <EMAIL>
                </a>
              </p>
            </div>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Infrastructure Security</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Cloud Infrastructure</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    Our infrastructure is built on industry-leading cloud platforms with robust security measures:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li><strong className="text-orange-200">Primary Hosting:</strong> AWS with servers primarily in the US</li>
                    <li><strong className="text-orange-200">Content Delivery:</strong> Cloudflare for enhanced performance and security</li>
                    <li><strong className="text-orange-200">Database Security:</strong> Encrypted databases with access controls</li>
                    <li><strong className="text-orange-200">Network Security:</strong> VPC isolation and network-level controls</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">AI Model Providers</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    We partner with leading AI providers to deliver our services. All providers have
                    zero data retention agreements to ensure your code is not stored or used for training:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>OpenAI - Zero data retention agreement</li>
                    <li>Anthropic - Zero data retention agreement</li>
                    <li>Google Cloud Vertex API - Zero data retention agreement</li>
                    <li>Custom models hosted on secure infrastructure</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Access Controls</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    We implement strict access controls across our infrastructure:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Least-privilege access for all team members</li>
                    <li>Multi-factor authentication required</li>
                    <li>Regular access reviews and audits</li>
                    <li>Secure secrets management</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Client Security</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Application Security</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    Cubent is built with security best practices in mind:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Regular security updates and patches</li>
                    <li>Secure communication protocols (HTTPS/TLS)</li>
                    <li>Input validation and sanitization</li>
                    <li>Protection against common web vulnerabilities</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">API Endpoints</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    Our application communicates with the following secure domains:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li><code className="bg-orange-500/20 px-2 py-1 rounded text-orange-200">api.cubent.dev</code> - Main API requests</li>
                    <li><code className="bg-orange-500/20 px-2 py-1 rounded text-orange-200">cdn.cubent.dev</code> - Static content delivery</li>
                    <li><code className="bg-orange-500/20 px-2 py-1 rounded text-orange-200">auth.cubent.dev</code> - Authentication services</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">AI Requests and Data Handling</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Request Processing</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    To provide AI-powered coding assistance, Cubent processes various types of requests:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Chat conversations and code questions</li>
                    <li>Code completion and suggestions</li>
                    <li>Code analysis and optimization recommendations</li>
                    <li>Documentation and explanation requests</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Data Flow Security</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    All AI requests follow a secure data flow:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Encrypted transmission from client to our servers</li>
                    <li>Secure processing on our AWS infrastructure</li>
                    <li>Encrypted communication with AI model providers</li>
                    <li>Immediate deletion of temporary processing data</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Privacy Mode Guarantee</h2>

              <div className="bg-orange-500/10 border border-orange-500/30 rounded-xl p-6 mb-6">
                <h3 className="text-xl font-medium mb-3 text-orange-200">Enhanced Privacy Protection</h3>
                <p className="text-orange-100 leading-relaxed">
                  Privacy Mode provides the highest level of data protection for your code and conversations.
                </p>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Privacy Mode Features</h3>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Code data is never stored by AI model providers</li>
                    <li>No data used for model training purposes</li>
                    <li>Temporary processing only for immediate responses</li>
                    <li>Separate infrastructure for privacy mode users</li>
                    <li>Enhanced logging restrictions and data isolation</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Implementation</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    Our privacy mode implementation includes:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Parallel infrastructure for privacy and non-privacy requests</li>
                    <li>Header-based request routing with redundant checks</li>
                    <li>Default privacy mode assumption for safety</li>
                    <li>Team-level privacy mode enforcement</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Code Indexing Security</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Secure Indexing Process</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    When codebase indexing is enabled, we implement several security measures:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Respect for .gitignore and .cubentignore files</li>
                    <li>File path obfuscation using client-side encryption</li>
                    <li>Merkle tree-based change detection</li>
                    <li>Encrypted storage of embeddings and metadata</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Privacy Mode Indexing</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    For privacy mode users, additional protections apply:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>No plaintext code stored on servers</li>
                    <li>Local processing of search results</li>
                    <li>Encrypted embeddings only</li>
                    <li>Automatic data deletion upon account termination</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Compliance and Certifications</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Security Standards</h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    We are committed to maintaining high security standards and are working towards:
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>SOC 2 Type II certification (in progress)</li>
                    <li>Regular third-party security assessments</li>
                    <li>Penetration testing by reputable security firms</li>
                    <li>Compliance with industry best practices</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Account Security</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Account Protection</h3>
                  <ul className="list-disc pl-6 space-y-2 text-gray-300">
                    <li>Strong password requirements</li>
                    <li>Multi-factor authentication support</li>
                    <li>Session management and timeout controls</li>
                    <li>Account activity monitoring</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-medium mb-3 text-orange-200">Data Deletion</h3>
                  <p className="text-gray-300 leading-relaxed">
                    You can delete your account and all associated data at any time. We guarantee
                    complete data removal within 30 days of deletion request.
                  </p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Vulnerability Disclosure</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                We take security vulnerabilities seriously and encourage responsible disclosure:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-gray-300">
                <li>Report vulnerabilities to <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300"><EMAIL></a></li>
                <li>We commit to acknowledging reports within 5 business days</li>
                <li>Critical issues will be addressed immediately</li>
                <li>Security advisories published for resolved issues</li>
              </ul>
            </section>
            </section>

            <section>
              <h2 className="text-3xl font-semibold mb-6 text-orange-300">Contact Information</h2>

              <div className="bg-orange-500/10 border border-orange-500/30 rounded-xl p-6">
                <p className="text-orange-100 mb-4 leading-relaxed">
                  For security-related questions or concerns, please contact us:
                </p>
                <ul className="list-disc pl-6 space-y-2 text-orange-200">
                  <li>Security Team: <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300"><EMAIL></a></li>
                  <li>General Inquiries: <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300 underline decoration-orange-400/50 hover:decoration-orange-300"><EMAIL></a></li>
                </ul>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
