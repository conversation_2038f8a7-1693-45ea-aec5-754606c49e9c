// Script to inspect current legal pages structure
import { basehub } from './packages/cms/.basehub/index.js';

const client = basehub();

async function inspectLegalPages() {
  try {
    console.log('Fetching detailed legal pages structure...');
    
    const pages = await client.query({
      legalPages: {
        items: {
          _id: true,
          _slug: true,
          _title: true,
          description: true,
          body: {
            json: {
              content: true,
              toc: true
            },
            plainText: true,
            readingTime: true
          }
        }
      }
    });

    console.log('\n=== LEGAL PAGES STRUCTURE ===');
    
    pages.legalPages.items.forEach((page, index) => {
      console.log(`\n${index + 1}. ${page._title} (${page._slug})`);
      console.log(`   ID: ${page._id}`);
      console.log(`   Description: ${page.description}`);
      console.log(`   Reading Time: ${page.body.readingTime} min`);
      
      console.log('\n   --- TABLE OF CONTENTS ---');
      if (page.body.json.toc && page.body.json.toc.length > 0) {
        page.body.json.toc.forEach((item, i) => {
          console.log(`   ${i + 1}. ${item.text} (level ${item.level})`);
        });
      } else {
        console.log('   No table of contents');
      }
      
      console.log('\n   --- CONTENT STRUCTURE ---');
      if (page.body.json.content && Array.isArray(page.body.json.content)) {
        console.log(`   Content blocks: ${page.body.json.content.length}`);
        page.body.json.content.slice(0, 3).forEach((block, i) => {
          console.log(`   Block ${i + 1}:`, JSON.stringify(block, null, 2));
        });
        if (page.body.json.content.length > 3) {
          console.log(`   ... and ${page.body.json.content.length - 3} more blocks`);
        }
      } else {
        console.log('   Content structure:', typeof page.body.json.content);
        console.log('   Content preview:', JSON.stringify(page.body.json.content, null, 2).substring(0, 500));
      }
      
      console.log('\n   --- PLAIN TEXT (first 300 chars) ---');
      console.log(`   ${page.body.plainText.substring(0, 300)}...`);
    });

  } catch (error) {
    console.error('Error inspecting legal pages:', error);
  }
}

inspectLegalPages();
