{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/terms/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'Terms of Service | Cubent',\n  description: 'Terms of Service for Cubent AI coding assistant',\n};\n\nexport default function TermsOfService() {\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      <div className=\"container mx-auto px-4 py-16 max-w-4xl\">\n        <h1 className=\"text-4xl font-bold mb-8\">Terms of Service</h1>\n        <p className=\"text-gray-400 mb-8\">Last Updated: January 2, 2025</p>\n        \n        <div className=\"prose prose-invert max-w-none\">\n          <p className=\"text-lg mb-6\">\n            Welcome to <PERSON><PERSON><PERSON>, an AI-powered coding assistant. These Terms of Service (\"Terms\") \n            govern your access to and use of Cubent's software platform, APIs, documentation, \n            and related tools (collectively, the \"Service\"). By using the Service, you agree to these Terms.\n          </p>\n\n          <p className=\"mb-6\">\n            Please also read our <a href=\"/privacy\" className=\"text-blue-400 hover:text-blue-300\">Privacy Policy</a>, \n            which explains how we collect, use, disclose, and process personal data.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">1. Access and Use</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">1.1 Provision of Access</h3>\n          <p className=\"mb-4\">\n            Cubent is an AI-powered coding assistant that provides intelligent code suggestions, \n            completions, and development tools to help developers write code more efficiently. \n            Subject to your compliance with these Terms, Cubent grants you a limited right to access and use the Service.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">1.2 Content</h3>\n          <p className=\"mb-4\">\n            You may provide inputs to the Service (\"Inputs\") and receive code suggestions, outputs, \n            or other functions based on your Inputs (collectively, \"Suggestions\"). Inputs and Suggestions \n            are collectively referred to as \"Content\". We may use Content to provide the Service, \n            comply with applicable law, enforce our terms and policies, and keep the Service safe.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">1.3 Model Training</h3>\n          <div className=\"bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-6\">\n            <p className=\"text-blue-100\">\n              <strong>CUBENT WILL NOT USE CONTENT TO TRAIN, OR ALLOW ANY THIRD PARTY TO TRAIN, \n              ANY AI MODELS, UNLESS YOU'VE EXPLICITLY AGREED TO THE USE OF CONTENT FOR TRAINING.</strong> \n              You can manage your preferences regarding the use of Inputs and Suggestions for training in your account settings.\n            </p>\n          </div>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">1.4 Limitations for Suggestions</h3>\n          <p className=\"mb-4\">\n            You acknowledge that Suggestions are generated automatically by AI technology and may be \n            similar to suggestions provided to other users. You understand that AI models have limitations, including:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Suggestions may contain errors or misleading information</li>\n            <li>AI models may produce repetitive or formulaic content</li>\n            <li>AI models may struggle with complex reasoning and decision-making</li>\n            <li>Training data may contain biases or inaccuracies</li>\n          </ul>\n          <p className=\"mb-4\">\n            You agree that you are responsible for evaluating and bearing all risks associated with \n            using any Suggestions, including reliance on their accuracy, completeness, or usefulness.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">1.5 Use Restrictions</h3>\n          <p className=\"mb-4\">You may not:</p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Reverse engineer, disassemble, or attempt to derive the source code of the Service</li>\n            <li>Reproduce, modify, or create derivative works of the Service</li>\n            <li>Rent, lease, lend, or sell the Service</li>\n            <li>Use the Service to develop competing AI models or engage in model extraction</li>\n            <li>Probe, scan, or attempt to penetrate the Service's security</li>\n            <li>Use the Service in ways that violate applicable laws or third-party rights</li>\n            <li>Provide regulated data subject to specific legal protections (e.g., HIPAA, PCI DSS)</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">2. Eligibility</h2>\n          <p className=\"mb-4\">\n            You must be at least 18 years old or the age of majority in your jurisdiction to use the Service. \n            By agreeing to these Terms, you represent that you meet these age requirements and have not \n            previously been suspended or removed from the Service.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">3. Account Registration</h2>\n          <p className=\"mb-4\">\n            To access most features of the Service, you must register for an account. You agree to provide \n            accurate, complete, and current information and to keep your account information up to date. \n            You are responsible for maintaining the confidentiality of your account credentials and for \n            all activities that occur under your account.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">4. Payment Terms</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">4.1 Paid Services</h3>\n          <p className=\"mb-4\">\n            Certain features of the Service may require payment of fees. All fees are in U.S. Dollars \n            and are non-refundable except as required by law. We reserve the right to change pricing \n            with advance notice.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">4.2 Subscription Service</h3>\n          <p className=\"mb-4\">\n            Subscription plans automatically renew for successive periods unless cancelled. You must \n            cancel at least 24 hours before renewal to avoid being charged for the next period. \n            You can cancel through your account settings or by contacting us at \n            <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a>.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">5. Ownership and Licenses</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">5.1 Service Ownership</h3>\n          <p className=\"mb-4\">\n            Cubent and its licensors own all rights to the Service, including all improvements and \n            intellectual property rights. No implied licenses are granted under these Terms.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">5.2 Your Content</h3>\n          <p className=\"mb-4\">\n            You retain all rights to your Inputs. Cubent assigns to you all rights in any Suggestions \n            generated by the Service.\n          </p>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">5.3 Feedback</h3>\n          <p className=\"mb-4\">\n            If you provide feedback about the Service, you grant Cubent the right to use that feedback \n            without restriction or compensation.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">6. Third-Party Services</h2>\n          <p className=\"mb-4\">\n            The Service may include optional third-party services such as extensions and plugins. \n            Your use of third-party services is subject to their respective terms, and Cubent makes \n            no representations or warranties regarding such services.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">7. Termination</h2>\n          <p className=\"mb-4\">\n            You may stop using the Service at any time. We may suspend or terminate your access for \n            violations of these Terms or other reasons. Upon termination, we may delete Content \n            associated with your account. If you believe your account was terminated in error, \n            contact us at <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a>.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">8. Privacy</h2>\n          <p className=\"mb-4\">\n            Please read our <a href=\"/privacy\" className=\"text-blue-400 hover:text-blue-300\">Privacy Policy</a> \n            for information about how we collect, use, and protect your personal data.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">9. Disclaimers</h2>\n          <div className=\"bg-red-900/20 border border-red-600/30 rounded-lg p-4 mb-6\">\n            <p className=\"text-red-100\">\n              <strong>THE SERVICE AND SUGGESTIONS ARE PROVIDED \"AS IS\" AND \"AS AVAILABLE\" WITHOUT WARRANTIES OF ANY KIND.</strong> \n              Cubent disclaims all warranties, including implied warranties of merchantability, \n              fitness for a particular purpose, and non-infringement. We do not warrant that the Service \n              will be uninterrupted, secure, or error-free.\n            </p>\n          </div>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">10. Limitation of Liability</h2>\n          <div className=\"bg-red-900/20 border border-red-600/30 rounded-lg p-4 mb-6\">\n            <p className=\"text-red-100\">\n              <strong>TO THE FULLEST EXTENT PERMITTED BY LAW, CUBENT'S LIABILITY IS LIMITED TO THE GREATER OF:</strong> \n              (A) the amount you paid for the Service in the six months prior to the claim, or (B) $100. \n              Cubent will not be liable for indirect, incidental, special, consequential, or punitive damages.\n            </p>\n          </div>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">11. Dispute Resolution</h2>\n          <p className=\"mb-4\">\n            Any disputes will be resolved through binding arbitration rather than in court, except for \n            small claims court matters. You may opt out of arbitration within 30 days of account creation \n            by emailing <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a>.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">12. General Terms</h2>\n          <p className=\"mb-4\">\n            These Terms constitute the entire agreement between you and Cubent regarding the Service. \n            California law governs these Terms. We may update these Terms from time to time with notice.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">13. Contact Information</h2>\n          <p className=\"mb-4\">\n            For questions about these Terms, please contact us at:\n          </p>\n          <p className=\"mb-4\">\n            Email: <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a><br />\n            Legal: <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6VAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAElC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAE,WAAU;sCAAe;;;;;;sCAM5B,6VAAC;4BAAE,WAAU;;gCAAO;8CACG,6VAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAoC;;;;;;gCAAkB;;;;;;;sCAI1G,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAMpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAOpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAE,WAAU;;kDACX,6VAAC;kDAAO;;;;;;oCACmF;;;;;;;;;;;;sCAK/F,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAIpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAEN,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCACpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAMpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAOpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAMpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;;gCAAO;8CAIlB,6VAAC;oCAAE,MAAK;oCAAyB,WAAU;8CAAoC;;;;;;gCAAmB;;;;;;;sCAGpG,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAMpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;;gCAAO;8CAIJ,6VAAC;oCAAE,MAAK;oCAAyB,WAAU;8CAAoC;;;;;;gCAAmB;;;;;;;sCAGlH,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;;gCAAO;8CACF,6VAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAoC;;;;;;gCAAkB;;;;;;;sCAIrG,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAE,WAAU;;kDACX,6VAAC;kDAAO;;;;;;oCAA4G;;;;;;;;;;;;sCAOxH,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAE,WAAU;;kDACX,6VAAC;kDAAO;;;;;;oCAAiG;;;;;;;;;;;;sCAM7G,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;;gCAAO;8CAGN,6VAAC;oCAAE,MAAK;oCAA0B,WAAU;8CAAoC;;;;;;gCAAoB;;;;;;;sCAGlH,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAE,WAAU;;gCAAO;8CACX,6VAAC;oCAAE,MAAK;oCAAyB,WAAU;8CAAoC;;;;;;8CAAmB,6VAAC;;;;;gCAAK;8CACxG,6VAAC;oCAAE,MAAK;oCAA0B,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}]}