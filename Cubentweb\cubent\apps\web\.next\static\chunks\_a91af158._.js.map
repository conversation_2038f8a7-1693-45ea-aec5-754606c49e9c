{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\nexport {\n  __commonJS,\n  __toESM\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;;AAElB,IAAI,WAAW,OAAO,MAAM;AAC5B,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,cAAc;AACxC,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,aAAa,CAAC,IAAI,MAAQ,SAAS;QACrC,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;YAAE,SAAS,CAAC;QAAE,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,OAAO;IACpG;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,UAAU,CAAC,KAAK,YAAY,SAAW,CAAC,SAAS,OAAO,OAAO,SAAS,aAAa,QAAQ,CAAC,GAAG,YACnG,sEAAsE;IACtE,iEAAiE;IACjE,sEAAsE;IACtE,qEAAqE;IACrE,cAAc,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,QAAQ,WAAW;QAAE,OAAO;QAAK,YAAY;IAAK,KAAK,QACzG,IACD", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n\"use client\";\nimport \"./chunk-YSQDPG26.js\";\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-conditional-renderer.tsx\nimport * as React from \"react\";\nimport { createPortal } from \"react-dom\";\nvar LazyClientToolbar = React.lazy(\n  () => import(\"./client-toolbar-CIQDQ5LJ.js\").then((mod) => ({ default: mod.ClientToolbar }))\n);\nvar ClientConditionalRenderer = ({\n  draft,\n  isForcedDraft,\n  enableDraftMode,\n  disableDraftMode,\n  revalidateTags,\n  resolvedRef,\n  getLatestBranches\n}) => {\n  const [hasRendered, setHasRendered] = React.useState(false);\n  React.useEffect(() => {\n    setHasRendered(true);\n  }, []);\n  const bshbPreviewLSName = `bshb-preview-${resolvedRef.repoHash}`;\n  const seekAndStoreBshbPreviewToken = React.useCallback(\n    (type) => {\n      if (typeof window === \"undefined\")\n        return;\n      const urlParams = new URLSearchParams(window.location.search);\n      const bshbPreviewToken2 = urlParams.get(\"bshb-preview\");\n      if (bshbPreviewToken2) {\n        try {\n          window.localStorage?.setItem(bshbPreviewLSName, bshbPreviewToken2);\n        } catch (e) {\n        }\n        return bshbPreviewToken2;\n      }\n      if (type === \"url-only\")\n        return;\n      try {\n        const fromStorage = window.localStorage?.getItem(bshbPreviewLSName);\n        if (fromStorage)\n          return fromStorage;\n      } catch (e) {\n      }\n    },\n    [bshbPreviewLSName]\n  );\n  const [bshbPreviewToken, setBshbPreviewToken] = React.useState(seekAndStoreBshbPreviewToken);\n  const [shouldAutoEnableDraft, setShouldAutoEnableDraft] = React.useState();\n  React.useLayoutEffect(() => {\n    if (draft || isForcedDraft) {\n      setShouldAutoEnableDraft(false);\n      return;\n    }\n    const previewToken = seekAndStoreBshbPreviewToken(\"url-only\");\n    if (!previewToken) {\n      setShouldAutoEnableDraft(false);\n      return;\n    }\n    setBshbPreviewToken(previewToken);\n    setShouldAutoEnableDraft(true);\n  }, [draft, isForcedDraft, seekAndStoreBshbPreviewToken]);\n  React.useEffect(() => {\n    const url = new URL(window.location.href);\n    const shouldRevalidate = url.searchParams.get(\"__bshb-odr\") === \"true\";\n    const odrToken = url.searchParams.get(\"__bshb-odr-token\");\n    const ref = url.searchParams.get(\"__bshb-odr-ref\");\n    if (shouldRevalidate && odrToken) {\n      revalidateTags({ bshbPreviewToken: odrToken, ...ref ? { ref } : {} }).then(({ success, message }) => {\n        document.documentElement.dataset.basehubOdrStatus = success ? \"success\" : \"error\";\n        if (!success) {\n          document.documentElement.dataset.basehubOdrErrorMessage = \"Response failed\";\n        }\n        if (message) {\n          document.documentElement.dataset.basehubOdrMessage = message;\n        }\n      }).catch((e) => {\n        document.documentElement.dataset.basehubOdrStatus = \"error\";\n        let errorMessage = \"\";\n        try {\n          errorMessage = e.message;\n        } catch (err) {\n          console.error(err);\n          errorMessage = \"Unknown error\";\n        }\n        document.documentElement.dataset.basehubOdrErrorMessage = errorMessage;\n      });\n    }\n  }, [revalidateTags]);\n  if (!bshbPreviewToken && !isForcedDraft || !hasRendered || typeof document === \"undefined\") {\n    return null;\n  }\n  const Portal = createPortal(\n    /* @__PURE__ */ React.createElement(\n      LazyClientToolbar,\n      {\n        disableDraftMode,\n        enableDraftMode,\n        draft,\n        isForcedDraft,\n        bshbPreviewToken,\n        shouldAutoEnableDraft,\n        seekAndStoreBshbPreviewToken,\n        resolvedRef,\n        getLatestBranches,\n        bshbPreviewLSName\n      }\n    ),\n    document.body\n  );\n  return Portal;\n};\nexport {\n  ClientConditionalRenderer\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;AAGlB;AAEA,8JAA8J;AAC9J;AACA;;AALA;;;;AAMA,IAAI,kCAAoB,CAAA,GAAA,4QAAA,CAAA,OAAU,AAAD,EAC/B,IAAM,oKAAuC,IAAI,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS,IAAI,aAAa;QAAC,CAAC;KADxF;AAGJ,IAAI,4BAA4B,CAAC,EAC/B,KAAK,EACL,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,iBAAiB,EAClB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IACrD,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;+CAAE;YACd,eAAe;QACjB;8CAAG,EAAE;IACL,MAAM,oBAAoB,CAAC,aAAa,EAAE,YAAY,QAAQ,EAAE;IAChE,MAAM,+BAA+B,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;+EACnD,CAAC;YACC,uCACE;;YAAM;YACR,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YAC5D,MAAM,oBAAoB,UAAU,GAAG,CAAC;YACxC,IAAI,mBAAmB;gBACrB,IAAI;oBACF,OAAO,YAAY,EAAE,QAAQ,mBAAmB;gBAClD,EAAE,OAAO,GAAG,CACZ;gBACA,OAAO;YACT;YACA,IAAI,SAAS,YACX;YACF,IAAI;gBACF,MAAM,cAAc,OAAO,YAAY,EAAE,QAAQ;gBACjD,IAAI,aACF,OAAO;YACX,EAAE,OAAO,GAAG,CACZ;QACF;8EACA;QAAC;KAAkB;IAErB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD;IACvE,CAAA,GAAA,4QAAA,CAAA,kBAAqB,AAAD;qDAAE;YACpB,IAAI,SAAS,eAAe;gBAC1B,yBAAyB;gBACzB;YACF;YACA,MAAM,eAAe,6BAA6B;YAClD,IAAI,CAAC,cAAc;gBACjB,yBAAyB;gBACzB;YACF;YACA,oBAAoB;YACpB,yBAAyB;QAC3B;oDAAG;QAAC;QAAO;QAAe;KAA6B;IACvD,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;+CAAE;YACd,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YACxC,MAAM,mBAAmB,IAAI,YAAY,CAAC,GAAG,CAAC,kBAAkB;YAChE,MAAM,WAAW,IAAI,YAAY,CAAC,GAAG,CAAC;YACtC,MAAM,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC;YACjC,IAAI,oBAAoB,UAAU;gBAChC,eAAe;oBAAE,kBAAkB;oBAAU,GAAG,MAAM;wBAAE;oBAAI,IAAI,CAAC,CAAC;gBAAC,GAAG,IAAI;2DAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;wBAC9F,SAAS,eAAe,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAAU,YAAY;wBAC1E,IAAI,CAAC,SAAS;4BACZ,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAG;wBAC5D;wBACA,IAAI,SAAS;4BACX,SAAS,eAAe,CAAC,OAAO,CAAC,iBAAiB,GAAG;wBACvD;oBACF;0DAAG,KAAK;2DAAC,CAAC;wBACR,SAAS,eAAe,CAAC,OAAO,CAAC,gBAAgB,GAAG;wBACpD,IAAI,eAAe;wBACnB,IAAI;4BACF,eAAe,EAAE,OAAO;wBAC1B,EAAE,OAAO,KAAK;4BACZ,QAAQ,KAAK,CAAC;4BACd,eAAe;wBACjB;wBACA,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAG;oBAC5D;;YACF;QACF;8CAAG;QAAC;KAAe;IACnB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,OAAO,aAAa,aAAa;QAC1F,OAAO;IACT;IACA,MAAM,uBAAS,CAAA,GAAA,mRAAA,CAAA,eAAY,AAAD,EACxB,aAAa,iBAAG,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EAChC,mBACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF,IAEF,SAAS,IAAI;IAEf,OAAO;AACT;GAtGI;MAAA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    client: {\n      NEXT_PUBLIC_POSTHOG_KEY: z.string().startsWith('phc_').optional(),\n      NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().startsWith('G-').optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\n      NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\n    },\n  });\n"], "names": [], "mappings": ";;;AAW+B;AAX/B;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,yBAAyB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,QAAQ;YAC/D,0BAA0B,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACnD,+BAA+B,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ;QACrE;QACA,YAAY;YACV,uBAAuB;YACvB,wBAAwB;YACxB,6BAA6B;QAC/B;IACF", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/client.tsx"], "sourcesContent": ["'use client';\n\nimport posthog, { type PostHog } from 'posthog-js';\nimport { PostHogProvider as PostHogProviderRaw } from 'posthog-js/react';\nimport type { ReactNode } from 'react';\nimport { useEffect } from 'react';\nimport { keys } from '../keys';\n\ntype PostHogProviderProps = {\n  readonly children: ReactNode;\n};\n\nexport const PostHogProvider = (\n  properties: Omit<PostHogProviderProps, 'client'>\n) => {\n  useEffect(() => {\n    try {\n      const envKeys = keys();\n      if (envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST) {\n        posthog.init(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n          api_host: '/ingest',\n          ui_host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n          person_profiles: 'identified_only',\n          capture_pageview: false, // Disable automatic pageview capture, as we capture manually\n          capture_pageleave: true, // Overrides the `capture_pageview` setting\n        }) as PostHog;\n      } else {\n        console.warn('PostHog environment variables not configured. Analytics disabled.');\n      }\n    } catch (error) {\n      console.warn('PostHog initialization failed:', error);\n    }\n  }, []);\n\n  return <PostHogProviderRaw client={posthog} {...properties} />;\n};\n\nexport { usePostHog as useAnalytics } from 'posthog-js/react';\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAYO,MAAM,kBAAkB,CAC7B;;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI;gBACF,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,OAAI,AAAD;gBACnB,IAAI,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,EAAE;oBACvE,+PAAA,CAAA,UAAO,CAAC,IAAI,CAAC,QAAQ,uBAAuB,EAAE;wBAC5C,UAAU;wBACV,SAAS,QAAQ,wBAAwB;wBACzC,iBAAiB;wBACjB,kBAAkB;wBAClB,mBAAmB;oBACrB;gBACF,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,kCAAkC;YACjD;QACF;oCAAG,EAAE;IAEL,qBAAO,4SAAC,8QAAA,CAAA,kBAAkB;QAAC,QAAQ,+PAAA,CAAA,UAAO;QAAG,GAAG,UAAU;;;;;;AAC5D;GAvBa;KAAA", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';\nimport { dark } from '@clerk/themes';\nimport type { Theme } from '@clerk/types';\nimport { useTheme } from 'next-themes';\nimport type { ComponentProps } from 'react';\n\ntype AuthProviderProperties = ComponentProps<typeof ClerkProvider> & {\n  privacyUrl?: string;\n  termsUrl?: string;\n  helpUrl?: string;\n};\n\nexport const AuthProvider = ({\n  privacyUrl,\n  termsUrl,\n  helpUrl,\n  ...properties\n}: AuthProviderProperties) => {\n  const { resolvedTheme } = useTheme();\n  const isDark = resolvedTheme === 'dark';\n  const baseTheme = isDark ? dark : undefined;\n\n  const variables: Theme['variables'] = {\n    fontFamily: 'system-ui, -apple-system, sans-serif',\n    fontFamilyButtons: 'system-ui, -apple-system, sans-serif',\n    fontWeight: {\n      bold: 700,\n      normal: 400,\n      medium: 500,\n    },\n    // Orange color theming\n    colorPrimary: '#f97316', // orange-500\n    colorSuccess: '#10b981', // emerald-500\n    colorWarning: '#f59e0b', // amber-500\n    colorDanger: '#ef4444', // red-500\n    colorNeutral: '#6b7280', // gray-500\n    colorText: '#1f2937', // darker gray-800 for main text\n    colorTextOnPrimaryBackground: '#ffffff',\n    colorTextSecondary: '#374151', // darker gray-700 for secondary text like \"Continue with Google\"\n    colorInputBackground: '#ffffff', // white background for inputs\n    colorInputText: '#111827', // dark text for inputs\n    borderRadius: '0.75rem', // rounded-xl\n    spacingUnit: '1.25rem', // Increased spacing for larger form\n  };\n\n  const elements: Theme['elements'] = {\n    dividerLine: 'bg-border',\n    socialButtonsIconButton: 'bg-card hover:bg-orange-500/10 border-orange-500/20 transition-colors py-3 px-4',\n    navbarButton: 'text-foreground',\n    organizationSwitcherTrigger__open: 'bg-background',\n    organizationPreviewMainIdentifier: 'text-foreground',\n    organizationSwitcherTriggerIcon: 'text-muted-foreground',\n    organizationPreview__organizationSwitcherTrigger: 'gap-2',\n    organizationPreviewAvatarContainer: 'shrink-0',\n    // Form elements with orange accents and larger sizing - REMOVED DUPLICATE (moved to bottom with grey background)\n    // Header elements - completely hidden\n    header: 'hidden',\n    headerTitle: 'hidden',\n    headerSubtitle: 'hidden',\n    // Hide alternative methods and identity preview\n    alternativeMethodsBlockButton: 'hidden',\n    alternativeMethodsBlockButtonText: 'hidden',\n    identityPreview: 'hidden',\n    identityPreviewText: 'hidden',\n    identityPreviewEditButton: 'hidden',\n    // Card styling with solid white background - ROUNDED modal box with EVEN LESS padding\n    card: '!bg-white !shadow-none !border-0 !rounded-3xl p-4 mx-auto',\n    rootBox: 'relative !bg-white !rounded-3xl shadow-2xl min-w-[400px] mx-auto !border-0',\n    main: '!bg-white !border-0 !shadow-none',\n    modalContent: '!bg-white !border-0 !shadow-none',\n    // Style the form button (Continue button) - reduced height, NO ROUNDING\n    formButtonPrimary: '!bg-gradient-to-r !from-orange-500 !to-orange-600 hover:!from-orange-600 hover:!to-orange-700 !text-white !shadow-md !transition-all !duration-200 !py-1.5 !px-6 !text-base !font-medium !w-full !rounded-none !border-0',\n    // Style Google button with background\n    socialButtonsBlockButton: '!bg-slate-50 !border !border-slate-200 hover:!bg-slate-100 !text-gray-700 !shadow-sm hover:!shadow-md !transition-all !duration-200 !py-3 !px-6 !rounded-lg',\n    // Style email input with grey background\n    formFieldInput: '!bg-slate-50 !border !border-slate-200 focus:!bg-white focus:!border-orange-500 focus:!ring-2 focus:!ring-orange-500/20 !rounded-lg !py-3 !px-4 !text-base !transition-all !duration-200',\n  };\n\n  const layout: Theme['layout'] = {\n    privacyPageUrl: privacyUrl,\n    termsPageUrl: termsUrl,\n    helpPageUrl: helpUrl,\n  };\n\n  return (\n    <ClerkProvider\n      {...properties}\n      appearance={{ layout, baseTheme, elements, variables }}\n    />\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAcO,MAAM,eAAe,CAAC,EAC3B,UAAU,EACV,QAAQ,EACR,OAAO,EACP,GAAG,YACoB;;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,SAAS,kBAAkB;IACjC,MAAM,YAAY,SAAS,qOAAA,CAAA,OAAI,GAAG;IAElC,MAAM,YAAgC;QACpC,YAAY;QACZ,mBAAmB;QACnB,YAAY;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,uBAAuB;QACvB,cAAc;QACd,cAAc;QACd,cAAc;QACd,aAAa;QACb,cAAc;QACd,WAAW;QACX,8BAA8B;QAC9B,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,MAAM,WAA8B;QAClC,aAAa;QACb,yBAAyB;QACzB,cAAc;QACd,mCAAmC;QACnC,mCAAmC;QACnC,iCAAiC;QACjC,kDAAkD;QAClD,oCAAoC;QACpC,iHAAiH;QACjH,sCAAsC;QACtC,QAAQ;QACR,aAAa;QACb,gBAAgB;QAChB,gDAAgD;QAChD,+BAA+B;QAC/B,mCAAmC;QACnC,iBAAiB;QACjB,qBAAqB;QACrB,2BAA2B;QAC3B,sFAAsF;QACtF,MAAM;QACN,SAAS;QACT,MAAM;QACN,cAAc;QACd,wEAAwE;QACxE,mBAAmB;QACnB,sCAAsC;QACtC,0BAA0B;QAC1B,yCAAyC;QACzC,gBAAgB;IAClB;IAEA,MAAM,SAA0B;QAC9B,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,qBACE,4SAAC,sRAAA,CAAA,gBAAa;QACX,GAAG,UAAU;QACd,YAAY;YAAE;YAAQ;YAAW;YAAU;QAAU;;;;;;AAG3D;GA9Ea;;QAMe,4PAAA,CAAA,WAAQ;;;KANvB", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,4SAAC,2QAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,4PAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/log.ts"], "sourcesContent": ["import { log as logtail } from '@logtail/next';\n\nexport const log = process.env.NODE_ENV === 'production' ? logtail : console;\n"], "names": [], "mappings": ";;;AAEmB;AAFnB;;AAEO,MAAM,MAAM,6EAAkD", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/error.ts"], "sourcesContent": ["import { captureException } from '@sentry/nextjs';\nimport { log } from './log';\n\nexport const parseError = (error: unknown): string => {\n  let message = 'An error occurred';\n\n  if (error instanceof Error) {\n    message = error.message;\n  } else if (error && typeof error === 'object' && 'message' in error) {\n    message = error.message as string;\n  } else {\n    message = String(error);\n  }\n\n  try {\n    captureException(error);\n    log.error(`Parsing error: ${message}`);\n  } catch (newError) {\n    // biome-ignore lint/suspicious/noConsole: Need console here\n    console.error('Error parsing error:', newError);\n  }\n\n  return message;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI,UAAU;IAEd,IAAI,iBAAiB,OAAO;QAC1B,UAAU,MAAM,OAAO;IACzB,OAAO,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;QACnE,UAAU,MAAM,OAAO;IACzB,OAAO;QACL,UAAU,OAAO;IACnB;IAEA,IAAI;QACF,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;QACjB,mIAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;IACvC,EAAE,OAAO,UAAU;QACjB,4DAA4D;QAC5D,QAAQ,KAAK,CAAC,wBAAwB;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/lib/utils.ts"], "sourcesContent": ["import { parseError } from '@repo/observability/error';\nimport { clsx } from 'clsx';\nimport type { ClassValue } from 'clsx';\nimport { toast } from 'sonner';\nimport { twMerge } from 'tailwind-merge';\n\nexport const cn = (...inputs: ClassValue[]): string => twMerge(clsx(inputs));\n\nexport const capitalize = (str: string) =>\n  str.charAt(0).toUpperCase() + str.slice(1);\n\nexport const handleError = (error: unknown): void => {\n  const message = parseError(error);\n\n  toast.error(message);\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;AAEO,MAAM,KAAK,CAAC,GAAG,SAAiC,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AAE7D,MAAM,aAAa,CAAC,MACzB,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AAEnC,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;AACd", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,4SAAC,gRAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,4SAAC;kBACC,cAAA,4SAAC,gRAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,4SAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,4SAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,4SAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,4SAAC,gRAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/react-pump/chunk-F5PHAOMO.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\n\nexport {\n  __commonJS\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;AAElB,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,aAAa,CAAC,IAAI,MAAQ,SAAS;QACrC,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;YAAE,SAAS,CAAC;QAAE,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,OAAO;IACpG", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_aliasing.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nexport const aliasSeparator = '__alias__'\n\nexport function replaceSystemAliases(obj) {\n    if (typeof obj !== 'object' || obj === null) {\n        return obj\n    }\n\n    if (Array.isArray(obj)) {\n        return obj.map((item) => replaceSystemAliases(item))\n    }\n\n    const newObj = {}\n    for (const [key, value] of Object.entries(obj)) {\n        if (key.includes(aliasSeparator)) {\n            const [_prefix, ...rest] = key.split(aliasSeparator)\n            const newKey = rest.join(aliasSeparator) // In case there are multiple __alias__ in the key\n            newObj[newKey] = replaceSystemAliases(value)\n        } else {\n            newObj[key] = replaceSystemAliases(value)\n        }\n    }\n\n    return newObj\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;;AACP,MAAM,iBAAiB;AAEvB,SAAS,qBAAqB,GAAG;IACpC,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QACzC,OAAO;IACX;IAEA,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,OAAO,IAAI,GAAG,CAAC,CAAC,OAAS,qBAAqB;IAClD;IAEA,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC5C,IAAI,IAAI,QAAQ,CAAC,iBAAiB;YAC9B,MAAM,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC;YACrC,MAAM,SAAS,KAAK,IAAI,CAAC,gBAAgB,kDAAkD;;YAC3F,MAAM,CAAC,OAAO,GAAG,qBAAqB;QAC1C,OAAO;YACH,MAAM,CAAC,IAAI,GAAG,qBAAqB;QACvC;IACJ;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/react-pump/client-pump-WYUPTPKD.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n\"use client\";\n\n\nimport \"./chunk-F5PHAOMO.js\";\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/react/pump/client-pump.tsx\nimport * as React from \"react\";\nimport { replaceSystemAliases } from \"../runtime/_aliasing.js\";\nvar pusherMounted = false;\nvar subscribers = /* @__PURE__ */ new Set();\nvar clientCache = /* @__PURE__ */ new Map();\nvar lastResponseHashCache = /* @__PURE__ */ new Map();\nvar DEDUPE_TIME_MS = 32;\nvar ClientPump = ({\n  children,\n  rawQueries,\n  pumpEndpoint,\n  pumpToken: initialPumpToken,\n  initialState,\n  initialResolvedChildren,\n  apiVersion,\n  previewRef: _previewRef\n}) => {\n  const pumpTokenRef = React.useRef(initialPumpToken);\n  const [result, setResult] = React.useState(\n    initialState\n  );\n  const initialStateRef = React.useRef(initialState);\n  initialStateRef.current = initialState;\n  const [previewRef, setPreviewRef] = React.useState(_previewRef);\n  const previewRefRef = React.useRef(previewRef);\n  previewRefRef.current = previewRef;\n  const refetch = React.useCallback(async () => {\n    let newPumpToken;\n    let pusherData = void 0;\n    let spaceID = void 0;\n    const responses = await Promise.all(\n      rawQueries.map(async (rawQueryOp, index) => {\n        if (!pumpTokenRef.current) {\n          console.warn(\"No pump token found. Skipping query.\");\n          return null;\n        }\n        const queryHash = JSON.stringify(rawQueryOp);\n        const responseHashCacheKey = queryHash;\n        const queryCacheKey = queryHash + previewRef;\n        const lastResponseHash = lastResponseHashCache.get(responseHashCacheKey) || initialStateRef.current?.responseHashes?.[index] || \"\";\n        if (clientCache.has(queryCacheKey)) {\n          const cached = clientCache.get(queryCacheKey);\n          if (performance.now() - cached.start < DEDUPE_TIME_MS) {\n            const response2 = await cached.response;\n            if (!response2)\n              return null;\n            if (response2.newPumpToken) {\n              newPumpToken = response2.newPumpToken;\n            }\n            pusherData = response2.pusherData;\n            spaceID = response2.spaceID;\n            return response2;\n          }\n        }\n        const responsePromise = fetch(pumpEndpoint, {\n          cache: \"no-store\",\n          method: \"POST\",\n          headers: {\n            \"content-type\": \"application/json\",\n            \"x-basehub-api-version\": apiVersion,\n            \"x-basehub-ref\": previewRef\n          },\n          body: JSON.stringify({\n            ...rawQueryOp,\n            pumpToken: pumpTokenRef.current,\n            lastResponseHash\n          })\n        }).then(async (response2) => {\n          const {\n            data = null,\n            errors = null,\n            newPumpToken: newPumpToken2,\n            spaceID: spaceID2,\n            pusherData: pusherData2,\n            responseHash\n          } = await response2.json();\n          lastResponseHashCache.set(responseHashCacheKey, responseHash);\n          return {\n            data: replaceSystemAliases(data),\n            spaceID: spaceID2,\n            pusherData: pusherData2,\n            newPumpToken: newPumpToken2,\n            errors,\n            responseHash,\n            changed: lastResponseHash !== responseHash\n          };\n        }).catch((err) => {\n          console.error(`Error fetching data from the BaseHub Draft API:\n              \n${JSON.stringify(err, null, 2)}\n              \nContact <EMAIL> for help.`);\n        });\n        clientCache.set(queryCacheKey, {\n          start: performance.now(),\n          response: responsePromise\n        });\n        const response = await responsePromise;\n        if (!response)\n          return null;\n        if (response.newPumpToken) {\n          newPumpToken = response.newPumpToken;\n        }\n        pusherData = response.pusherData;\n        spaceID = response.spaceID;\n        return response;\n      })\n    );\n    const shouldUpdate = responses.some((r) => r?.changed);\n    if (shouldUpdate) {\n      if (!pusherData || !spaceID)\n        return;\n      setResult((p) => {\n        if (!pusherData || !spaceID)\n          return p;\n        return {\n          data: responses.map((r, i) => {\n            if (!r?.changed)\n              return p?.data?.[i] ?? null;\n            return r?.data ?? null;\n          }),\n          errors: responses.map((r, i) => {\n            if (!r?.changed)\n              return p?.errors?.[i] ?? null;\n            return r?.errors ?? null;\n          }),\n          responseHashes: responses.map((r) => r?.responseHash ?? \"\"),\n          pusherData,\n          spaceID\n        };\n      });\n    }\n    if (newPumpToken) {\n      pumpTokenRef.current = newPumpToken;\n    }\n  }, [pumpEndpoint, rawQueries, apiVersion, previewRef]);\n  const currentToastRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!result?.errors)\n      return;\n    const mainError = result.errors[0]?.[0];\n    if (!mainError)\n      return;\n    console.error(\n      `Error fetching data from the BaseHub Draft API: ${mainError.message}${mainError.path ? ` at ${mainError.path.join(\".\")}` : \"\"}`\n    );\n  }, [result?.errors]);\n  React.useEffect(() => {\n    function boundRefetch() {\n      refetch();\n    }\n    boundRefetch();\n    subscribers.add(boundRefetch);\n    return () => {\n      subscribers.delete(boundRefetch);\n    };\n  }, [refetch]);\n  const [pusher, setPusher] = React.useState(null);\n  const pusherChannelKey = result?.pusherData?.channel_key;\n  const pusherAppKey = result?.pusherData.app_key;\n  const pusherCluster = result?.pusherData.cluster;\n  React.useEffect(() => {\n    if (pusherMounted)\n      return;\n    if (!pusherAppKey || !pusherCluster)\n      return;\n    pusherMounted = true;\n    import(\"./pusher-KF5UTUSO.js\").then((mod) => {\n      setPusher(new mod.default(pusherAppKey, { cluster: pusherCluster }));\n    }).catch((err) => {\n      console.log(\"error importing pusher\");\n      console.error(err);\n    });\n    return () => {\n      pusherMounted = false;\n    };\n  }, [pusherAppKey, pusherCluster]);\n  React.useEffect(() => {\n    if (!pusherChannelKey)\n      return;\n    if (!pusher)\n      return;\n    const channel = pusher.subscribe(pusherChannelKey);\n    channel.bind(\n      \"poke\",\n      (message) => {\n        if (message?.mutatedEntryTypes?.includes(\"block\") && message.branch === previewRefRef.current) {\n          subscribers.forEach((sub) => sub());\n        }\n      }\n    );\n    return () => {\n      channel.unsubscribe();\n    };\n  }, [pusher, pusherChannelKey]);\n  React.useEffect(() => {\n    function handleRefChange() {\n      const previewRef2 = (\n        // @ts-ignore\n        window.__bshb_ref\n      );\n      if (!previewRef2 || typeof previewRef2 !== \"string\")\n        return;\n      setPreviewRef(previewRef2);\n    }\n    handleRefChange();\n    window.addEventListener(\"__bshb_ref_changed\", handleRefChange);\n    return () => {\n      window.removeEventListener(\"__bshb_ref_changed\", handleRefChange);\n    };\n  }, []);\n  const resolvedData = React.useMemo(() => {\n    return result?.data.map((r, i) => r ?? initialState?.data?.[i] ?? null);\n  }, [initialState?.data, result?.data]);\n  const [resolvedChildren, setResolvedChildren] = React.useState(\n    typeof children === \"function\" ? (\n      // if function, we'll resolve in React.useEffect below\n      initialResolvedChildren\n    ) : children\n  );\n  React.useEffect(() => {\n    if (!resolvedData)\n      return;\n    if (typeof children === \"function\") {\n      const res = children(resolvedData);\n      if (res instanceof Promise) {\n        res.then(setResolvedChildren);\n      } else {\n        setResolvedChildren(res);\n      }\n    } else {\n      setResolvedChildren(children);\n    }\n  }, [children, resolvedData]);\n  return resolvedChildren ?? initialResolvedChildren;\n};\nexport {\n  ClientPump\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;AAKlB;AAEA,4IAA4I;AAC5I;AACA;;AAPA;;;;AAQA,IAAI,gBAAgB;AACpB,IAAI,cAAc,aAAa,GAAG,IAAI;AACtC,IAAI,cAAc,aAAa,GAAG,IAAI;AACtC,IAAI,wBAAwB,aAAa,GAAG,IAAI;AAChD,IAAI,iBAAiB;AACrB,IAAI,aAAa,CAAC,EAChB,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,WAAW,gBAAgB,EAC3B,YAAY,EACZ,uBAAuB,EACvB,UAAU,EACV,YAAY,WAAW,EACxB;;IACC,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IAClC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EACvC;IAEF,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IACrC,gBAAgB,OAAO,GAAG;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IACnD,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IACnC,cAAc,OAAO,GAAG;IACxB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;2CAAE;YAChC,IAAI;YACJ,IAAI,aAAa,KAAK;YACtB,IAAI,UAAU,KAAK;YACnB,MAAM,YAAY,MAAM,QAAQ,GAAG,CACjC,WAAW,GAAG;mDAAC,OAAO,YAAY;oBAChC,IAAI,CAAC,aAAa,OAAO,EAAE;wBACzB,QAAQ,IAAI,CAAC;wBACb,OAAO;oBACT;oBACA,MAAM,YAAY,KAAK,SAAS,CAAC;oBACjC,MAAM,uBAAuB;oBAC7B,MAAM,gBAAgB,YAAY;oBAClC,MAAM,mBAAmB,sBAAsB,GAAG,CAAC,yBAAyB,gBAAgB,OAAO,EAAE,gBAAgB,CAAC,MAAM,IAAI;oBAChI,IAAI,YAAY,GAAG,CAAC,gBAAgB;wBAClC,MAAM,SAAS,YAAY,GAAG,CAAC;wBAC/B,IAAI,YAAY,GAAG,KAAK,OAAO,KAAK,GAAG,gBAAgB;4BACrD,MAAM,YAAY,MAAM,OAAO,QAAQ;4BACvC,IAAI,CAAC,WACH,OAAO;4BACT,IAAI,UAAU,YAAY,EAAE;gCAC1B,eAAe,UAAU,YAAY;4BACvC;4BACA,aAAa,UAAU,UAAU;4BACjC,UAAU,UAAU,OAAO;4BAC3B,OAAO;wBACT;oBACF;oBACA,MAAM,kBAAkB,MAAM,cAAc;wBAC1C,OAAO;wBACP,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,yBAAyB;4BACzB,iBAAiB;wBACnB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,GAAG,UAAU;4BACb,WAAW,aAAa,OAAO;4BAC/B;wBACF;oBACF,GAAG,IAAI;2EAAC,OAAO;4BACb,MAAM,EACJ,OAAO,IAAI,EACX,SAAS,IAAI,EACb,cAAc,aAAa,EAC3B,SAAS,QAAQ,EACjB,YAAY,WAAW,EACvB,YAAY,EACb,GAAG,MAAM,UAAU,IAAI;4BACxB,sBAAsB,GAAG,CAAC,sBAAsB;4BAChD,OAAO;gCACL,MAAM,CAAA,GAAA,uJAAA,CAAA,uBAAoB,AAAD,EAAE;gCAC3B,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd;gCACA;gCACA,SAAS,qBAAqB;4BAChC;wBACF;0EAAG,KAAK;2EAAC,CAAC;4BACR,QAAQ,KAAK,CAAC,CAAC;;AAEzB,EAAE,KAAK,SAAS,CAAC,KAAK,MAAM,GAAG;;qCAEM,CAAC;wBAC9B;;oBACA,YAAY,GAAG,CAAC,eAAe;wBAC7B,OAAO,YAAY,GAAG;wBACtB,UAAU;oBACZ;oBACA,MAAM,WAAW,MAAM;oBACvB,IAAI,CAAC,UACH,OAAO;oBACT,IAAI,SAAS,YAAY,EAAE;wBACzB,eAAe,SAAS,YAAY;oBACtC;oBACA,aAAa,SAAS,UAAU;oBAChC,UAAU,SAAS,OAAO;oBAC1B,OAAO;gBACT;;YAEF,MAAM,eAAe,UAAU,IAAI;gEAAC,CAAC,IAAM,GAAG;;YAC9C,IAAI,cAAc;gBAChB,IAAI,CAAC,cAAc,CAAC,SAClB;gBACF;uDAAU,CAAC;wBACT,IAAI,CAAC,cAAc,CAAC,SAClB,OAAO;wBACT,OAAO;4BACL,MAAM,UAAU,GAAG;mEAAC,CAAC,GAAG;oCACtB,IAAI,CAAC,GAAG,SACN,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI;oCACzB,OAAO,GAAG,QAAQ;gCACpB;;4BACA,QAAQ,UAAU,GAAG;mEAAC,CAAC,GAAG;oCACxB,IAAI,CAAC,GAAG,SACN,OAAO,GAAG,QAAQ,CAAC,EAAE,IAAI;oCAC3B,OAAO,GAAG,UAAU;gCACtB;;4BACA,gBAAgB,UAAU,GAAG;mEAAC,CAAC,IAAM,GAAG,gBAAgB;;4BACxD;4BACA;wBACF;oBACF;;YACF;YACA,IAAI,cAAc;gBAChB,aAAa,OAAO,GAAG;YACzB;QACF;0CAAG;QAAC;QAAc;QAAY;QAAY;KAAW;IACrD,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IACrC,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,CAAC,QAAQ,QACX;YACF,MAAM,YAAY,OAAO,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE;YACvC,IAAI,CAAC,WACH;YACF,QAAQ,KAAK,CACX,CAAC,gDAAgD,EAAE,UAAU,OAAO,GAAG,UAAU,IAAI,GAAG,CAAC,IAAI,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;QAEpI;+BAAG;QAAC,QAAQ;KAAO;IACnB,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,SAAS;gBACP;YACF;YACA;YACA,YAAY,GAAG,CAAC;YAChB;wCAAO;oBACL,YAAY,MAAM,CAAC;gBACrB;;QACF;+BAAG;QAAC;KAAQ;IACZ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IAC3C,MAAM,mBAAmB,QAAQ,YAAY;IAC7C,MAAM,eAAe,QAAQ,WAAW;IACxC,MAAM,gBAAgB,QAAQ,WAAW;IACzC,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,eACF;YACF,IAAI,CAAC,gBAAgB,CAAC,eACpB;YACF,gBAAgB;YAChB,0JAA+B,IAAI;wCAAC,CAAC;oBACnC,UAAU,IAAI,IAAI,OAAO,CAAC,cAAc;wBAAE,SAAS;oBAAc;gBACnE;uCAAG,KAAK;wCAAC,CAAC;oBACR,QAAQ,GAAG,CAAC;oBACZ,QAAQ,KAAK,CAAC;gBAChB;;YACA;wCAAO;oBACL,gBAAgB;gBAClB;;QACF;+BAAG;QAAC;QAAc;KAAc;IAChC,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,CAAC,kBACH;YACF,IAAI,CAAC,QACH;YACF,MAAM,UAAU,OAAO,SAAS,CAAC;YACjC,QAAQ,IAAI,CACV;wCACA,CAAC;oBACC,IAAI,SAAS,mBAAmB,SAAS,YAAY,QAAQ,MAAM,KAAK,cAAc,OAAO,EAAE;wBAC7F,YAAY,OAAO;oDAAC,CAAC,MAAQ;;oBAC/B;gBACF;;YAEF;wCAAO;oBACL,QAAQ,WAAW;gBACrB;;QACF;+BAAG;QAAC;QAAQ;KAAiB;IAC7B,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,SAAS;gBACP,MAAM,cACJ,aAAa;gBACb,OAAO,UAAU;gBAEnB,IAAI,CAAC,eAAe,OAAO,gBAAgB,UACzC;gBACF,cAAc;YAChB;YACA;YACA,OAAO,gBAAgB,CAAC,sBAAsB;YAC9C;wCAAO;oBACL,OAAO,mBAAmB,CAAC,sBAAsB;gBACnD;;QACF;+BAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;4CAAE;YACjC,OAAO,QAAQ,KAAK;oDAAI,CAAC,GAAG,IAAM,KAAK,cAAc,MAAM,CAAC,EAAE,IAAI;;QACpE;2CAAG;QAAC,cAAc;QAAM,QAAQ;KAAK;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAC3D,OAAO,aAAa,aAClB,sDAAsD;IACtD,0BACE;IAEN,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,CAAC,cACH;YACF,IAAI,OAAO,aAAa,YAAY;gBAClC,MAAM,MAAM,SAAS;gBACrB,IAAI,eAAe,SAAS;oBAC1B,IAAI,IAAI,CAAC;gBACX,OAAO;oBACL,oBAAoB;gBACtB;YACF,OAAO;gBACL,oBAAoB;YACtB;QACF;+BAAG;QAAC;QAAU;KAAa;IAC3B,OAAO,oBAAoB;AAC7B;GApOI;KAAA", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,4SAAC,wRAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,4SAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,4SAAC,wRAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,4SAAC,wRAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,4SAAC,wRAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,4SAAC,+SAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,4SAAC,wRAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,4SAAC,wRAAA,CAAA,WAAgC;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,4SAAC,wRAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,4SAAC,wRAAA,CAAA,YAAiC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/header/logo.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 24, height: 24, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,4SAAC,sRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,4SAAC,sRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,4SAAC,sRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,4SAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,4SAAC,sRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,4SAAC,sRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,4SAAC,sRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,4SAAC,sRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4SAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,4SAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,4SAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4SAAC,iSAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,4SAAC,sRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,4SAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,4SAAC,sRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,4SAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,4SAAC,iTAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,4SAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,4SAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,4SAAC,kRAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/header/user-profile.tsx"], "sourcesContent": ["'use client';\n\nimport { Button } from '@repo/design-system/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@repo/design-system/components/ui/dropdown-menu';\nimport { Avatar, AvatarFallback, AvatarImage } from '@repo/design-system/components/ui/avatar';\nimport { LogOut, Settings, User } from 'lucide-react';\nimport Link from 'next/link';\n\ntype AuthUser = {\n  id: string;\n  fullName: string | null;\n  firstName: string | null;\n  lastName: string | null;\n  emailAddresses: Array<{ emailAddress: string }>;\n  imageUrl: string;\n};\n\ntype UserProfileProps = {\n  user: AuthUser;\n};\n\nexport const UserProfile = ({ user }: UserProfileProps) => {\n  const handleSignOut = () => {\n    // Redirect to app.cubent.dev sign out\n    window.location.href = 'https://app.cubent.dev/sign-out';\n  };\n\n  const getInitials = (name: string | null) => {\n    if (!name) return 'U';\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const displayName = user.fullName || user.firstName || user.emailAddresses[0]?.emailAddress || 'User';\n  const email = user.emailAddresses[0]?.emailAddress;\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" className=\"relative h-10 w-10 rounded-full\">\n          <Avatar className=\"h-10 w-10\">\n            <AvatarImage src={user.imageUrl} alt={displayName} />\n            <AvatarFallback>{getInitials(user.fullName)}</AvatarFallback>\n          </Avatar>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">{displayName}</p>\n            {email && (\n              <p className=\"text-xs leading-none text-muted-foreground\">\n                {email}\n              </p>\n            )}\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem asChild>\n          <Link href=\"https://app.cubent.dev\" className=\"flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>Dashboard</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link href=\"https://app.cubent.dev/settings\" className=\"flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>Settings</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem onClick={handleSignOut} className=\"flex items-center\">\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>Sign out</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AACA;AAbA;;;;;;;AA4BO,MAAM,cAAc,CAAC,EAAE,IAAI,EAAoB;IACpD,MAAM,gBAAgB;QACpB,sCAAsC;QACtC,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,SAAS,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,gBAAgB;IAC/F,MAAM,QAAQ,KAAK,cAAc,CAAC,EAAE,EAAE;IAEtC,qBACE,4SAAC,wKAAA,CAAA,eAAY;;0BACX,4SAAC,wKAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,4SAAC,8JAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,4SAAC,8JAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,4SAAC,8JAAA,CAAA,cAAW;gCAAC,KAAK,KAAK,QAAQ;gCAAE,KAAK;;;;;;0CACtC,4SAAC,8JAAA,CAAA,iBAAc;0CAAE,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;0BAIhD,4SAAC,wKAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,4SAAC,wKAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAE,WAAU;8CAAoC;;;;;;gCAChD,uBACC,4SAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;kCAKT,4SAAC,wKAAA,CAAA,wBAAqB;;;;;kCACtB,4SAAC,wKAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,4SAAC,8QAAA,CAAA,UAAI;4BAAC,MAAK;4BAAyB,WAAU;;8CAC5C,4SAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,4SAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,4SAAC,wKAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,4SAAC,8QAAA,CAAA,UAAI;4BAAC,MAAK;4BAAkC,WAAU;;8CACrD,4SAAC,iSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4SAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,4SAAC,wKAAA,CAAA,wBAAqB;;;;;kCACtB,4SAAC,wKAAA,CAAA,mBAAgB;wBAAC,SAAS;wBAAe,WAAU;;0CAClD,4SAAC,iSAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,4SAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;KA7Da", "debugId": null}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/hooks/useAuthStatus.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ntype AuthUser = {\n  id: string;\n  fullName: string | null;\n  firstName: string | null;\n  lastName: string | null;\n  emailAddresses: Array<{ emailAddress: string }>;\n  imageUrl: string;\n};\n\ntype AuthStatus = {\n  isAuthenticated: boolean;\n  user: AuthUser | null;\n  isLoading: boolean;\n};\n\nexport function useAuthStatus(): AuthStatus {\n  const [authStatus, setAuthStatus] = useState<AuthStatus>({\n    isAuthenticated: false,\n    user: null,\n    isLoading: true,\n  });\n\n  useEffect(() => {\n    function checkAuthStatus() {\n      try {\n        console.log('[AUTH] Checking authentication status...');\n        console.log('[AUTH] All cookies:', document.cookie);\n\n        // Check if we have our custom auth token\n        const authTokenCookie = document.cookie\n          .split('; ')\n          .find(row => row.startsWith('cubent_auth_token='));\n\n        console.log('[AUTH] Auth token cookie found:', !!authTokenCookie);\n\n        if (!authTokenCookie) {\n          console.log('[AUTH] No auth token cookie, setting not authenticated');\n          setAuthStatus({\n            isAuthenticated: false,\n            user: null,\n            isLoading: false,\n          });\n          return;\n        }\n\n        // Extract and decode the token\n        const rawToken = authTokenCookie.split('=')[1];\n        const token = decodeURIComponent(rawToken); // URL decode first\n        console.log('[AUTH] Token found:', token.substring(0, 50) + '...');\n\n        try {\n          const userData = JSON.parse(atob(token));\n          console.log('[AUTH] User data decoded:', userData);\n\n          // Check if token is not too old (7 days)\n          const tokenAge = Date.now() - userData.timestamp;\n          const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds\n\n          if (tokenAge > maxAge) {\n            console.log('[AUTH] Token expired, setting not authenticated');\n            setAuthStatus({\n              isAuthenticated: false,\n              user: null,\n              isLoading: false,\n            });\n            return;\n          }\n\n          setAuthStatus({\n            isAuthenticated: true,\n            user: userData,\n            isLoading: false,\n          });\n        } catch (decodeError) {\n          console.error('[AUTH] Failed to decode token:', decodeError);\n          setAuthStatus({\n            isAuthenticated: false,\n            user: null,\n            isLoading: false,\n          });\n        }\n      } catch (error) {\n        console.error('[AUTH] Failed to check auth status:', error);\n        setAuthStatus({\n          isAuthenticated: false,\n          user: null,\n          isLoading: false,\n        });\n      }\n    }\n\n    checkAuthStatus();\n  }, []);\n\n  return authStatus;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAmBO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,iBAAiB;QACjB,MAAM;QACN,WAAW;IACb;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS;gBACP,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM;oBAElD,yCAAyC;oBACzC,MAAM,kBAAkB,SAAS,MAAM,CACpC,KAAK,CAAC,MACN,IAAI;mFAAC,CAAA,MAAO,IAAI,UAAU,CAAC;;oBAE9B,QAAQ,GAAG,CAAC,mCAAmC,CAAC,CAAC;oBAEjD,IAAI,CAAC,iBAAiB;wBACpB,QAAQ,GAAG,CAAC;wBACZ,cAAc;4BACZ,iBAAiB;4BACjB,MAAM;4BACN,WAAW;wBACb;wBACA;oBACF;oBAEA,+BAA+B;oBAC/B,MAAM,WAAW,gBAAgB,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC9C,MAAM,QAAQ,mBAAmB,WAAW,mBAAmB;oBAC/D,QAAQ,GAAG,CAAC,uBAAuB,MAAM,SAAS,CAAC,GAAG,MAAM;oBAE5D,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK;wBACjC,QAAQ,GAAG,CAAC,6BAA6B;wBAEzC,yCAAyC;wBACzC,MAAM,WAAW,KAAK,GAAG,KAAK,SAAS,SAAS;wBAChD,MAAM,SAAS,IAAI,KAAK,KAAK,KAAK,MAAM,yBAAyB;wBAEjE,IAAI,WAAW,QAAQ;4BACrB,QAAQ,GAAG,CAAC;4BACZ,cAAc;gCACZ,iBAAiB;gCACjB,MAAM;gCACN,WAAW;4BACb;4BACA;wBACF;wBAEA,cAAc;4BACZ,iBAAiB;4BACjB,MAAM;4BACN,WAAW;wBACb;oBACF,EAAE,OAAO,aAAa;wBACpB,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,cAAc;4BACZ,iBAAiB;4BACjB,MAAM;4BACN,WAAW;wBACb;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uCAAuC;oBACrD,cAAc;wBACZ,iBAAiB;wBACjB,MAAM;wBACN,WAAW;oBACb;gBACF;YACF;YAEA;QACF;kCAAG,EAAE;IAEL,OAAO;AACT;GAhFgB", "debugId": null}}, {"offset": {"line": 1932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/header/index.tsx"], "sourcesContent": ["'use client';\n\nimport { env } from '@/env';\n\nimport { Button } from '@repo/design-system/components/ui/button';\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from '@repo/design-system/components/ui/navigation-menu';\nimport { Menu, MoveRight, X } from 'lucide-react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\n\nimport type { Dictionary } from '@repo/internationalization';\nimport Image from 'next/image';\n\nimport Logo from './logo.svg';\nimport { UserProfile } from './user-profile';\nimport { useAuthStatus } from '../../hooks/useAuthStatus';\n\ntype HeaderProps = {\n  dictionary: Dictionary;\n};\n\nexport const Header = ({ dictionary }: HeaderProps) => {\n  const { isAuthenticated, user, isLoading } = useAuthStatus();\n  const pathname = usePathname();\n\n  const navigationItems = [\n    {\n      title: 'Pricing',\n      href: '/pricing',\n      description: '',\n    },\n    {\n      title: dictionary.web.header.docs,\n      href: 'https://docs.cubent.dev/',\n      description: '',\n    },\n    {\n      title: dictionary.web.header.blog,\n      href: '/blog',\n      description: '',\n    },\n    {\n      title: 'Company',\n      description: 'Learn more about Cubent',\n      items: [\n        {\n          title: 'About Us',\n          href: '/about',\n        },\n        {\n          title: 'Contact',\n          href: '/contact',\n        },\n      ],\n    },\n  ];\n\n  // Helper function to check if a navigation item is active\n  const isActiveItem = (href: string) => {\n    if (href === '/pricing') return pathname === '/pricing';\n    if (href === '/blog') return pathname.startsWith('/blog');\n    return false;\n  };\n\n  const [isOpen, setOpen] = useState(false);\n  return (\n    <header className=\"sticky top-0 left-0 z-40 w-full bg-background/20 backdrop-blur-md supports-[backdrop-filter]:bg-background/10\">\n      {/* Early Access Banner */}\n      <div className=\"w-full bg-gray-800/60 border-b border-gray-600/20 text-gray-200 py-2.5 px-4 text-center text-sm backdrop-blur-sm\">\n        <span className=\"font-medium\">Early Access:</span> We released the Byak plan -\n        <Button variant=\"link\" className=\"text-gray-200 hover:text-white underline p-0 ml-1 h-auto font-medium text-sm\" asChild>\n          <Link href=\"https://app.cubent.dev/sign-in\">\n            Start your free trial\n          </Link>\n        </Button>\n      </div>\n\n      <div className=\"border-b\">\n        <div className=\"relative w-full max-w-none flex min-h-20 flex-row items-center justify-between\" style={{paddingInline: 'clamp(1rem, 2.5%, 2rem)'}}>\n        <Link href=\"/\" className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\">\n          <Image\n            src={Logo}\n            alt=\"Cubent Logo\"\n            width={40}\n            height={40}\n            className=\"dark:invert\"\n          />\n          <p className=\"whitespace-nowrap font-semibold\">Cubent</p>\n        </Link>\n        <div className=\"hidden flex-row items-center justify-center gap-3 lg:flex absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2\">\n          <NavigationMenu className=\"flex items-center justify-center\">\n            <NavigationMenuList className=\"flex flex-row justify-center gap-3\">\n              {navigationItems.map((item) => (\n                <NavigationMenuItem key={item.title}>\n                  {item.href ? (\n                    <>\n                      <NavigationMenuLink asChild>\n                        <Button\n                          variant=\"ghost\"\n                          asChild\n                          className={isActiveItem(item.href) ? 'bg-neutral-800/50 text-white' : ''}\n                        >\n                          <Link\n                            href={item.href}\n                            target={item.href.startsWith('http') ? '_blank' : undefined}\n                            rel={item.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                          >\n                            {item.title}\n                          </Link>\n                        </Button>\n                      </NavigationMenuLink>\n                    </>\n                  ) : (\n                    <>\n                      <NavigationMenuTrigger className=\"font-medium text-sm bg-transparent hover:bg-transparent data-[state=open]:bg-transparent\">\n                        {item.title}\n                      </NavigationMenuTrigger>\n                      <NavigationMenuContent className=\"!w-[450px] p-4\">\n                        <div className=\"flex flex-col gap-4\">\n                          <div className=\"flex flex-col\">\n                            <p className=\"text-base\">{item.title}</p>\n                            <p className=\"text-muted-foreground text-sm\">\n                              {item.description}\n                            </p>\n                          </div>\n                          <div className=\"flex flex-col text-sm\">\n                            {item.items?.map((subItem, idx) => (\n                              <NavigationMenuLink\n                                href={subItem.href}\n                                key={idx}\n                                className=\"flex flex-row items-center justify-between rounded px-4 py-2 hover:bg-muted\"\n                              >\n                                <span>{subItem.title}</span>\n                                <MoveRight className=\"h-4 w-4 text-muted-foreground\" />\n                              </NavigationMenuLink>\n                            ))}\n                          </div>\n                        </div>\n                      </NavigationMenuContent>\n                    </>\n                  )}\n                </NavigationMenuItem>\n              ))}\n            </NavigationMenuList>\n          </NavigationMenu>\n        </div>\n        <div className=\"flex justify-end gap-2\">\n          {isLoading ? (\n            <div className=\"h-10 w-10 animate-pulse bg-gray-200 rounded-full\"></div>\n          ) : isAuthenticated && user ? (\n            <UserProfile user={user} />\n          ) : (\n            <Button variant=\"ghost\" asChild className=\"text-white hover:text-white hover:bg-white/10 h-10 flex items-center\">\n              <Link href=\"https://app.cubent.dev/sign-in\">\n                Sign In\n              </Link>\n            </Button>\n          )}\n          <Button asChild className=\"hidden md:inline-flex h-10 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white border-0\">\n            <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" className=\"flex flex-row items-center gap-2 px-4 py-2 whitespace-nowrap\">\n              <span className=\"shrink-0 text-sm\">Download Cubent</span>\n            </Link>\n          </Button>\n        </div>\n        <div className=\"flex w-12 shrink items-end justify-end lg:hidden\">\n          <Button variant=\"ghost\" onClick={() => setOpen(!isOpen)}>\n            {isOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n          </Button>\n          {isOpen && (\n            <div className=\"container absolute top-20 right-0 flex w-full flex-col gap-8 border-t bg-background py-4 shadow-lg px-4 sm:px-6 lg:px-8\">\n              {navigationItems.map((item) => (\n                <div key={item.title}>\n                  <div className=\"flex flex-col gap-2\">\n                    {item.href ? (\n                      <Link\n                        href={item.href}\n                        className={`flex items-center justify-between ${\n                          isActiveItem(item.href) ? 'bg-neutral-800/50 text-white rounded px-2 py-1' : ''\n                        }`}\n                        target={\n                          item.href.startsWith('http') ? '_blank' : undefined\n                        }\n                        rel={\n                          item.href.startsWith('http')\n                            ? 'noopener noreferrer'\n                            : undefined\n                        }\n                      >\n                        <span className=\"text-lg\">{item.title}</span>\n                        <MoveRight className=\"h-4 w-4 stroke-1 text-muted-foreground\" />\n                      </Link>\n                    ) : (\n                      <p className=\"text-lg\">{item.title}</p>\n                    )}\n                    {item.items?.map((subItem) => (\n                      <Link\n                        key={subItem.title}\n                        href={subItem.href}\n                        className=\"flex items-center justify-between\"\n                      >\n                        <span className=\"text-muted-foreground\">\n                          {subItem.title}\n                        </span>\n                        <MoveRight className=\"h-4 w-4 stroke-1\" />\n                      </Link>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n      </div>\n    </header>\n  );\n};\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAQA;AAAA;AAAA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;;;AAvBA;;;;;;;;;;;AA6BO,MAAM,SAAS,CAAC,EAAE,UAAU,EAAe;;IAChD,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACzD,MAAM,WAAW,CAAA,GAAA,oPAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,OAAO,WAAW,GAAG,CAAC,MAAM,CAAC,IAAI;YACjC,MAAM;YACN,aAAa;QACf;QACA;YACE,OAAO,WAAW,GAAG,CAAC,MAAM,CAAC,IAAI;YACjC,MAAM;YACN,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,OAAO;oBACP,MAAM;gBACR;aACD;QACH;KACD;IAED,0DAA0D;IAC1D,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,YAAY,OAAO,aAAa;QAC7C,IAAI,SAAS,SAAS,OAAO,SAAS,UAAU,CAAC;QACjD,OAAO;IACT;IAEA,MAAM,CAAC,QAAQ,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,qBACE,4SAAC;QAAO,WAAU;;0BAEhB,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAK,WAAU;kCAAc;;;;;;oBAAoB;kCAClD,4SAAC,8JAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAO,WAAU;wBAA+E,OAAO;kCACrH,cAAA,4SAAC,8QAAA,CAAA,UAAI;4BAAC,MAAK;sCAAiC;;;;;;;;;;;;;;;;;0BAMhD,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;oBAAiF,OAAO;wBAAC,eAAe;oBAAyB;;sCAChJ,4SAAC,8QAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,4SAAC,+OAAA,CAAA,UAAK;oCACJ,KAAK,mWAAA,CAAA,UAAI;oCACT,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,4SAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAEjD,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,0KAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,4SAAC,0KAAA,CAAA,qBAAkB;oCAAC,WAAU;8CAC3B,gBAAgB,GAAG,CAAC,CAAC,qBACpB,4SAAC,0KAAA,CAAA,qBAAkB;sDAChB,KAAK,IAAI,iBACR;0DACE,cAAA,4SAAC,0KAAA,CAAA,qBAAkB;oDAAC,OAAO;8DACzB,cAAA,4SAAC,8JAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,OAAO;wDACP,WAAW,aAAa,KAAK,IAAI,IAAI,iCAAiC;kEAEtE,cAAA,4SAAC,8QAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;4DAClD,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;sEAE3D,KAAK,KAAK;;;;;;;;;;;;;;;;8EAMnB;;kEACE,4SAAC,0KAAA,CAAA,wBAAqB;wDAAC,WAAU;kEAC9B,KAAK,KAAK;;;;;;kEAEb,4SAAC,0KAAA,CAAA,wBAAqB;wDAAC,WAAU;kEAC/B,cAAA,4SAAC;4DAAI,WAAU;;8EACb,4SAAC;oEAAI,WAAU;;sFACb,4SAAC;4EAAE,WAAU;sFAAa,KAAK,KAAK;;;;;;sFACpC,4SAAC;4EAAE,WAAU;sFACV,KAAK,WAAW;;;;;;;;;;;;8EAGrB,4SAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK,EAAE,IAAI,CAAC,SAAS,oBACzB,4SAAC,0KAAA,CAAA,qBAAkB;4EACjB,MAAM,QAAQ,IAAI;4EAElB,WAAU;;8FAEV,4SAAC;8FAAM,QAAQ,KAAK;;;;;;8FACpB,4SAAC,uSAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;;2EAJhB;;;;;;;;;;;;;;;;;;;;;;;2CApCI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;sCAqD3C,4SAAC;4BAAI,WAAU;;gCACZ,0BACC,4SAAC;oCAAI,WAAU;;;;;2CACb,mBAAmB,qBACrB,4SAAC,+KAAA,CAAA,cAAW;oCAAC,MAAM;;;;;yDAEnB,4SAAC,8JAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,OAAO;oCAAC,WAAU;8CACxC,cAAA,4SAAC,8QAAA,CAAA,UAAI;wCAAC,MAAK;kDAAiC;;;;;;;;;;;8CAKhD,4SAAC,8JAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACxB,cAAA,4SAAC,8QAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoE,WAAU;kDACvF,cAAA,4SAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;sCAIzC,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,8JAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,SAAS,IAAM,QAAQ,CAAC;8CAC7C,uBAAS,4SAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,4SAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAEvD,wBACC,4SAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,4SAAC;sDACC,cAAA,4SAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,iBACR,4SAAC,8QAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,kCAAkC,EAC5C,aAAa,KAAK,IAAI,IAAI,mDAAmD,IAC7E;wDACF,QACE,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;wDAE5C,KACE,KAAK,IAAI,CAAC,UAAU,CAAC,UACjB,wBACA;;0EAGN,4SAAC;gEAAK,WAAU;0EAAW,KAAK,KAAK;;;;;;0EACrC,4SAAC,uSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;6EAGvB,4SAAC;wDAAE,WAAU;kEAAW,KAAK,KAAK;;;;;;oDAEnC,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,4SAAC,8QAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAU;;8EAEV,4SAAC;oEAAK,WAAU;8EACb,QAAQ,KAAK;;;;;;8EAEhB,4SAAC,uSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;2DAPhB,QAAQ,KAAK;;;;;;;;;;;2CAzBhB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CpC;GAnMa;;QACkC,6JAAA,CAAA,gBAAa;QACzC,oPAAA,CAAA,cAAW;;;KAFjB", "debugId": null}}]}