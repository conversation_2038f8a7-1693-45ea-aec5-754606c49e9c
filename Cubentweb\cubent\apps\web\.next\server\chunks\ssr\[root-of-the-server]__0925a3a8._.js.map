{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/security/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'Security | Cubent',\n  description: 'Security practices and policies for Cubent AI coding assistant',\n};\n\nexport default function SecurityPolicy() {\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      <div className=\"container mx-auto px-4 py-16 max-w-4xl\">\n        <h1 className=\"text-4xl font-bold mb-8\">Security</h1>\n        <p className=\"text-gray-400 mb-8\">Last updated: January 2, 2025</p>\n        \n        <div className=\"prose prose-invert max-w-none\">\n          <p className=\"text-lg mb-6\">\n            Keeping your source code and developer environment secure is our top priority. \n            This page outlines how we approach security for Cubent.\n          </p>\n\n          <div className=\"bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 mb-8\">\n            <p className=\"text-yellow-200\">\n              <strong>Security Contact:</strong> For security-related questions or to report vulnerabilities, \n              please contact us at <a href=\"mailto:<EMAIL>\" className=\"text-yellow-400 hover:text-yellow-300\"><EMAIL></a>\n            </p>\n          </div>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Infrastructure Security</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Cloud Infrastructure</h3>\n          <p className=\"mb-4\">\n            Our infrastructure is built on industry-leading cloud platforms with robust security measures:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li><strong>Primary Hosting:</strong> AWS with servers primarily in the US</li>\n            <li><strong>Content Delivery:</strong> Cloudflare for enhanced performance and security</li>\n            <li><strong>Database Security:</strong> Encrypted databases with access controls</li>\n            <li><strong>Network Security:</strong> VPC isolation and network-level controls</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">AI Model Providers</h3>\n          <p className=\"mb-4\">\n            We partner with leading AI providers to deliver our services. All providers have \n            zero data retention agreements to ensure your code is not stored or used for training:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>OpenAI - Zero data retention agreement</li>\n            <li>Anthropic - Zero data retention agreement</li>\n            <li>Google Cloud Vertex API - Zero data retention agreement</li>\n            <li>Custom models hosted on secure infrastructure</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Access Controls</h3>\n          <p className=\"mb-4\">\n            We implement strict access controls across our infrastructure:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Least-privilege access for all team members</li>\n            <li>Multi-factor authentication required</li>\n            <li>Regular access reviews and audits</li>\n            <li>Secure secrets management</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Client Security</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Application Security</h3>\n          <p className=\"mb-4\">\n            Cubent is built with security best practices in mind:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Regular security updates and patches</li>\n            <li>Secure communication protocols (HTTPS/TLS)</li>\n            <li>Input validation and sanitization</li>\n            <li>Protection against common web vulnerabilities</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">API Endpoints</h3>\n          <p className=\"mb-4\">\n            Our application communicates with the following secure domains:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li><code>api.cubent.dev</code> - Main API requests</li>\n            <li><code>cdn.cubent.dev</code> - Static content delivery</li>\n            <li><code>auth.cubent.dev</code> - Authentication services</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">AI Requests and Data Handling</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Request Processing</h3>\n          <p className=\"mb-4\">\n            To provide AI-powered coding assistance, Cubent processes various types of requests:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Chat conversations and code questions</li>\n            <li>Code completion and suggestions</li>\n            <li>Code analysis and optimization recommendations</li>\n            <li>Documentation and explanation requests</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Data Flow Security</h3>\n          <p className=\"mb-4\">\n            All AI requests follow a secure data flow:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Encrypted transmission from client to our servers</li>\n            <li>Secure processing on our AWS infrastructure</li>\n            <li>Encrypted communication with AI model providers</li>\n            <li>Immediate deletion of temporary processing data</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Privacy Mode Guarantee</h2>\n          \n          <div className=\"bg-green-900/20 border border-green-600/30 rounded-lg p-4 mb-6\">\n            <h3 className=\"text-xl font-medium mb-3 text-green-200\">Enhanced Privacy Protection</h3>\n            <p className=\"text-green-100 mb-4\">\n              Privacy Mode provides the highest level of data protection for your code and conversations.\n            </p>\n          </div>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Privacy Mode Features</h3>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Code data is never stored by AI model providers</li>\n            <li>No data used for model training purposes</li>\n            <li>Temporary processing only for immediate responses</li>\n            <li>Separate infrastructure for privacy mode users</li>\n            <li>Enhanced logging restrictions and data isolation</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Implementation</h3>\n          <p className=\"mb-4\">\n            Our privacy mode implementation includes:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Parallel infrastructure for privacy and non-privacy requests</li>\n            <li>Header-based request routing with redundant checks</li>\n            <li>Default privacy mode assumption for safety</li>\n            <li>Team-level privacy mode enforcement</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Code Indexing Security</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Secure Indexing Process</h3>\n          <p className=\"mb-4\">\n            When codebase indexing is enabled, we implement several security measures:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Respect for .gitignore and .cubentignore files</li>\n            <li>File path obfuscation using client-side encryption</li>\n            <li>Merkle tree-based change detection</li>\n            <li>Encrypted storage of embeddings and metadata</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Privacy Mode Indexing</h3>\n          <p className=\"mb-4\">\n            For privacy mode users, additional protections apply:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>No plaintext code stored on servers</li>\n            <li>Local processing of search results</li>\n            <li>Encrypted embeddings only</li>\n            <li>Automatic data deletion upon account termination</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Compliance and Certifications</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Security Standards</h3>\n          <p className=\"mb-4\">\n            We are committed to maintaining high security standards and are working towards:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>SOC 2 Type II certification (in progress)</li>\n            <li>Regular third-party security assessments</li>\n            <li>Penetration testing by reputable security firms</li>\n            <li>Compliance with industry best practices</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Account Security</h2>\n          \n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Account Protection</h3>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Strong password requirements</li>\n            <li>Multi-factor authentication support</li>\n            <li>Session management and timeout controls</li>\n            <li>Account activity monitoring</li>\n          </ul>\n\n          <h3 className=\"text-xl font-medium mt-6 mb-3\">Data Deletion</h3>\n          <p className=\"mb-4\">\n            You can delete your account and all associated data at any time. We guarantee \n            complete data removal within 30 days of deletion request.\n          </p>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Vulnerability Disclosure</h2>\n          \n          <p className=\"mb-4\">\n            We take security vulnerabilities seriously and encourage responsible disclosure:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Report vulnerabilities to <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a></li>\n            <li>We commit to acknowledging reports within 5 business days</li>\n            <li>Critical issues will be addressed immediately</li>\n            <li>Security advisories published for resolved issues</li>\n          </ul>\n\n          <h2 className=\"text-2xl font-semibold mt-8 mb-4\">Contact Information</h2>\n          \n          <p className=\"mb-4\">\n            For security-related questions or concerns, please contact us:\n          </p>\n          <ul className=\"list-disc pl-6 mb-4\">\n            <li>Security Team: <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a></li>\n            <li>General Inquiries: <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300\"><EMAIL></a></li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6VAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAElC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAE,WAAU;sCAAe;;;;;;sCAK5B,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAE,WAAU;;kDACX,6VAAC;kDAAO;;;;;;oCAA0B;kDACb,6VAAC;wCAAE,MAAK;wCAA6B,WAAU;kDAAwC;;;;;;;;;;;;;;;;;sCAIhH,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;;sDAAG,6VAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CACrC,6VAAC;;sDAAG,6VAAC;sDAAO;;;;;;wCAA0B;;;;;;;8CACtC,6VAAC;;sDAAG,6VAAC;sDAAO;;;;;;wCAA2B;;;;;;;8CACvC,6VAAC;;sDAAG,6VAAC;sDAAO;;;;;;wCAA0B;;;;;;;;;;;;;sCAGxC,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAIpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;;sDAAG,6VAAC;sDAAK;;;;;;wCAAqB;;;;;;;8CAC/B,6VAAC;;sDAAG,6VAAC;sDAAK;;;;;;wCAAqB;;;;;;;8CAC/B,6VAAC;;sDAAG,6VAAC;sDAAK;;;;;;wCAAsB;;;;;;;;;;;;;sCAGlC,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6VAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAKrC,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAKpB,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;;wCAAG;sDAA0B,6VAAC;4CAAE,MAAK;4CAA6B,WAAU;sDAAoC;;;;;;;;;;;;8CACjH,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;8CACJ,6VAAC;8CAAG;;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6VAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC;;wCAAG;sDAAe,6VAAC;4CAAE,MAAK;4CAA6B,WAAU;sDAAoC;;;;;;;;;;;;8CACtG,6VAAC;;wCAAG;sDAAmB,6VAAC;4CAAE,MAAK;4CAAyB,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlH", "debugId": null}}]}