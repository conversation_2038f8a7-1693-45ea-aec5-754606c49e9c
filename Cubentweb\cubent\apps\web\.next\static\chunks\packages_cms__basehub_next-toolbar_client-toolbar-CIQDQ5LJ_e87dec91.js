(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ __turbopack_context__.s({
    "ClientToolbar": (()=>ClientToolbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$chunk$2d$YSQDPG26$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js [app-client] (ecmascript)");
// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
"use client";
;
// ../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js
var require_lodash = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$chunk$2d$YSQDPG26$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js" (exports, module) {
        var FUNC_ERROR_TEXT = "Expected a function";
        var NAN = 0 / 0;
        var symbolTag = "[object Symbol]";
        var reTrim = /^\s+|\s+$/g;
        var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
        var reIsBinary = /^0b[01]+$/i;
        var reIsOctal = /^0o[0-7]+$/i;
        var freeParseInt = parseInt;
        var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
        var freeSelf = typeof self == "object" && self && self.Object === Object && self;
        var root = freeGlobal || freeSelf || Function("return this")();
        var objectProto = Object.prototype;
        var objectToString = objectProto.toString;
        var nativeMax = Math.max;
        var nativeMin = Math.min;
        var now = function() {
            return root.Date.now();
        };
        function debounce3(func, wait, options) {
            var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
            if (typeof func != "function") {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            wait = toNumber(wait) || 0;
            if (isObject(options)) {
                leading = !!options.leading;
                maxing = "maxWait" in options;
                maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
                trailing = "trailing" in options ? !!options.trailing : trailing;
            }
            function invokeFunc(time) {
                var args = lastArgs, thisArg = lastThis;
                lastArgs = lastThis = void 0;
                lastInvokeTime = time;
                result = func.apply(thisArg, args);
                return result;
            }
            function leadingEdge(time) {
                lastInvokeTime = time;
                timerId = setTimeout(timerExpired, wait);
                return leading ? invokeFunc(time) : result;
            }
            function remainingWait(time) {
                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, result2 = wait - timeSinceLastCall;
                return maxing ? nativeMin(result2, maxWait - timeSinceLastInvoke) : result2;
            }
            function shouldInvoke(time) {
                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
                return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
            }
            function timerExpired() {
                var time = now();
                if (shouldInvoke(time)) {
                    return trailingEdge(time);
                }
                timerId = setTimeout(timerExpired, remainingWait(time));
            }
            function trailingEdge(time) {
                timerId = void 0;
                if (trailing && lastArgs) {
                    return invokeFunc(time);
                }
                lastArgs = lastThis = void 0;
                return result;
            }
            function cancel() {
                if (timerId !== void 0) {
                    clearTimeout(timerId);
                }
                lastInvokeTime = 0;
                lastArgs = lastCallTime = lastThis = timerId = void 0;
            }
            function flush() {
                return timerId === void 0 ? result : trailingEdge(now());
            }
            function debounced() {
                var time = now(), isInvoking = shouldInvoke(time);
                lastArgs = arguments;
                lastThis = this;
                lastCallTime = time;
                if (isInvoking) {
                    if (timerId === void 0) {
                        return leadingEdge(lastCallTime);
                    }
                    if (maxing) {
                        timerId = setTimeout(timerExpired, wait);
                        return invokeFunc(lastCallTime);
                    }
                }
                if (timerId === void 0) {
                    timerId = setTimeout(timerExpired, wait);
                }
                return result;
            }
            debounced.cancel = cancel;
            debounced.flush = flush;
            return debounced;
        }
        function isObject(value) {
            var type = typeof value;
            return !!value && (type == "object" || type == "function");
        }
        function isObjectLike(value) {
            return !!value && typeof value == "object";
        }
        function isSymbol(value) {
            return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
        }
        function toNumber(value) {
            if (typeof value == "number") {
                return value;
            }
            if (isSymbol(value)) {
                return NAN;
            }
            if (isObject(value)) {
                var other = typeof value.valueOf == "function" ? value.valueOf() : value;
                value = isObject(other) ? other + "" : other;
            }
            if (typeof value != "string") {
                return value === 0 ? value : +value;
            }
            value = value.replace(reTrim, "");
            var isBinary = reIsBinary.test(value);
            return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
        }
        module.exports = debounce3;
    }
});
;
// esbuild-scss-modules-plugin:./toolbar.module.scss
var digest = "dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7";
var classes = {
    "wrapper": "_wrapper_ypbb5_1",
    "branch": "_branch_ypbb5_32",
    "in": "_in_ypbb5_1",
    "root": "_root_ypbb5_53",
    "draft": "_draft_ypbb5_67",
    "breathe": "_breathe_ypbb5_1",
    "tooltipWrapper": "_tooltipWrapper_ypbb5_122",
    "tooltip": "_tooltip_ypbb5_122",
    "dragHandle": "_dragHandle_ypbb5_131",
    "dragging": "_dragging_ypbb5_135",
    "forceVisible": "_forceVisible_ypbb5_158",
    "top": "_top_ypbb5_161",
    "bottom": "_bottom_ypbb5_172",
    "right": "_right_ypbb5_182",
    "left": "_left_ypbb5_193",
    "branchSelect": "_branchSelect_ypbb5_219",
    "branchSelectIcon": "_branchSelectIcon_ypbb5_245"
};
var css = `._wrapper_ypbb5_1 {
  box-sizing: border-box;
  font-size: 16px;
}
._wrapper_ypbb5_1 *,
._wrapper_ypbb5_1 *:before,
._wrapper_ypbb5_1 *:after {
  box-sizing: inherit;
}
._wrapper_ypbb5_1 h1,
._wrapper_ypbb5_1 h2,
._wrapper_ypbb5_1 h3,
._wrapper_ypbb5_1 h4,
._wrapper_ypbb5_1 h5,
._wrapper_ypbb5_1 h6,
._wrapper_ypbb5_1 p,
._wrapper_ypbb5_1 ol,
._wrapper_ypbb5_1 ul {
  margin: 0;
  padding: 0;
  font-weight: normal;
}
._wrapper_ypbb5_1 ol,
._wrapper_ypbb5_1 ul {
  list-style: none;
}
._wrapper_ypbb5_1 img {
  max-width: 100%;
  height: auto;
}

._branch_ypbb5_32 {
  padding-left: 9px;
  padding-right: 12px;
  height: 100%;
  display: flex;
  align-items: center;
  font-weight: 500;
  user-select: none;
}

._wrapper_ypbb5_1 {
  position: fixed;
  bottom: 32px;
  right: 32px;
  background: #0c0c0c;
  z-index: 1000;
  border-radius: 7px;
  animation: _in_ypbb5_1 0.3s ease-out;
  display: flex;
}

._root_ypbb5_53 {
  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,
    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;
  border-radius: 6px;
  height: 36px;
  color: white;
  display: flex;
  border: 1px solid #303030;
  font-family: var(--font-family);
}
._root_ypbb5_53[data-draft-active=true] {
  border-color: #ff6c02;
  background-color: rgba(255, 108, 2, 0.15);
}
._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {
  border-color: #ff8b35;
}

._draft_ypbb5_67 {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 10px;
  cursor: pointer;
  color: #646464;
  border-left: 1px solid #303030;
  border-radius: 0 5px 5px 0;
  margin: -1px;
}
._draft_ypbb5_67:disabled:hover {
  cursor: not-allowed;
}
._draft_ypbb5_67[data-active=true] {
  border-color: #ff6c02;
}
._draft_ypbb5_67[data-active=true]:enabled:hover {
  border-color: #ff8b35;
  background-color: #ff8b35;
}
._draft_ypbb5_67[data-active=false] {
  border: 1px solid #303030;
}
._draft_ypbb5_67[data-active=false]:enabled:hover {
  background-color: #0c0c0c;
}
._draft_ypbb5_67:focus-visible {
  outline: 1px solid;
  outline-offset: -1px;
  outline-color: #303030;
  border-radius: 0 6px 6px 0;
}
._draft_ypbb5_67[data-active=true] {
  color: #f3f3f3;
  background-color: #ff6c02;
}
._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {
  transition: color 0.2s, background-color 0.2s;
}
._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {
  color: #fff;
}
._draft_ypbb5_67[data-loading=true] {
  cursor: wait !important;
}
._draft_ypbb5_67[data-loading=true] svg {
  animation: _breathe_ypbb5_1 1s infinite;
}

._tooltipWrapper_ypbb5_122 {
  position: relative;
  display: flex;
  height: 100%;
}
._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {
  visibility: visible;
}

._dragHandle_ypbb5_131 {
  all: unset;
  cursor: grab;
}
._dragHandle_ypbb5_131._dragging_ypbb5_135 {
  cursor: grabbing;
}
._dragHandle_ypbb5_131:active {
  cursor: grabbing;
}

._tooltip_ypbb5_122 {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%) translateY(0);
  background-color: #0c0c0c;
  border: 1px solid #303030;
  color: white;
  border-radius: 4px;
  max-width: 250px;
  width: max-content;
  font-size: 14px;
  z-index: 1000;
  visibility: hidden;
  --translate-x: -50%;
}
._tooltip_ypbb5_122._forceVisible_ypbb5_158 {
  visibility: visible;
}
._tooltip_ypbb5_122._top_ypbb5_161 {
  top: 40px;
  bottom: unset;
  transform: translateY(0) translateX(var(--translate-x));
}
._tooltip_ypbb5_122._top_ypbb5_161:before {
  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);
  top: -4.5px;
  bottom: unset;
  transform: translateX(var(--translate-x)) rotate(45deg);
}
._tooltip_ypbb5_122._bottom_ypbb5_172 {
  bottom: unset;
  top: -40px;
  transform: translateY(0) translateX(var(--translate-x));
}
._tooltip_ypbb5_122._bottom_ypbb5_172:before {
  bottom: -4.5px;
  top: unset;
  transform: translateX(0) rotate(45deg);
}
._tooltip_ypbb5_122._right_ypbb5_182 {
  right: 0;
  left: unset;
  transform: translateX(0);
  --translate-x: 0;
}
._tooltip_ypbb5_122._right_ypbb5_182:before {
  right: 8px;
  left: unset;
  transform: translateX(--translate-x) rotate(45deg);
}
._tooltip_ypbb5_122._left_ypbb5_193 {
  left: 50%;
  right: unset;
  transform: translateX(-50%);
  --translate-x: -50%;
}
._tooltip_ypbb5_122._left_ypbb5_193:before {
  left: 50%;
  right: unset;
  transform: translateX(-50%) rotate(45deg);
}
._tooltip_ypbb5_122:before {
  z-index: -1;
  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);
  content: "";
  position: absolute;
  bottom: -4.5px;
  left: 50%;
  width: 20px;
  height: 20px;
  background-color: #0c0c0c;
  transform: rotate(45deg) translateX(-50%);
  border-radius: 2px;
  border: 1px solid #303030;
}

._branchSelect_ypbb5_219 {
  height: 100%;
  background: none;
  border: none;
  font-weight: 500;
  font-size: 16px;
  padding-right: 8px;
  padding-bottom: 0px;
  padding-top: 0px;
  margin-bottom: 2px;
  min-width: 80px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  outline: none;
  color: inherit;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 1;
  font-family: var(--font-family);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

._branchSelectIcon_ypbb5_245 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  pointer-events: none;
}

@keyframes _in_ypbb5_1 {
  0% {
    opacity: 0;
    transform: translateY(4px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes _breathe_ypbb5_1 {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.45;
  }
  100% {
    opacity: 1;
  }
}`;
(function() {
    if (typeof document !== "undefined" && !document.getElementById(digest)) {
        var ele = document.createElement("style");
        ele.id = digest;
        ele.textContent = css;
        document.head.appendChild(ele);
    }
})();
var toolbar_module_default = classes;
// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/tooltip.tsx
var import_lodash = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$chunk$2d$YSQDPG26$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__toESM"])(require_lodash(), 1);
;
var Tooltip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_s(({ children, content, forceVisible }, ref)=>{
    _s();
    const tooltipContentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const checkOverflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((0, import_lodash.default)({
        "Tooltip.useCallback[checkOverflow]": ()=>{
            if (tooltipContentRef.current) {
                const rect = tooltipContentRef.current.getBoundingClientRect();
                const paddingInline = tooltipContentRef.current.classList.contains(toolbar_module_default.left) ? 0 : rect.width / 2;
                const paddingBlock = rect.height;
                const tooltipOffset = 40 * 2;
                const isAlreadyToTop = tooltipContentRef.current.classList.contains(toolbar_module_default.bottom);
                if ((isAlreadyToTop ? rect.top : rect.top - tooltipOffset - paddingBlock) <= 0) {
                    tooltipContentRef.current.classList.remove(toolbar_module_default.bottom);
                    tooltipContentRef.current.classList.add(toolbar_module_default.top);
                } else {
                    tooltipContentRef.current.classList.remove(toolbar_module_default.top);
                    tooltipContentRef.current.classList.add(toolbar_module_default.bottom);
                }
                if (rect.right + paddingInline > window.innerWidth) {
                    tooltipContentRef.current.classList.remove(toolbar_module_default.left);
                    tooltipContentRef.current.classList.add(toolbar_module_default.right);
                } else {
                    tooltipContentRef.current.classList.remove(toolbar_module_default.right);
                    tooltipContentRef.current.classList.add(toolbar_module_default.left);
                }
            }
        }
    }["Tooltip.useCallback[checkOverflow]"], 100), []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Tooltip.useEffect": ()=>{
            checkOverflow();
            window.addEventListener("resize", checkOverflow);
            return ({
                "Tooltip.useEffect": ()=>{
                    window.removeEventListener("resize", checkOverflow);
                }
            })["Tooltip.useEffect"];
        }
    }["Tooltip.useEffect"], [
        checkOverflow
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Tooltip.useImperativeHandle": ()=>({
                checkOverflow
            })
    }["Tooltip.useImperativeHandle"], [
        checkOverflow
    ]);
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: toolbar_module_default.tooltipWrapper
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("p", {
        ref: tooltipContentRef,
        style: {
            padding: "3px 8px"
        },
        className: forceVisible ? `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left} ${toolbar_module_default.forceVisible}` : `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left}`
    }, content), children);
}, "NhslALngfU01jgMRtQNQrjIS1cw="));
_c = Tooltip;
;
var DragHandle = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_s1(({ onDrag, children }, ref)=>{
    _s1();
    const [isDragging, setIsDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const initialPointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    const initialToolbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    const hasDragged = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "DragHandle.useImperativeHandle": ()=>({
                hasDragged: hasDragged.current
            })
    }["DragHandle.useImperativeHandle"]);
    const handleDrag = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DragHandle.useCallback[handleDrag]": (e)=>{
            if (!isDragging) return;
            const deltaX = e.clientX - initialPointer.current.x;
            const deltaY = e.clientY - initialPointer.current.y;
            const newToolbarX = initialToolbar.current.x + deltaX;
            const newToolbarY = initialToolbar.current.y + deltaY;
            if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {
                hasDragged.current = true;
            }
            onDrag({
                x: newToolbarX,
                y: newToolbarY
            });
        }
    }["DragHandle.useCallback[handleDrag]"], [
        isDragging,
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "DragHandle.useLayoutEffect": ()=>{
            if (!isDragging) return;
            window.addEventListener("pointermove", handleDrag);
            return ({
                "DragHandle.useLayoutEffect": ()=>{
                    window.removeEventListener("pointermove", handleDrag);
                }
            })["DragHandle.useLayoutEffect"];
        }
    }["DragHandle.useLayoutEffect"], [
        isDragging,
        onDrag,
        handleDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "DragHandle.useLayoutEffect": ()=>{
            if (!isDragging) {
                hasDragged.current = false;
                return;
            }
            const handlePointerUp = {
                "DragHandle.useLayoutEffect.handlePointerUp": ()=>{
                    setIsDragging(false);
                }
            }["DragHandle.useLayoutEffect.handlePointerUp"];
            window.addEventListener("pointerup", handlePointerUp);
            return ({
                "DragHandle.useLayoutEffect": ()=>{
                    window.removeEventListener("pointerup", handlePointerUp);
                }
            })["DragHandle.useLayoutEffect"];
        }
    }["DragHandle.useLayoutEffect"], [
        isDragging
    ]);
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        draggable: true,
        className: `${toolbar_module_default.dragHandle} ${isDragging ? toolbar_module_default.dragging : ""}`,
        onPointerDown: (e)=>{
            if (e.target instanceof HTMLElement && (e.target.nodeName.toLowerCase() === "select" || e.target.closest("select"))) {
                return;
            }
            const handle = e.currentTarget;
            if (!handle) return;
            e.stopPropagation();
            e.preventDefault();
            initialPointer.current = {
                x: e.clientX,
                y: e.clientY
            };
            const rect = handle.getBoundingClientRect();
            initialToolbar.current.x = rect.left;
            initialToolbar.current.y = rect.top;
            setIsDragging(true);
        },
        onPointerUp: ()=>{
            setIsDragging(false);
        }
    }, children);
}, "8Zo+PEPaiEwIr9zU6k1GmyrHSu8="));
_c1 = DragHandle;
;
var BranchSwitcher = ({ isForcedDraft, draft, apiRref, latestBranches, onRefChange, getAndSetLatestBranches })=>{
    _s2();
    const shadowRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const selectRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const sortedLatestBranches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "BranchSwitcher.useMemo[sortedLatestBranches]": ()=>{
            return [
                ...latestBranches
            ].sort({
                "BranchSwitcher.useMemo[sortedLatestBranches]": (a, b)=>{
                    if (a.isDefault) return -1;
                    if (b.isDefault) return 1;
                    return a.name.localeCompare(b.name);
                }
            }["BranchSwitcher.useMemo[sortedLatestBranches]"]);
        }
    }["BranchSwitcher.useMemo[sortedLatestBranches]"], [
        latestBranches
    ]);
    const refOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "BranchSwitcher.useMemo[refOptions]": ()=>{
            const options = new Set(sortedLatestBranches.map({
                "BranchSwitcher.useMemo[refOptions]": (branch)=>branch.name
            }["BranchSwitcher.useMemo[refOptions]"]));
            options.add(apiRref);
            return Array.from(options);
        }
    }["BranchSwitcher.useMemo[refOptions]"], [
        sortedLatestBranches,
        apiRref
    ]);
    const [refetchLatestBranches, setRefetchLatestBranches] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BranchSwitcher.useEffect": ()=>{
            if (refetchLatestBranches) {
                getAndSetLatestBranches().then({
                    "BranchSwitcher.useEffect": ()=>{
                        setRefetchLatestBranches(false);
                    }
                }["BranchSwitcher.useEffect"]);
            }
        }
    }["BranchSwitcher.useEffect"], [
        refetchLatestBranches,
        getAndSetLatestBranches
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BranchSwitcher.useEffect": ()=>{
            const shadow = shadowRef.current;
            const select = selectRef.current;
            if (!shadow || !select) return;
            const updateSelectWidth = {
                "BranchSwitcher.useEffect.updateSelectWidth": ()=>{
                    const width = shadow.offsetWidth;
                    select.style.width = `${width + 20}px`;
                }
            }["BranchSwitcher.useEffect.updateSelectWidth"];
            updateSelectWidth();
            window.addEventListener("resize", updateSelectWidth);
            return ({
                "BranchSwitcher.useEffect": ()=>{
                    window.removeEventListener("resize", updateSelectWidth);
                    if (select) {
                        select.style.removeProperty("width");
                    }
                }
            })["BranchSwitcher.useEffect"];
        }
    }["BranchSwitcher.useEffect"], [
        apiRref
    ]);
    const isDraftActive = isForcedDraft || draft;
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: toolbar_module_default.branch,
        "data-draft-active": isDraftActive,
        onMouseEnter: ()=>{
            setRefetchLatestBranches(true);
        }
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(BranchIcon, null), "\xA0", /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Tooltip, {
        content: !isDraftActive ? "Switch branch and enter draft mode" : "Switch branch"
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("select", {
        ref: selectRef,
        value: apiRref,
        onChange: (e)=>onRefChange(e.target.value, {
                enableDraftMode: !isDraftActive
            }),
        className: toolbar_module_default.branchSelect,
        onMouseDown: (e)=>{
            e.stopPropagation();
        },
        onClick: (e)=>{
            e.stopPropagation();
            setRefetchLatestBranches(true);
        }
    }, refOptions.map((r)=>{
        return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("option", {
            key: r,
            value: r
        }, r);
    })), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        width: "15",
        height: "15",
        viewBox: "0 0 15 15",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        className: toolbar_module_default.branchSelectIcon
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",
        fill: "currentColor",
        fillRule: "evenodd",
        clipRule: "evenodd"
    }))), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: toolbar_module_default.branchSelect,
        style: {
            visibility: "hidden",
            opacity: 0,
            pointerEvents: "none",
            position: "absolute",
            top: 0,
            left: 0
        },
        "aria-hidden": "true",
        ref: shadowRef
    }, apiRref));
};
_s2(BranchSwitcher, "crIHnSJNjHvIuJkzUEG0iBbZ5PI=");
_c2 = BranchSwitcher;
var BranchIcon = ()=>{
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "18",
        height: "18",
        fill: "none"
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fill: "#F3F3F3",
        fillRule: "evenodd",
        d: "M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z",
        clipRule: "evenodd"
    }), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fill: "#F3F3F3",
        fillRule: "evenodd",
        d: "M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z",
        clipRule: "evenodd"
    }), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fill: "#F3F3F3",
        fillRule: "evenodd",
        d: "M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z",
        clipRule: "evenodd"
    }), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fill: "#F3F3F3",
        fillRule: "evenodd",
        d: "M5.767 9.022V5.374H4.702v3.648h1.065Z",
        clipRule: "evenodd"
    }));
};
_c3 = BranchIcon;
// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx
var import_lodash2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$chunk$2d$YSQDPG26$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__toESM"])(require_lodash(), 1);
;
var TOOLBAR_POSITION_STORAGE_KEY = "bshb_toolbar_pos";
var ClientToolbar = ({ draft, isForcedDraft, enableDraftMode, disableDraftMode, bshbPreviewToken, shouldAutoEnableDraft, seekAndStoreBshbPreviewToken, resolvedRef, getLatestBranches })=>{
    _s3();
    const [toolbarRef, setToolbarRef] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const dragHandleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const tooltipRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [message, setMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [previewRef, _setPreviewRef] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(resolvedRef.ref);
    const [isDefaultRefSelected, setIsDefaultRefSelected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isLoadingRef, setIsLoadingRef] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [latestBranches, setLatestBranches] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const currentMessageTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const displayMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[displayMessage]": (message2)=>{
            window.clearTimeout(currentMessageTimeout.current);
            setMessage(message2);
            currentMessageTimeout.current = window.setTimeout({
                "ClientToolbar.useCallback[displayMessage]": ()=>setMessage("")
            }["ClientToolbar.useCallback[displayMessage]"], 5e3);
        }
    }["ClientToolbar.useCallback[displayMessage]"], [
        setMessage
    ]);
    const triggerDraftMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[triggerDraftMode]": (previewToken)=>{
            setLoading(true);
            enableDraftMode({
                bshbPreviewToken: previewToken
            }).then({
                "ClientToolbar.useCallback[triggerDraftMode]": ({ status, response })=>{
                    if (status === 200) {
                        setLatestBranches({
                            "ClientToolbar.useCallback[triggerDraftMode]": (p)=>response.latestBranches ?? p
                        }["ClientToolbar.useCallback[triggerDraftMode]"]);
                        window.location.reload();
                    } else if ("error" in response) {
                        displayMessage(`Draft mode activation error: ${response.error}`);
                    } else {
                        displayMessage("Draft mode activation error");
                    }
                }
            }["ClientToolbar.useCallback[triggerDraftMode]"]).finally({
                "ClientToolbar.useCallback[triggerDraftMode]": ()=>setLoading(false)
            }["ClientToolbar.useCallback[triggerDraftMode]"]);
        }
    }["ClientToolbar.useCallback[triggerDraftMode]"], [
        enableDraftMode,
        displayMessage
    ]);
    const bshbPreviewRefCookieName = `bshb-preview-ref-${resolvedRef.repoHash}`;
    const previewRefCookieManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ClientToolbar.useMemo[previewRefCookieManager]": ()=>({
                set: ({
                    "ClientToolbar.useMemo[previewRefCookieManager]": (ref)=>{
                        document.cookie = `${bshbPreviewRefCookieName}=${ref}; path=/; Max-Age=${60 * 60 * 24 * 30 * 365}`;
                    }
                })["ClientToolbar.useMemo[previewRefCookieManager]"],
                clear: ({
                    "ClientToolbar.useMemo[previewRefCookieManager]": ()=>{
                        document.cookie = `${bshbPreviewRefCookieName}=; path=/; Max-Age=-1`;
                    }
                })["ClientToolbar.useMemo[previewRefCookieManager]"],
                get: ({
                    "ClientToolbar.useMemo[previewRefCookieManager]": ()=>{
                        return document.cookie.split("; ").find({
                            "ClientToolbar.useMemo[previewRefCookieManager]": (row)=>row.startsWith(bshbPreviewRefCookieName)
                        }["ClientToolbar.useMemo[previewRefCookieManager]"])?.split("=")[1] ?? null;
                    }
                })["ClientToolbar.useMemo[previewRefCookieManager]"]
            })
    }["ClientToolbar.useMemo[previewRefCookieManager]"], [
        bshbPreviewRefCookieName
    ]);
    const [hasAutoEnabledDraftOnce, setHasAutoEnabledDraftOnce] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "ClientToolbar.useLayoutEffect": ()=>{
            if (draft || hasAutoEnabledDraftOnce || !shouldAutoEnableDraft || isForcedDraft || !bshbPreviewToken) {
                return;
            }
            triggerDraftMode(bshbPreviewToken);
            setHasAutoEnabledDraftOnce(true);
        }
    }["ClientToolbar.useLayoutEffect"], [
        isForcedDraft,
        enableDraftMode,
        seekAndStoreBshbPreviewToken,
        bshbPreviewToken,
        displayMessage,
        triggerDraftMode,
        draft,
        shouldAutoEnableDraft,
        hasAutoEnabledDraftOnce
    ]);
    const getAndSetLatestBranches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[getAndSetLatestBranches]": async ()=>{
            let result = [];
            const res = await getLatestBranches({
                bshbPreviewToken
            });
            if (!res) return;
            if (Array.isArray(res.response)) {
                result = res.response;
            } else if ("error" in res.response) {
                console.error(`BaseHub Toolbar Error: ${res.response.error}`);
            }
            setLatestBranches(result);
        }
    }["ClientToolbar.useCallback[getAndSetLatestBranches]"], [
        bshbPreviewToken,
        getLatestBranches
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            async function effect() {
                while(true){
                    try {
                        getAndSetLatestBranches();
                        await new Promise({
                            "ClientToolbar.useEffect.effect": (resolve)=>setTimeout(resolve, 3e4)
                        }["ClientToolbar.useEffect.effect"]);
                    } catch (error) {
                        console.error(`BaseHub Toolbar Error: ${error}`);
                        break;
                    }
                }
            }
            effect();
        }
    }["ClientToolbar.useEffect"], [
        getAndSetLatestBranches
    ]);
    const setRefWithEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[setRefWithEvents]": (ref)=>{
            _setPreviewRef(ref);
            window.__bshb_ref = ref;
            window.dispatchEvent(new CustomEvent("__bshb_ref_changed"));
            previewRefCookieManager.set(ref);
            setIsDefaultRefSelected(ref === resolvedRef.ref);
        }
    }["ClientToolbar.useCallback[setRefWithEvents]"], [
        previewRefCookieManager,
        resolvedRef.ref
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            const url = new URL(window.location.href);
            let previewRef2 = url.searchParams.get("bshb-preview-ref");
            if (!previewRef2) {
                previewRef2 = previewRefCookieManager.get();
            }
            setIsLoadingRef(false);
            if (!previewRef2) return;
            setRefWithEvents(previewRef2);
        }
    }["ClientToolbar.useEffect"], [
        previewRefCookieManager,
        setRefWithEvents,
        resolvedRef.repoHash
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            if (isLoadingRef) return;
            setIsDefaultRefSelected(previewRef === resolvedRef.ref);
        }
    }["ClientToolbar.useEffect"], [
        previewRef,
        resolvedRef.ref,
        isLoadingRef
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            if (isLoadingRef) return;
            if (isDefaultRefSelected) {
                setRefWithEvents(resolvedRef.ref);
                previewRefCookieManager.clear();
                const url = new URL(window.location.href);
                url.searchParams.delete("bshb-preview-ref");
                window.history.replaceState(null, "", url.toString());
            }
        }
    }["ClientToolbar.useEffect"], [
        isDefaultRefSelected,
        isLoadingRef,
        previewRefCookieManager,
        resolvedRef.ref,
        setRefWithEvents
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "ClientToolbar.useLayoutEffect": ()=>{
            tooltipRef.current?.checkOverflow();
        }
    }["ClientToolbar.useLayoutEffect"], [
        message
    ]);
    const getStoredToolbarPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[getStoredToolbarPosition]": ()=>{
            if (!toolbarRef) return;
            if ("object" === "undefined" || !window.sessionStorage) return;
            const toolbarPositionStored = window.sessionStorage.getItem(TOOLBAR_POSITION_STORAGE_KEY);
            if (!toolbarPositionStored) return;
            const toolbarPosition = JSON.parse(toolbarPositionStored);
            if (!("x" in toolbarPosition)) return;
            if (!("y" in toolbarPosition)) return;
            return toolbarPosition;
        }
    }["ClientToolbar.useCallback[getStoredToolbarPosition]"], [
        toolbarRef
    ]);
    const updateToolbarStoredPositionDebounced = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((0, import_lodash2.default)({
        "ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]": (position)=>{
            if ("object" === "undefined" || !window.sessionStorage) return;
            const storedPosition = getStoredToolbarPosition() ?? {
                x: 0,
                y: 0
            };
            window.sessionStorage.setItem(TOOLBAR_POSITION_STORAGE_KEY, JSON.stringify({
                ...storedPosition,
                ...position
            }));
        }
    }["ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]"], 250), []);
    const dragToolbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ClientToolbar.useCallback[dragToolbar]": (position)=>{
            const toolbar = toolbarRef;
            if (!toolbar) return;
            const rect = toolbar.getBoundingClientRect();
            const padding = 32;
            const newPositionForStore = {};
            if (position.x - padding < 0) {
                toolbar.style.left = `${padding}px`;
                toolbar.style.right = "unset";
                newPositionForStore.x = padding;
            } else if (position.x + rect.width + padding > window.innerWidth) {
                toolbar.style.right = `${padding}px`;
                toolbar.style.left = "unset";
                newPositionForStore.x = padding;
            } else {
                toolbar.style.right = "unset";
                toolbar.style.left = `${position.x}px`;
                newPositionForStore.x = position.x;
            }
            if (position.y - padding < 0) {
                toolbar.style.bottom = "unset";
                toolbar.style.top = `${padding}px`;
                newPositionForStore.y = padding;
            } else if (position.y + rect.height + padding > window.innerHeight) {
                toolbar.style.top = "unset";
                toolbar.style.bottom = `${padding}px`;
                newPositionForStore.y = padding;
            } else {
                toolbar.style.bottom = "unset";
                toolbar.style.top = `${position.y}px`;
                newPositionForStore.x = position.y;
            }
            updateToolbarStoredPositionDebounced({
                x: position.x,
                y: position.y
            });
        }
    }["ClientToolbar.useCallback[dragToolbar]"], [
        toolbarRef,
        updateToolbarStoredPositionDebounced
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const repositionToolbar = {
                "ClientToolbar.useEffect.repositionToolbar": ()=>{
                    const pos = getStoredToolbarPosition();
                    if (!pos) return;
                    dragToolbar(pos);
                    tooltipRef.current?.checkOverflow();
                }
            }["ClientToolbar.useEffect.repositionToolbar"];
            repositionToolbar();
            window.addEventListener("resize", repositionToolbar);
            return ({
                "ClientToolbar.useEffect": ()=>{
                    window.removeEventListener("resize", repositionToolbar);
                }
            })["ClientToolbar.useEffect"];
        }
    }["ClientToolbar.useEffect"], [
        getStoredToolbarPosition,
        dragToolbar
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientToolbar.useEffect": ()=>{
            if (!latestBranches) return;
            const fromCookie = previewRefCookieManager.get();
            if (!fromCookie) return;
            if (!latestBranches.find({
                "ClientToolbar.useEffect": (branch)=>branch.name === fromCookie
            }["ClientToolbar.useEffect"])) {
                previewRefCookieManager.clear();
            }
        }
    }["ClientToolbar.useEffect"], [
        latestBranches,
        previewRefCookieManager
    ]);
    const tooltip = isForcedDraft ? "Draft enforced by dev env" : `${draft ? "Disable" : "Enable"} draft mode`;
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: toolbar_module_default.wrapper,
        ref: setToolbarRef
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(DragHandle, {
        ref: dragHandleRef,
        onDrag: (pos)=>{
            dragToolbar(pos);
            tooltipRef.current?.checkOverflow();
        }
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: toolbar_module_default.root,
        "data-draft-active": isForcedDraft || draft
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(BranchSwitcher, {
        isForcedDraft,
        draft,
        apiRref: previewRef,
        latestBranches,
        onRefChange: (newRef, opts)=>{
            const url = new URL(window.location.href);
            url.searchParams.set("bshb-preview-ref", newRef);
            window.history.replaceState(null, "", url.toString());
            setRefWithEvents(newRef);
            if (opts.enableDraftMode) {
                const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();
                if (!previewToken) {
                    return displayMessage("Preview token not found");
                }
                triggerDraftMode(previewToken);
            }
        },
        getAndSetLatestBranches
    }), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(AutoAddRefToUrlOnPathChangeIfRefIsNotDefault, {
        previewRef,
        resolvedRef,
        isDraftModeEnabled: isForcedDraft || draft
    }), /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Tooltip, {
        content: message || tooltip,
        ref: tooltipRef,
        forceVisible: Boolean(message)
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("button", {
        className: toolbar_module_default.draft,
        "data-active": isForcedDraft || draft,
        "aria-label": `${draft ? "Disable" : "Enable"} draft mode`,
        "data-loading": loading,
        disabled: isForcedDraft || loading,
        onClick: ()=>{
            if (loading || dragHandleRef.current?.hasDragged) return;
            if (draft) {
                setLoading(true);
                disableDraftMode().then(()=>{
                    const url = new URL(window.location.href);
                    url.searchParams.delete("bshb-preview");
                    url.searchParams.delete("__vercel_draft");
                    window.location.href = url.toString();
                }).finally(()=>setLoading(false));
            } else {
                const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();
                if (!previewToken) {
                    return displayMessage("Preview token not found");
                }
                triggerDraftMode(previewToken);
            }
        }
    }, draft || isForcedDraft ? /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(EyeIcon, null) : /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(EyeDashedIcon, null))))));
};
_s3(ClientToolbar, "IQ4+EzxYsCgE2Bgb/VOSmx8qxRw=");
_c4 = ClientToolbar;
var AutoAddRefToUrlOnPathChangeIfRefIsNotDefault = ({ previewRef, resolvedRef, isDraftModeEnabled })=>{
    _s4();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const [initialPathname, setInitialPathname] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(pathname);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect": ()=>{
            if (initialPathname) return;
            setInitialPathname(pathname);
        }
    }["AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect"], [
        pathname,
        initialPathname
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect": ()=>{
            if (isDraftModeEnabled) return;
            if (initialPathname === pathname) {
                return;
            }
            if (previewRef !== resolvedRef.ref) {
                const url = new URL(window.location.href);
                url.searchParams.set("bshb-preview-ref", previewRef);
                window.history.replaceState(null, "", url.toString());
            }
        }
    }["AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect"], [
        isDraftModeEnabled,
        previewRef,
        resolvedRef.ref,
        pathname,
        initialPathname
    ]);
    return null;
};
_s4(AutoAddRefToUrlOnPathChangeIfRefIsNotDefault, "6OlaD9VqixIv4hI8ylvxpPZQWYs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c5 = AutoAddRefToUrlOnPathChangeIfRefIsNotDefault;
var EyeDashedIcon = ()=>{
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        "data-testid": "geist-icon",
        height: "16",
        strokeLinejoin: "round",
        viewBox: "0 0 16 16",
        width: "16",
        style: {
            color: "currentcolor"
        }
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",
        fill: "currentColor"
    }));
};
_c6 = EyeDashedIcon;
var EyeIcon = ()=>{
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        "data-testid": "geist-icon",
        height: "16",
        strokeLinejoin: "round",
        viewBox: "0 0 16 16",
        width: "16",
        style: {
            color: "currentcolor"
        }
    }, /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",
        fill: "currentColor"
    }));
};
_c7 = EyeIcon;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "Tooltip");
__turbopack_context__.k.register(_c1, "DragHandle");
__turbopack_context__.k.register(_c2, "BranchSwitcher");
__turbopack_context__.k.register(_c3, "BranchIcon");
__turbopack_context__.k.register(_c4, "ClientToolbar");
__turbopack_context__.k.register(_c5, "AutoAddRefToUrlOnPathChangeIfRefIsNotDefault");
__turbopack_context__.k.register(_c6, "EyeDashedIcon");
__turbopack_context__.k.register(_c7, "EyeIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=packages_cms__basehub_next-toolbar_client-toolbar-CIQDQ5LJ_e87dec91.js.map