// Temporary script to query current legal pages content
import { legal } from './packages/cms/index.js';

async function queryLegalPages() {
  try {
    console.log('Fetching legal pages...');
    const posts = await legal.getPosts();

    console.log('\n=== LEGAL PAGES FOUND ===');
    posts.forEach((post, index) => {
      console.log(`\n${index + 1}. ${post._title}`);
      console.log(`   Slug: ${post._slug}`);
      console.log(`   Description: ${post.description}`);
      console.log(`   Reading Time: ${post.body.readingTime} min`);
      console.log(`   Content Length: ${post.body.plainText.length} characters`);
    });

    // Get detailed content for each page
    for (const post of posts) {
      console.log(`\n\n=== DETAILED CONTENT FOR: ${post._title} ===`);
      console.log(`Slug: ${post._slug}`);
      console.log(`Description: ${post.description}`);
      console.log(`Reading Time: ${post.body.readingTime} min read`);
      console.log('\n--- TABLE OF CONTENTS ---');
      if (post.body.json.toc && post.body.json.toc.length > 0) {
        post.body.json.toc.forEach((item, index) => {
          console.log(`${index + 1}. ${item.text}`);
        });
      } else {
        console.log('No table of contents available');
      }

      console.log('\n--- PLAIN TEXT CONTENT (first 1000 chars) ---');
      console.log(post.body.plainText.substring(0, 1000) + '...');

      console.log('\n--- JSON CONTENT STRUCTURE ---');
      console.log('Content blocks:', post.body.json.content ? post.body.json.content.length : 0);
    }

  } catch (error) {
    console.error('Error fetching legal pages:', error);
  }
}

queryLegalPages();
