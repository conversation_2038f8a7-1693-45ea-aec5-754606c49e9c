"use strict";exports.id=9752,exports.ids=[9752],exports.modules={23055:(e,t,s)=>{s.d(t,{a:()=>h,h:()=>l});var a=s(51472),r=s(70031);let i=(0,a.fi)({token:(0,r.H)().BASEHUB_TOKEN}),n=(0,a.G8)("BlockImage",{url:!0,width:!0,height:!0,alt:!0,blurDataURL:!0}),o=(0,a.G8)("PostsItem",{_slug:!0,_title:!0,authors:{_title:!0,avatar:n,xUrl:!0},categories:{_title:!0},date:!0,description:!0,image:n}),_=(0,a.G8)("PostsItem",{...o,body:{plainText:!0,json:{content:!0,toc:!0},readingTime:!0}}),l={postsQuery:(0,a.G8)("Query",{blog:{posts:{items:o}}}),latestPostQuery:(0,a.G8)("Query",{blog:{posts:{__args:{orderBy:"_sys_createdAt__DESC"},item:_}}}),postQuery:e=>({blog:{posts:{__args:{filter:{_sys_slug:{eq:e}}},item:_}}}),getPosts:async()=>(await i.query(l.postsQuery)).blog.posts.items,getLatestPost:async()=>(await i.query(l.latestPostQuery)).blog.posts.item,getPost:async e=>{let t=l.postQuery(e);return(await i.query(t)).blog.posts.item}},d=(0,a.G8)("LegalPagesItem",{_slug:!0,_title:!0,description:!0}),u=(0,a.G8)("LegalPagesItem",{...d,body:{plainText:!0,json:{content:!0,toc:!0},readingTime:!0}}),h={postsQuery:(0,a.G8)("Query",{legalPages:{items:u}}),latestPostQuery:(0,a.G8)("Query",{legalPages:{__args:{orderBy:"_sys_createdAt__DESC"},item:u}}),postQuery:e=>(0,a.G8)("Query",{legalPages:{__args:{filter:{_sys_slug:{eq:e}}},item:u}}),getPosts:async()=>(await i.query(h.postsQuery)).legalPages.items,getLatestPost:async()=>(await i.query(h.latestPostQuery)).legalPages.item,getPost:async e=>{let t=h.postQuery(e);return(await i.query(t)).legalPages.item}}},29804:(e,t,s)=>{s.d(t,{_:()=>l});var a=s(70031),r=s(71166),i=s(25),n=s(51460),o=s(16248),_=s(82263);let l=(0,r.w)({extends:[(0,a.H)(),(0,r.w)({extends:[(0,o.II)()],server:{ANALYZE:i.z.string().optional(),NEXT_RUNTIME:i.z.enum(["nodejs","edge"]).optional()},client:{NEXT_PUBLIC_APP_URL:i.z.string().url(),NEXT_PUBLIC_WEB_URL:i.z.string().url(),NEXT_PUBLIC_API_URL:i.z.string().url().optional(),NEXT_PUBLIC_DOCS_URL:i.z.string().url().optional()},runtimeEnv:{ANALYZE:process.env.ANALYZE,NEXT_RUNTIME:"nodejs",NEXT_PUBLIC_APP_URL:"http://localhost:3000",NEXT_PUBLIC_WEB_URL:"http://localhost:3001",NEXT_PUBLIC_API_URL:"http://localhost:3002",NEXT_PUBLIC_DOCS_URL:"https://docs.cubent.dev/"}}),(0,r.w)({server:{RESEND_FROM:i.z.string().email(),RESEND_TOKEN:i.z.string().startsWith("re_")},runtimeEnv:{RESEND_FROM:process.env.RESEND_FROM,RESEND_TOKEN:process.env.RESEND_TOKEN}}),(0,_.H)(),(0,n.H)(),(0,r.w)({server:{ARCJET_KEY:i.z.string().startsWith("ajkey_").optional()},runtimeEnv:{ARCJET_KEY:process.env.ARCJET_KEY}}),(0,r.w)({server:{UPSTASH_REDIS_REST_URL:i.z.string().url().optional(),UPSTASH_REDIS_REST_TOKEN:i.z.string().optional()},runtimeEnv:{UPSTASH_REDIS_REST_URL:process.env.UPSTASH_REDIS_REST_URL,UPSTASH_REDIS_REST_TOKEN:process.env.UPSTASH_REDIS_REST_TOKEN}})],server:{},client:{},runtimeEnv:{}})},51460:(e,t,s)=>{s.d(t,{H:()=>i});var a=s(71166),r=s(25);let i=()=>(0,a.w)({server:{FLAGS_SECRET:r.z.string().optional()},runtimeEnv:{FLAGS_SECRET:process.env.FLAGS_SECRET}})},51472:(e,t,s)=>{s.d(t,{G8:()=>p,GJ:()=>b,Y2:()=>E,f0:()=>A,fi:()=>I,xg:()=>P});var a=class extends Error{constructor(e,t){let s=Array.isArray(e)?e.map(e=>e?.message||"").join("\n"):"";s||(s="GraphQL error"),super(s),this.errors=[],this.errors=e,this.data=t}};function r(e,t){let s=t.map(e=>e.request);1===s.length&&(s=s[0]),(()=>{try{return e.fetcher(s)}catch(e){return Promise.reject(e)}})().then(e=>{if(!(1!==t.length||Array.isArray(e)))return e.errors&&e.errors.length?void t[0].reject(new a(e.errors,e.data)):void t[0].resolve(e);if(e.length!==t.length)throw Error("response length did not match query length");for(let s=0;s<t.length;s++)e[s].errors&&e[s].errors.length?t[s].reject(new a(e[s].errors,e[s].data)):t[s].resolve(e[s])}).catch(e=>{for(let s=0;s<t.length;s++)t[s].reject(e)})}function i(e,t){let s=e._queue,a=t.maxBatchSize||0;if(e._queue=[],a>0&&a<s.length)for(let t=0;t<s.length/a;t++)r(e,s.slice(t*a,(t+1)*a));else r(e,s)}var n=class e{constructor(e,{batchInterval:t=16,shouldBatch:s=!0,maxBatchSize:a=0}={}){this.fetcher=e,this._options={batchInterval:t,shouldBatch:s,maxBatchSize:a},this._queue=[]}fetch(e,t,s,a={}){let r={query:e},n=Object.assign({},this._options,a);return t&&(r.variables=t),s&&(r.operationName=s),new Promise((e,t)=>{this._queue.push({request:r,resolve:e,reject:t}),1===this._queue.length&&(n.shouldBatch?setTimeout(()=>i(this,n),n.batchInterval):i(this,n))})}forceFetch(t,s,a,r={}){let n={query:t},o=Object.assign({},this._options,r,{shouldBatch:!1});return s&&(n.variables=s),a&&(n.operationName=a),new Promise((t,s)=>{let a=new e(this.fetcher,this._options);a._queue=[{request:n,resolve:t,reject:s}],i(a,o)})}},o={maxBatchSize:10,batchInterval:40},_=({url:e,headers:t={},fetcher:s,fetch:r,batch:i=!1,..._})=>{if(!e&&!s)throw Error("url or fetcher is required");if(s=s||(async(s,a)=>{let i="function"==typeof t?await t():t;if(i=i||{},"undefined"==typeof fetch&&!r)throw Error("Global `fetch` function is not available, pass a fetch polyfill to Genql `createClient`");let n=r||fetch;a?.headers&&(i={...i,...a.headers},delete a.headers);let o=await n(e,{headers:{"Content-Type":"application/json",...i},method:"POST",body:JSON.stringify(s),..._,...a});if(!o.ok)throw Error(`${o.statusText}: ${await o.text()}`);return await o.json()}),!i)return async(e,t)=>{let r=await s(e,t);if(Array.isArray(r))return r.map(e=>{if(e?.errors?.length)throw new a(e.errors||[],e.data);return e.data});if(r?.errors?.length)throw new a(r.errors||[],r.data);return r.data};let l=new n(async(e,t)=>await s(e,t),!0===i?o:i);return async({query:e,variables:t})=>{let s=await l.fetch(e,t);if(s?.data)return s.data;throw Error("Genql batch fetcher returned unexpected result "+JSON.stringify(s))}},l="__alias__";function d(e){if("object"!=typeof e||null===e)return e;if(Array.isArray(e))return e.map(e=>d(e));let t={};for(let[s,a]of Object.entries(e))if(s.includes(l)){let[e,...r]=s.split(l);t[r.join(l)]=d(a)}else t[s]=d(a);return t}var u=(e,t,s,a)=>{if("object"==typeof e&&"__args"in e){let r=e.__args,i={...e};delete i.__args;let n=Object.keys(r);if(0===n.length)return u(i,t,s,a);let o=c(t.root,s),_=n.map(e=>{t.varCounter++;let a=`v${t.varCounter}`,i=o.args&&o.args[e];if(!i)throw Error(`no typing defined for argument \`${e}\` in path \`${s.join(".")}\``);let n=["String","String!"].includes(i[1]),_=r[e];return n&&"object"==typeof _&&(_=JSON.stringify(_)),t.variables[a]={value:_,typing:i},`${e}:$${a}`});return`(${_})${u(i,t,s,a)}`}if("object"!=typeof e||!(Object.keys(e).length>0))return"";{let r,i=Object.keys(e).filter(t=>!!e[t]),n=s.length>0?c(t.root,s).type:t.root,o=n.scalar,_=i.filter(e=>!!(["__scalar","__name","__fragmentOn"].includes(e)||e.startsWith("on_"))||n.fields&&e in n.fields);if(0===_.length)return"";if(i.includes("__scalar")){let s=new Set(Object.keys(e).filter(t=>!e[t]));o?.length&&(t.fragmentCounter++,r=`f${t.fragmentCounter}`,t.fragments.push(`fragment ${r} on ${n.name}{${o.filter(e=>!s.has(e)).map(e=>`${a?.aliasPrefix?`${a.aliasPrefix}${l}${e}: `:""}${e}`).join(",")}}`))}let d=_.filter(e=>!["__scalar","__name","__fragmentOn"].includes(e)).map(r=>{if(r.startsWith("on_")){t.fragmentCounter++;let i=`f${t.fragmentCounter}`,n=u(e[r],t,[...s,r],{...a,aliasPrefix:i}),o=r.match(/^on_(.+)/);if(!o||!o[1])throw Error("match failed");return t.fragments.push(`fragment ${i} on ${o[1]}${n}`),`...${i}`}{let i=n.fields?.[r];if(!i)return"";if(!i.type.fields)return`${a?.aliasPrefix?`${a.aliasPrefix}${l}${r}: `:""}${r}`;let o=u(e[r],t,[...s,r],a);if(!o&&i.type.fields){let e=i.type.scalar?.[0];if(e)return`${a?.aliasPrefix?`${a.aliasPrefix}${l}${r}: `:""}${r}{${e}}`}return`${a?.aliasPrefix?`${a.aliasPrefix}${l}${r}: `:""}${r}${o}`}}).filter(Boolean).concat(r?[`...${r}`]:[]).join(",");return d?`{${d}}`:""}},h=(e,t,s)=>{let a={root:t,varCounter:0,variables:{},fragmentCounter:0,fragments:[]},r=u(s,a,[]),i=Object.keys(a.variables),n=i.length>0?`(${i.map(e=>{let t=a.variables[e].typing[1];return`$${e}:${t}`})})`:"",o=s?.__name||"";return{query:[`${e} ${o}${n}${r}`,...a.fragments].join(","),variables:Object.keys(a.variables).reduce((e,t)=>(e[t]=a.variables[t].value,e),{}),...o?{operationName:o.toString()}:{}}},c=(e,t)=>{let s;if(!e)throw Error("root type is not provided");if(0===t.length)throw Error("path is empty");return t.forEach(t=>{let a=s?s.type:e;if(!a.fields)throw Error(`type \`${a.name}\` does not have fields`);let r=Object.keys(a.fields).filter(e=>e.startsWith("on_")).reduce((e,t)=>{let s=a.fields&&a.fields[t];return s&&e.push(s.type),e},[a]),i=null;if(r.forEach(e=>{let s=e.fields&&e.fields[t];s&&(i=s)}),!i)throw Error(`type \`${a.name}\` does not have a field \`${t}\``);s=i}),s},m=({queryRoot:e,mutationRoot:t,subscriptionRoot:s,getExtraFetchOptions:a,...r})=>{let i=_(r),n={};return e&&(n.query=async t=>{if(!e)throw Error("queryRoot argument is missing");let s=h("query",e,t),r=await a?.("query",s,t);return await i(s,r).then(e=>d(e))}),t&&(n.mutation=async e=>{if(!t)throw Error("mutationRoot argument is missing");let s=h("mutation",t,e),r=await a?.("mutation",s,e);return await i(h("mutation",t,e),r)}),n};m.replaceSystemAliases=d;var y=e=>(Object.keys(e).forEach(t=>{let s=e[t];if(!s.fields)return;let a=s.fields;Object.keys(a).forEach(t=>{let s=a[t];if(s.args){let t=s.args;Object.keys(t).forEach(s=>{let a=t[s];if(a){let[t]=a;"string"==typeof t&&(e[t]||(e[t]={name:t}),a[0]=e[t])}})}let r=s.type;"string"==typeof r&&(e[r]||(e[r]={name:r}),s.type=e[r])})}),e);function p(e,t){return{__fragmentOn:e,...t}}var g=(e=>{let t=Object.assign({},...Object.keys(e.types).map((e,t)=>({[t]:e})));return y(Object.assign({},...Object.keys(e.types||{}).map(s=>{let a=e.types[s]||{};return{[s]:{name:s,scalar:Object.keys(a).filter(t=>{let[s]=a[t]||[];return!(!(s&&e.scalars.includes(s))||Object.values(a[t]?.[1]||{}).map(e=>e?.[1]).filter(Boolean).some(e=>e&&e.endsWith("!")))}),fields:Object.assign({},...Object.keys(a).map(e=>{let[s,r]=a[e]||[];return null==s?{}:{[e]:{type:t[s],args:Object.assign({},...Object.keys(r||{}).map(e=>{if(!r||!r[e])return;let[s,a]=r[e];return{[e]:[t[s],a||t[s]]}}))}}}))}}})))})({scalars:[0,4,5,6,7,25,29,30,32,33,35,36,37,41,53,58,63,71,72],types:{AnalyticsKeyScope:{},Authors: <AUTHORS>