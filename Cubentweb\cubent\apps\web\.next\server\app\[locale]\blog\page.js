const CHUNK_PUBLIC_PATH = "server/app/[locale]/blog/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_002c68e8._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_6053f640._.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_client_components_forbidden-error_e549a34e.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_client_components_unauthorized-error_7dab06d3.js");
runtime.loadChunk("server/chunks/ssr/apps_web_app_[locale]_69c544cb._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_app_[locale]_7e86570f._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_app_[locale]_9a716489._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__a34c8529._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__a59ed7dc._.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_a9d7e4c6._.js");
runtime.loadChunk("server/chunks/ssr/ec4b9_zod_dist_esm_e54d6085._.js");
runtime.loadChunk("server/chunks/ssr/9583c_@sentry_core_build_cjs_102f8899._.js");
runtime.loadChunk("server/chunks/ssr/d96dc_@sentry_node_build_cjs_f09a6c16._.js");
runtime.loadChunk("server/chunks/ssr/d486d_@opentelemetry_core_build_esm_51b95a45._.js");
runtime.loadChunk("server/chunks/ssr/ffc21_@opentelemetry_semantic-conventions_build_esm_51285830._.js");
runtime.loadChunk("server/chunks/ssr/bfb6c_@opentelemetry_semantic-conventions_build_esm_788bf6fe._.js");
runtime.loadChunk("server/chunks/ssr/8b446_@opentelemetry_sdk-trace-base_build_esm_b1948024._.js");
runtime.loadChunk("server/chunks/ssr/46f7a_@opentelemetry_resources_build_esm_a0078a80._.js");
runtime.loadChunk("server/chunks/ssr/b52d1_@sentry_nextjs_build_cjs_39264c91._.js");
runtime.loadChunk("server/chunks/ssr/78375_tailwind-merge_dist_bundle-mjs_mjs_a887465a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_18c5e77e._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_app_[locale]_global-error_tsx_3a5fc62b._.js");
runtime.loadChunk("server/chunks/ssr/25c57_@clerk_nextjs_dist_esm_server_a8720f07._.js");
runtime.loadChunk("server/chunks/ssr/_77d0522f._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b10971c0._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/[locale]/blog/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/blog/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/blog/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", METADATA_3 => \"[project]/apps/web/app/[locale]/icon.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_4 => \"[project]/apps/web/app/[locale]/apple-icon.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/apple-icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_5 => \"[project]/apps/web/app/[locale]/opengraph-image.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_6 => \"[project]/apps/web/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/web/app/[locale]/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/apps/web/app/[locale]/blog/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/blog/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", METADATA_3 => \"[project]/apps/web/app/[locale]/icon.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_4 => \"[project]/apps/web/app/[locale]/apple-icon.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/apple-icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_5 => \"[project]/apps/web/app/[locale]/opengraph-image.png.mjs { IMAGE => \\\"[project]/apps/web/app/[locale]/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_6 => \"[project]/apps/web/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/web/app/[locale]/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/apps/web/app/[locale]/blog/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
