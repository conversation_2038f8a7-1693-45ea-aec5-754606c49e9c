exports.id=6270,exports.ids=[6270],exports.modules={7820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",sizes:"192x192",url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"apple-icon.png")+"?6b949b4e1bd36892"}]},15190:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",sizes:"32x32",url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"icon.png")+"?0fb4a24cefe3ddc0"}]},15330:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>s});var n=r(6340);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","PostHogProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","useAnalytics")},15813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var n=r(99730),s=r(74938),a=r(83590),o=r(24319),i=r.n(o),l=r(24087),c=r.n(l);let d=(0,a.cn)(c().variable,i().variable,"touch-manipulation font-sans antialiased");var u=r(74562),h=r(35371);let m=({error:e,reset:t})=>((0,h.useEffect)(()=>{(0,u.captureException)(e)},[e]),(0,n.jsx)("html",{lang:"en",className:d,children:(0,n.jsxs)("body",{children:[(0,n.jsx)("h1",{children:"Oops, something went wrong"}),(0,n.jsx)(s.$,{onClick:()=>t(),children:"Try again"})]})}))},17218:(e,t,r)=>{"use strict";r.d(t,{J:()=>d}),Object.getOwnPropertyNames;var n=r(23233),s=r(51472),a=n.lazy(()=>Promise.resolve().then(r.bind(r,40356)).then(e=>({default:e.ClientPump}))),o=new Map,i=null,l=null,c=null,d=async({children:e,queries:t,bind:d,...u})=>{let h,m=[],p=[];if(s.f0){let e=!1;if(void 0===u.draft)try{let{draftMode:t}=await Promise.resolve().then(r.bind(r,62644));e=(await t()).isEnabled}catch(e){}e&&void 0===u.draft&&(u.draft=!0)}let{headers:f,draft:b}=(0,s.GJ)(u),g=f["x-basehub-token"],v=f["x-basehub-api-version"],x="https://aws.basehub.com/pump",w=0===t.length,y=b&&w?[{_sys:{id:!0}}]:t;if(b&&s.f0)try{let{cookies:e}=await Promise.resolve().then(r.bind(r,62644)),t=await e(),n=t.get("bshb-preview-ref-"+s.Y2.repoHash)?.value;n&&(f["x-basehub-ref"]=n)}catch(e){}let j=await Promise.all(y.map(async(e,t)=>{let r,n=(0,s.xg)(e),a=JSON.stringify({...n,...f})+(b?"_draft":"_prod");if(o.has(a)){let e=o.get(a);performance.now()-e.start<32&&(r=await e.data)}if(!r){let d=b?fetch(x,{...s.f0?{cache:"no-store"}:{},method:"POST",headers:{...f,"content-type":"application/json","x-basehub-token":g,"x-basehub-api-version":v},body:JSON.stringify(n)}).then(async e=>{let{data:r=null,newPumpToken:n,errors:a=null,spaceID:o,pusherData:d,responseHash:u}=await e.json();return i=n,c=d,l=o,m.push(a),p[t]=u,s.fi.replaceSystemAliases(r)}):(0,s.fi)(u).query(e);o.set(a,{start:performance.now(),data:d}),r=await d}return{data:r,rawQueryOp:n}}));d&&(e=e.bind(null,d));let P=e(j.map(e=>e.data));if(h=P instanceof Promise?await P?.catch(e=>{if(b)return console.error("Error in Pump children function",e),null;throw e}):P,b){if(!i||!l||!c)throw console.log("Results (length):",j?.length),console.log("Errors:",JSON.stringify(m,null,2)),console.log("Pump Endpoint:",x),console.log("Pump Token:",i),console.log("Space ID:",l),console.log("Pusher Data:",c),console.log("Response Hashes:",JSON.stringify(p,null,2)),Error("Pump did not return the necessary data. Look at the logs to see what's missing.");return n.createElement(a,{rawQueries:j.map(e=>e.rawQueryOp),initialState:{data:w?[]:j.map(e=>e.data??null),errors:m,responseHashes:p,pusherData:c,spaceID:l},pumpEndpoint:x,pumpToken:i??void 0,initialResolvedChildren:h,apiVersion:v,previewRef:f["x-basehub-ref"]||s.Y2.ref},e)}return h}},18362:(e,t,r)=>{"use strict";r.d(t,{x:()=>p,w:()=>f});var n=r(94752),s=r(52661);r(37091);var a=r(29804),o=r(23055),i=r(17218),l=r(82263);let c=(0,l.H)().BETTERSTACK_API_KEY,d=(0,l.H)().BETTERSTACK_URL,u=async()=>{if(!c||!d)return null;let e="bg-muted-foreground",t="Unable to fetch status";try{let r=await fetch("https://uptime.betterstack.com/api/v2/monitors",{headers:{Authorization:`Bearer ${c}`}});if(!r.ok)throw Error("Failed to fetch status");let{data:n}=await r.json(),s=n.filter(e=>"up"===e.attributes.status).length/n.length;0===s?(e="bg-destructive",t="Degraded performance"):s<1?(e="bg-warning",t="Partial outage"):(e="bg-success",t="All systems normal")}catch{e="bg-muted-foreground",t="Unable to fetch status"}return(0,n.jsxs)("a",{className:"flex items-center gap-3 font-medium text-sm",target:"_blank",rel:"noreferrer",href:d,children:[(0,n.jsxs)("span",{className:"relative flex h-2 w-2",children:[(0,n.jsx)("span",{className:`absolute inline-flex h-full w-full animate-ping rounded-full opacity-75 ${e}`}),(0,n.jsx)("span",{className:`relative inline-flex h-2 w-2 rounded-full ${e}`})]}),(0,n.jsx)("span",{className:"text-muted-foreground",children:t})]})};var h=r(49499),m=r.n(h);let p=async function([e]){let t=[{title:"Product",items:[{title:"Home",href:"/"},{title:"Pricing",href:"/pricing"},{title:"Features",href:"/features"},{title:"Enterprise",href:"/enterprise"},{title:"Downloads",href:"/downloads"},{title:"Students",href:"/students"}]},{title:"Resources",items:[{title:"Docs",href:a._.NEXT_PUBLIC_DOCS_URL||"/docs"},{title:"Blog",href:"/blog"},{title:"Forum",href:"/forum"},{title:"Changelog",href:"/changelog"}]},{title:"Company",items:[{title:"Anysphere",href:"/company"},{title:"Careers",href:"/careers"},{title:"Community",href:"/community"},{title:"Customers",href:"/customers"}]},{title:"Legal",items:e.legalPages.items.map(e=>({title:e._title,href:`/legal/${e._slug}`}))}];return(0,n.jsxs)("footer",{className:"relative bg-background border-t border-border overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-tr from-orange-500/5 via-background to-orange-600/3 pointer-events-none"}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none select-none",children:(0,n.jsx)("div",{className:"text-[16rem] lg:text-[20rem] xl:text-[24rem] font-bold tracking-tighter opacity-[0.02] whitespace-nowrap",style:{fontFamily:"var(--font-geist-sans)",transform:"translateY(-10%)"},children:"Cubent"})}),(0,n.jsxs)("div",{className:"relative z-10 mx-auto max-w-7xl px-6 py-12 lg:px-8 lg:py-16",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4 mb-12",children:[(0,n.jsx)(m(),{href:"mailto:<EMAIL>",className:"text-foreground hover:text-muted-foreground transition-colors text-lg font-medium",children:"<EMAIL>"}),(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsx)(m(),{href:"https://x.com/cubent",target:"_blank",rel:"noopener noreferrer",className:"text-orange-500 hover:text-orange-400 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})}),(0,n.jsx)(m(),{href:"https://github.com/cubent",target:"_blank",rel:"noopener noreferrer",className:"text-orange-500 hover:text-orange-400 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})}),(0,n.jsx)(m(),{href:"https://discord.gg/cubent",target:"_blank",rel:"noopener noreferrer",className:"text-orange-500 hover:text-orange-400 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z"})})}),(0,n.jsx)(m(),{href:"https://youtube.com/@cubent",target:"_blank",rel:"noopener noreferrer",className:"text-orange-500 hover:text-orange-400 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})})]})]}),(0,n.jsx)("div",{className:"flex justify-center mb-12",children:(0,n.jsx)("div",{className:"grid grid-cols-2 gap-8 sm:grid-cols-4 max-w-4xl",children:t.map(e=>(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-foreground",children:e.title}),(0,n.jsx)("ul",{className:"flex flex-col gap-3",children:e.items?.map(e=>(0,n.jsx)("li",{children:(0,n.jsx)(m(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",target:e.href.includes("http")?"_blank":void 0,rel:e.href.includes("http")?"noopener noreferrer":void 0,children:e.title})},e.title))})]},e.title))})}),(0,n.jsxs)("div",{className:"flex flex-col items-center gap-6",children:[(0,n.jsx)(u,{}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," Made by Logicent Ltd"]})]})]})]})},f=()=>(0,n.jsx)(i.J,{queries:[o.a.postsQuery],children:(0,s.A)(p,"40f08c3d29a2f32c36fa37e5a9ba2c32897f96adb3",null)})},18915:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var n=r(99730),s=r(26708),a=r(45550),o=r(48585);let i=({privacyUrl:e,termsUrl:t,helpUrl:r,...i})=>{let{resolvedTheme:l}=(0,o.D)(),c="dark"===l?a.dark:void 0;return(0,n.jsx)(s.lJ,{...i,appearance:{layout:{privacyPageUrl:e,termsPageUrl:t,helpPageUrl:r},baseTheme:c,elements:{dividerLine:"bg-border",socialButtonsIconButton:"bg-card hover:bg-orange-500/10 border-orange-500/20 transition-colors py-3 px-4",navbarButton:"text-foreground",organizationSwitcherTrigger__open:"bg-background",organizationPreviewMainIdentifier:"text-foreground",organizationSwitcherTriggerIcon:"text-muted-foreground",organizationPreview__organizationSwitcherTrigger:"gap-2",organizationPreviewAvatarContainer:"shrink-0",header:"hidden",headerTitle:"hidden",headerSubtitle:"hidden",alternativeMethodsBlockButton:"hidden",alternativeMethodsBlockButtonText:"hidden",identityPreview:"hidden",identityPreviewText:"hidden",identityPreviewEditButton:"hidden",card:"!bg-white !shadow-none !border-0 !rounded-3xl p-4 mx-auto",rootBox:"relative !bg-white !rounded-3xl shadow-2xl min-w-[400px] mx-auto !border-0",main:"!bg-white !border-0 !shadow-none",modalContent:"!bg-white !border-0 !shadow-none",formButtonPrimary:"!bg-gradient-to-r !from-orange-500 !to-orange-600 hover:!from-orange-600 hover:!to-orange-700 !text-white !shadow-md !transition-all !duration-200 !py-1.5 !px-6 !text-base !font-medium !w-full !rounded-none !border-0",socialButtonsBlockButton:"!bg-slate-50 !border !border-slate-200 hover:!bg-slate-100 !text-gray-700 !shadow-sm hover:!shadow-md !transition-all !duration-200 !py-3 !px-6 !rounded-lg",formFieldInput:"!bg-slate-50 !border !border-slate-200 focus:!bg-white focus:!border-orange-500 focus:!ring-2 focus:!ring-orange-500/20 !rounded-lg !py-3 !px-4 !text-base !transition-all !duration-200"},variables:{fontFamily:"system-ui, -apple-system, sans-serif",fontFamilyButtons:"system-ui, -apple-system, sans-serif",fontWeight:{bold:700,normal:400,medium:500},colorPrimary:"#f97316",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorDanger:"#ef4444",colorNeutral:"#6b7280",colorText:"#1f2937",colorTextOnPrimaryBackground:"#ffffff",colorTextSecondary:"#374151",colorInputBackground:"#ffffff",colorInputText:"#111827",borderRadius:"0.75rem",spacingUnit:"1.25rem"}}})}},21731:(e,t,r)=>{Promise.resolve().then(r.bind(r,42908)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,19859)),Promise.resolve().then(r.bind(r,19865)),Promise.resolve().then(r.bind(r,354)),Promise.resolve().then(r.bind(r,27022)),Promise.resolve().then(r.bind(r,38980)),Promise.resolve().then(r.bind(r,15138)),Promise.resolve().then(r.bind(r,48585)),Promise.resolve().then(r.t.bind(r,41265,23)),Promise.resolve().then(r.t.bind(r,96908,23)),Promise.resolve().then(r.bind(r,22683)),Promise.resolve().then(r.bind(r,89620)),Promise.resolve().then(r.bind(r,18915)),Promise.resolve().then(r.bind(r,79047)),Promise.resolve().then(r.bind(r,38327)),Promise.resolve().then(r.bind(r,84685)),Promise.resolve().then(r.bind(r,41033))},22589:(e,t,r)=>{"use strict";r.d(t,{xK:()=>m,qr:()=>p,z2:()=>f,q2:()=>b,M7:()=>g});var n=r(52661);r(37091),Object.create;var s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=(Object.getPrototypeOf,Object.prototype.hasOwnProperty),l=r(23233),c=r(62644),d=r(55686),u=r(51472),h=l.lazy(()=>Promise.resolve().then(r.bind(r,43401)).then(e=>({default:e.ClientConditionalRenderer})));let m=async function(e,{bshbPreviewToken:t}){try{let{headers:r,url:n}=(0,u.GJ)(e),s=v(n,"/api/nextjs/preview-auth"),a=await fetch(s,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-token":r["x-basehub-token"]},body:JSON.stringify({bshbPreview:t})});if(!a.headers.get("content-type")?.includes("json"))return{status:400,response:{error:"Bad request"}};let o=await a.json();return 200===a.status&&(await (0,c.draftMode)()).enable(),{status:a.status,response:o}}catch(e){return{status:500,response:{error:"Something went wrong"}}}},p=async function(e,{bshbPreviewToken:t}){try{let{headers:r,url:n,isForcedDraft:s}=(0,u.GJ)(e);if(!1===(await (0,c.draftMode)()).isEnabled&&!s&&!t)return{status:403,response:{error:"Unauthorized"}};let a=v(n,"/api/nextjs/latest-branches"),o=await fetch(a,{cache:"no-store",method:"GET",headers:{"content-type":"application/json","x-basehub-token":r["x-basehub-token"],...t&&{"x-basehub-preview-token":t},...s&&{"x-basehub-forced-draft":"true"}}});if(!o.headers.get("content-type")?.includes("json"))return{status:400,response:{error:"Bad request"}};let i=await o.json();return{status:o.status,response:i}}catch(e){return{status:500,response:{error:"Something went wrong"}}}},f=async function(){(await (0,c.draftMode)()).disable()},b=async function(e,{bshbPreviewToken:t,ref:r}){let{headers:n,url:s}=(0,u.GJ)(e),a=v(s,"/api/nextjs/pending-tags");if(!t)return{success:!1,error:"Unauthorized"};let o=await fetch(a,{cache:"no-store",method:"GET",headers:{"content-type":"application/json","x-basehub-token":n["x-basehub-token"],"x-basehub-ref":r||n["x-basehub-ref"],"x-basehub-preview-token":t,"x-basehub-sdk-build-id":n["x-basehub-sdk-build-id"]}});if(200!==o.status)return{success:!1,message:`Received status ${o.status} from server`};let i=await o.json();try{let{tags:e}=i;if(!e||!Array.isArray(e)||0===e.length)return{success:!0,message:"No tags to revalidate"};return await Promise.all(e.map(async e=>{let t=e.startsWith("basehub-")?e:`basehub-${e}`;await (0,d.revalidateTag)(t)})),{success:!0,message:`Revalidated ${e.length} tags`}}catch(e){return console.log(i),console.error(e),{success:!1,message:"Something went wrong while revalidating tags"}}};var g=async({...e})=>{let{isForcedDraft:t}=(0,u.GJ)(e),r=(0,n.A)(m,"608b2f8eca36791b674ce9740d60d899091a017080",null),s=(0,n.A)(p,"600ae2011570e2df1d46454ad223098fc4baa55559",null),a=(0,n.A)(f,"00a93fb4bd742ae20ac4f2efc682a9dd8b873c54ce",null),o=(0,n.A)(b,"60800baff73db42f3be2da01face57da531e4ef986",null),i=r.bind(null,e),d=s.bind(null,e),g=o.bind(null,e);return l.createElement(h,{draft:(await (0,c.draftMode)()).isEnabled,isForcedDraft:t,enableDraftMode:i,disableDraftMode:a,revalidateTags:g,getLatestBranches:d,resolvedRef:u.Y2})};function v(e,t){let r;switch(!0){case e.origin.includes("api.basehub.com"):r="https://basehub.com"+t+e.search+e.hash;break;case e.origin.includes("api.bshb.dev"):r="https://basehub.dev"+t+e.search+e.hash;break;case e.origin.includes("localhost:3001"):r="http://localhost:3000"+t+e.search+e.hash;break;default:r=e.origin+t+e.search+e.hash}return r}},23669:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var n=r(96242);let s=(0,n.createServerReference)("7f2f5c5b51c39976549c9b796e9f92b90c613023ad",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},24505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f2f5c5b51c39976549c9b796e9f92b90c613023ad":()=>s.y,"7f74b2609587768b4f96033c3941567ea123d4aa10":()=>n.ai,"7f7a07c907510a35b2076c3ab7fb980bba39f345ba":()=>n.at,"7f8895703335f413188b5025fa2fc430107b4a90f5":()=>n.ot});var n=r(73819),s=r(44089)},28364:(e,t,r)=>{"use strict";r.d(t,{H:()=>a});var n=r(71166),s=r(25);let a=()=>(0,n.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:s.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:s.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:s.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}})},28427:(e,t,r)=>{var n={"./en.json":[94202,4202]};function s(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(n),s.id=28427,e.exports=s},30846:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,99597,23)),Promise.resolve().then(r.t.bind(r,71453,23)),Promise.resolve().then(r.t.bind(r,20213,23)),Promise.resolve().then(r.t.bind(r,13748,23)),Promise.resolve().then(r.t.bind(r,52812,23)),Promise.resolve().then(r.t.bind(r,73488,23)),Promise.resolve().then(r.t.bind(r,20134,23)),Promise.resolve().then(r.t.bind(r,15336,23))},34226:(e,t,r)=>{Promise.resolve().then(r.bind(r,15813))},34526:(e,t,r)=>{"use strict";r.d(t,{P:()=>c,f:()=>u});var n=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,c=(e,t)=>function(){return t||(0,e[o(e)[0]])((t={exports:{}}).exports,t),t.exports},d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of o(t))l.call(e,i)||i===r||s(e,i,{get:()=>t[i],enumerable:!(n=a(t,i))||n.enumerable});return e},u=(e,t,r)=>(r=null!=e?n(i(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e))},35574:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,10487,23)),Promise.resolve().then(r.t.bind(r,89819,23)),Promise.resolve().then(r.t.bind(r,73391,23)),Promise.resolve().then(r.t.bind(r,31310,23)),Promise.resolve().then(r.t.bind(r,28138,23)),Promise.resolve().then(r.t.bind(r,82926,23)),Promise.resolve().then(r.t.bind(r,58084,23)),Promise.resolve().then(r.t.bind(r,1934,23))},37919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var n=r(94752);r(77648);var s=r(77021),a=r(29329),o=r(28364),i=r(15330),l=r(44920);let{NEXT_PUBLIC_GA_MEASUREMENT_ID:c}=(0,o.H)(),d=({children:e})=>(0,n.jsxs)(i.PostHogProvider,{children:[e,(0,n.jsx)(l.Analytics,{}),c&&(0,n.jsx)(a.GoogleAnalytics,{gaId:c})]});var u=r(44577),h=r(54191),m=r(90411),p=r(15991);let f=({children:e,...t})=>(0,n.jsx)(p.ThemeProvider,{attribute:"class",defaultTheme:"dark",forcedTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,...t,children:e}),b=({children:e,privacyUrl:t,termsUrl:r,helpUrl:s,...a})=>(0,n.jsx)(f,{...a,children:(0,n.jsx)(u.AuthProvider,{privacyUrl:t,termsUrl:r,helpUrl:s,children:(0,n.jsxs)(d,{children:[(0,n.jsx)(m.TooltipProvider,{children:e}),(0,n.jsx)(h.Toaster,{})]})})});var g=r(48384),v=r(94055),x=r.n(v),w=r(29663),y=r.n(w);let j=(0,g.cn)(y().variable,x().variable,"touch-manipulation font-sans antialiased");var P=r(99956),C=r(51460);let N=()=>(0,C.H)().FLAGS_SECRET?(0,n.jsx)(P.N,{}):null;var E=r(77422),k=r(18362),_=r(74664);let T=async({children:e,params:t})=>{let{locale:r}=await t,a=await (0,E.T)(r);return(0,n.jsx)("html",{lang:r,className:(0,g.cn)(j,"scroll-smooth"),suppressHydrationWarning:!0,children:(0,n.jsxs)("body",{children:[(0,n.jsxs)(b,{children:[(0,n.jsx)(_.Header,{dictionary:a}),e,(0,n.jsx)(k.w,{})]}),(0,n.jsx)(N,{}),(0,n.jsx)(s.M,{})]})})}},38327:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientPump:()=>c}),r(76262);var n=r(35371);let s="__alias__";var a=!1,o=new Set,i=new Map,l=new Map,c=({children:e,rawQueries:t,pumpEndpoint:c,pumpToken:d,initialState:u,initialResolvedChildren:h,apiVersion:m,previewRef:p})=>{let f=n.useRef(d),[b,g]=n.useState(u),v=n.useRef(u);v.current=u;let[x,w]=n.useState(p),y=n.useRef(x);y.current=x;let j=n.useCallback(async()=>{let e,r,n,a=await Promise.all(t.map(async(t,a)=>{if(!f.current)return console.warn("No pump token found. Skipping query."),null;let o=JSON.stringify(t),d=o+x,u=l.get(o)||v.current?.responseHashes?.[a]||"";if(i.has(d)){let t=i.get(d);if(performance.now()-t.start<32){let s=await t.response;return s?(s.newPumpToken&&(e=s.newPumpToken),r=s.pusherData,n=s.spaceID,s):null}}let h=fetch(c,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":m,"x-basehub-ref":x},body:JSON.stringify({...t,pumpToken:f.current,lastResponseHash:u})}).then(async e=>{let{data:t=null,errors:r=null,newPumpToken:n,spaceID:a,pusherData:i,responseHash:c}=await e.json();return l.set(o,c),{data:function e(t){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>e(t));let r={};for(let[n,a]of Object.entries(t))if(n.includes(s)){let[t,...o]=n.split(s);r[o.join(s)]=e(a)}else r[n]=e(a);return r}(t),spaceID:a,pusherData:i,newPumpToken:n,errors:r,responseHash:c,changed:u!==c}}).catch(e=>{console.error(`Error fetching data from the BaseHub Draft API:
              
${JSON.stringify(e,null,2)}
              
Contact <EMAIL> for help.`)});i.set(d,{start:performance.now(),response:h});let p=await h;return p?(p.newPumpToken&&(e=p.newPumpToken),r=p.pusherData,n=p.spaceID,p):null}));if(a.some(e=>e?.changed)){if(!r||!n)return;g(e=>r&&n?{data:a.map((t,r)=>t?.changed?t?.data??null:e?.data?.[r]??null),errors:a.map((t,r)=>t?.changed?t?.errors??null:e?.errors?.[r]??null),responseHashes:a.map(e=>e?.responseHash??""),pusherData:r,spaceID:n}:e)}e&&(f.current=e)},[c,t,m,x]);n.useRef(null),n.useEffect(()=>{if(!b?.errors)return;let e=b.errors[0]?.[0];e&&console.error(`Error fetching data from the BaseHub Draft API: ${e.message}${e.path?` at ${e.path.join(".")}`:""}`)},[b?.errors]),n.useEffect(()=>{function e(){j()}return e(),o.add(e),()=>{o.delete(e)}},[j]);let[P,C]=n.useState(null),N=b?.pusherData?.channel_key,E=b?.pusherData.app_key,k=b?.pusherData.cluster;n.useEffect(()=>{if(!a&&E&&k)return a=!0,r.e(3611).then(r.bind(r,83611)).then(e=>{C(new e.default(E,{cluster:k}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{a=!1}},[E,k]),n.useEffect(()=>{if(!N||!P)return;let e=P.subscribe(N);return e.bind("poke",e=>{e?.mutatedEntryTypes?.includes("block")&&e.branch===y.current&&o.forEach(e=>e())}),()=>{e.unsubscribe()}},[P,N]),n.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&w(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let _=n.useMemo(()=>b?.data.map((e,t)=>e??u?.data?.[t]??null),[u?.data,b?.data]),[T,O]=n.useState("function"==typeof e?h:e);return n.useEffect(()=>{if(_)if("function"==typeof e){let t=e(_);t instanceof Promise?t.then(O):O(t)}else O(e)},[e,_]),T??h}},39440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",width:1200,height:630,url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"opengraph-image.png")+"?1de9f909622c0a32"}]},40356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientPump:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call ClientPump() from the server but ClientPump is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\cms\\.basehub\\react-pump\\client-pump-WYUPTPKD.js","ClientPump")},41033:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>a});var n=r(99730);r(35371);var s=r(47887);function a({delayDuration:e=0,...t}){return(0,n.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}r(83590)},42908:(e,t,r)=>{"use strict";r.d(t,{Header:()=>z});var n=r(99730),s=r(74938),a=r(35371),o=r(22866),i=r(72795),l=r(10439),c=r(83590);function d({className:e,children:t,viewport:r=!0,...s}){return(0,n.jsxs)(o.bL,{"data-slot":"navigation-menu","data-viewport":r,className:(0,c.cn)("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",e),...s,children:[t,r&&(0,n.jsx)(b,{})]})}function u({className:e,...t}){return(0,n.jsx)(o.B8,{"data-slot":"navigation-menu-list",className:(0,c.cn)("group flex flex-1 list-none items-center justify-center gap-1",e),...t})}function h({className:e,...t}){return(0,n.jsx)(o.q7,{"data-slot":"navigation-menu-item",className:(0,c.cn)("relative",e),...t})}let m=(0,i.F)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1");function p({className:e,children:t,...r}){return(0,n.jsxs)(o.l9,{"data-slot":"navigation-menu-trigger",className:(0,c.cn)(m(),"group",e),...r,children:[t," ",(0,n.jsx)(l.A,{className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}function f({className:e,...t}){return(0,n.jsx)(o.UC,{"data-slot":"navigation-menu-content",className:(0,c.cn)("data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none",e),...t})}function b({className:e,...t}){return(0,n.jsx)("div",{className:(0,c.cn)("absolute top-full left-0 isolate z-50 flex justify-center"),children:(0,n.jsx)(o.LM,{"data-slot":"navigation-menu-viewport",className:(0,c.cn)("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",e),...t})})}function g({className:e,...t}){return(0,n.jsx)(o.N_,{"data-slot":"navigation-menu-link",className:(0,c.cn)("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4",e),...t})}var v=r(37047),x=r(88529),w=r(25202),y=r(41265),j=r.n(y),P=r(53172),C=r(48473);let N={src:"/_next/static/media/logo.bef10624.svg",height:24,width:24,blurWidth:0,blurHeight:0};var E=r(14845);function k({...e}){return(0,n.jsx)(E.bL,{"data-slot":"dropdown-menu",...e})}function _({...e}){return(0,n.jsx)(E.l9,{"data-slot":"dropdown-menu-trigger",...e})}function T({className:e,sideOffset:t=4,...r}){return(0,n.jsx)(E.ZL,{children:(0,n.jsx)(E.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function O({className:e,inset:t,variant:r="default",...s}){return(0,n.jsx)(E.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function S({className:e,inset:t,...r}){return(0,n.jsx)(E.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,c.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function U({className:e,...t}){return(0,n.jsx)(E.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var A=r(97777),R=r(39378),D=r(52476),B=r(4862);let L=({user:e})=>{let t=e.fullName||e.firstName||e.emailAddresses[0]?.emailAddress||"User",r=e.emailAddresses[0]?.emailAddress;return(0,n.jsxs)(k,{children:[(0,n.jsx)(_,{asChild:!0,children:(0,n.jsx)(s.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,n.jsxs)(A.eu,{className:"h-10 w-10",children:[(0,n.jsx)(A.BK,{src:e.imageUrl,alt:t}),(0,n.jsx)(A.q5,{children:(e=>e?e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):"U")(e.fullName)})]})})}),(0,n.jsxs)(T,{className:"w-56",align:"end",forceMount:!0,children:[(0,n.jsx)(S,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium leading-none",children:t}),r&&(0,n.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:r})]})}),(0,n.jsx)(U,{}),(0,n.jsx)(O,{asChild:!0,children:(0,n.jsxs)(j(),{href:"https://app.cubent.dev",className:"flex items-center",children:[(0,n.jsx)(R.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Dashboard"})]})}),(0,n.jsx)(O,{asChild:!0,children:(0,n.jsxs)(j(),{href:"https://app.cubent.dev/settings",className:"flex items-center",children:[(0,n.jsx)(D.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Settings"})]})}),(0,n.jsx)(U,{}),(0,n.jsxs)(O,{onClick:()=>{window.location.href="https://app.cubent.dev/sign-out"},className:"flex items-center",children:[(0,n.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Sign out"})]})]})]})},z=({dictionary:e})=>{let{isAuthenticated:t,user:r,isLoading:o}=function(){let[e,t]=(0,a.useState)({isAuthenticated:!1,user:null,isLoading:!0});return e}(),i=(0,P.usePathname)(),l=[{title:"Pricing",href:"/pricing",description:""},{title:e.web.header.docs,href:"https://docs.cubent.dev/",description:""},{title:e.web.header.blog,href:"/blog",description:""},{title:"Company",description:"Learn more about Cubent",items:[{title:"About Us",href:"/about"},{title:"Contact",href:"/contact"}]}],c=e=>"/pricing"===e?"/pricing"===i:"/blog"===e&&i.startsWith("/blog"),[m,b]=(0,a.useState)(!1);return(0,n.jsxs)("header",{className:"sticky top-0 left-0 z-40 w-full bg-background/20 backdrop-blur-md supports-[backdrop-filter]:bg-background/10",children:[(0,n.jsxs)("div",{className:"w-full bg-gray-800/60 border-b border-gray-600/20 text-gray-200 py-2.5 px-4 text-center text-sm backdrop-blur-sm",children:[(0,n.jsx)("span",{className:"font-medium",children:"Early Access:"})," We released the Byak plan -",(0,n.jsx)(s.$,{variant:"link",className:"text-gray-200 hover:text-white underline p-0 ml-1 h-auto font-medium text-sm",asChild:!0,children:(0,n.jsx)(j(),{href:"https://app.cubent.dev/sign-in",children:"Start your free trial"})})]}),(0,n.jsx)("div",{className:"border-b",children:(0,n.jsxs)("div",{className:"relative w-full max-w-none flex min-h-20 flex-row items-center justify-between",style:{paddingInline:"clamp(1rem, 2.5%, 2rem)"},children:[(0,n.jsxs)(j(),{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,n.jsx)(C.default,{src:N,alt:"Cubent Logo",width:32,height:32,className:"dark:invert"}),(0,n.jsx)("p",{className:"whitespace-nowrap font-semibold",children:"Cubent"})]}),(0,n.jsx)("div",{className:"hidden flex-row items-center justify-center gap-3 lg:flex absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2",children:(0,n.jsx)(d,{className:"flex items-center justify-center",children:(0,n.jsx)(u,{className:"flex flex-row justify-center gap-3",children:l.map(e=>(0,n.jsx)(h,{children:e.href?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(g,{asChild:!0,children:(0,n.jsx)(s.$,{variant:"ghost",asChild:!0,className:c(e.href)?"bg-neutral-800/50 text-white":"",children:(0,n.jsx)(j(),{href:e.href,target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:e.title})})})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p,{className:"font-medium text-sm",children:e.title}),(0,n.jsx)(f,{className:"!w-[450px] p-4",children:(0,n.jsxs)("div",{className:"flex grid-cols-2 flex-col gap-4 lg:grid",children:[(0,n.jsxs)("div",{className:"flex h-full flex-col justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("p",{className:"text-base",children:e.title}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description})]}),(0,n.jsx)(s.$,{size:"sm",className:"mt-10",asChild:!0,children:(0,n.jsx)(j(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",children:"Download Extension"})})]}),(0,n.jsx)("div",{className:"flex h-full flex-col justify-end text-sm",children:e.items?.map((e,t)=>(0,n.jsxs)(g,{href:e.href,className:"flex flex-row items-center justify-between rounded px-4 py-2 hover:bg-muted",children:[(0,n.jsx)("span",{children:e.title}),(0,n.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]},t))})]})})]})},e.title))})})}),(0,n.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,n.jsx)(s.$,{variant:"outline",asChild:!0,className:"hidden md:inline-flex h-10",children:(0,n.jsxs)(j(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",className:"flex flex-row items-center gap-2 px-4 py-2 whitespace-nowrap",children:[(0,n.jsx)("svg",{className:"h-4 w-4 shrink-0",viewBox:"0 0 88 88",fill:"currentColor",children:(0,n.jsx)("path",{d:"M0 12.402l35.687-4.86.016 34.423L0 45.194zm35.67 33.529l.028 34.453L.028 75.48.026 45.7zm4.326-39.025L87.314 0v41.527l-47.318 4.425zm47.329 39.349v41.527L40.028 81.441l.016-34.486z"})}),(0,n.jsx)("span",{className:"shrink-0 text-sm",children:"Download"})]})}),o?(0,n.jsx)("div",{className:"h-10 w-10 animate-pulse bg-gray-200 rounded-full"}):t&&r?(0,n.jsx)(L,{user:r}):(0,n.jsx)(s.$,{asChild:!0,children:(0,n.jsx)(j(),{href:"https://app.cubent.dev/sign-in",children:"Sign In"})})]}),(0,n.jsxs)("div",{className:"flex w-12 shrink items-end justify-end lg:hidden",children:[(0,n.jsx)(s.$,{variant:"ghost",onClick:()=>b(!m),children:m?(0,n.jsx)(x.A,{className:"h-5 w-5"}):(0,n.jsx)(w.A,{className:"h-5 w-5"})}),m&&(0,n.jsx)("div",{className:"container absolute top-20 right-0 flex w-full flex-col gap-8 border-t bg-background py-4 shadow-lg px-4 sm:px-6 lg:px-8",children:l.map(e=>(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[e.href?(0,n.jsxs)(j(),{href:e.href,className:`flex items-center justify-between ${c(e.href)?"bg-neutral-800/50 text-white rounded px-2 py-1":""}`,target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:[(0,n.jsx)("span",{className:"text-lg",children:e.title}),(0,n.jsx)(v.A,{className:"h-4 w-4 stroke-1 text-muted-foreground"})]}):(0,n.jsx)("p",{className:"text-lg",children:e.title}),e.items?.map(e=>(0,n.jsxs)(j(),{href:e.href,className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:e.title}),(0,n.jsx)(v.A,{className:"h-4 w-4 stroke-1"})]},e.title))]})},e.title))})]})]})})]})}},43401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientConditionalRenderer:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call ClientConditionalRenderer() from the server but ClientConditionalRenderer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\cms\\.basehub\\next-toolbar\\client-conditional-renderer-KQINRCBN.js","ClientConditionalRenderer")},44577:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\auth\\provider.tsx","AuthProvider")},45707:(e,t,r)=>{Promise.resolve().then(r.bind(r,74664)),Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.t.bind(r,41853,23)),Promise.resolve().then(r.t.bind(r,48031,23)),Promise.resolve().then(r.t.bind(r,80476,23)),Promise.resolve().then(r.bind(r,44920)),Promise.resolve().then(r.bind(r,11106)),Promise.resolve().then(r.bind(r,8340)),Promise.resolve().then(r.bind(r,15991)),Promise.resolve().then(r.t.bind(r,49499,23)),Promise.resolve().then(r.t.bind(r,21970,23)),Promise.resolve().then(r.bind(r,93665)),Promise.resolve().then(r.bind(r,15330)),Promise.resolve().then(r.bind(r,44577)),Promise.resolve().then(r.bind(r,43401)),Promise.resolve().then(r.bind(r,40356)),Promise.resolve().then(r.bind(r,54191)),Promise.resolve().then(r.bind(r,90411))},48384:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o,cn:()=>a}),r(42870);var n=r(57752);r(93665);var s=r(48410);let a=(...e)=>(0,s.QP)((0,n.$)(e)),o=e=>e.charAt(0).toUpperCase()+e.slice(1)},54191:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sonner.tsx","Toaster")},71178:(e,t,r)=>{Promise.resolve().then(r.bind(r,84641))},74619:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=74619,e.exports=t},74664:(e,t,r)=>{"use strict";r.d(t,{Header:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\components\\header\\index.tsx","Header")},74938:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var n=r(99730);r(35371);var s=r(58576),a=r(72795),o=r(83590);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:a=!1,...l}){let c=a?s.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}},76262:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=Object.getOwnPropertyNames,s=(e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports}},77021:(e,t,r)=>{"use strict";r.d(t,{M:()=>n.M7});var n=r(22589)},77422:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});let n=JSON.parse('{"H":{"s":"en","z":[]}}'),s=[n.H.s,...n.H.z],a=Object.fromEntries(s.map(e=>[e,()=>r(28427)(`./${e}.json`).then(e=>e.default).catch(t=>(console.error(`Failed to load dictionary for locale: ${e}`,t),r.e(4202).then(r.t.bind(r,94202,19)).then(e=>e.default)))])),o=async e=>{let t=e.split("-")[0];if(!s.includes(t))return console.warn(`Locale "${e}" is not supported, defaulting to "en"`),a.en();try{return await a[t]()}catch(e){return console.error(`Error loading dictionary for locale "${t}", falling back to "en"`,e),a.en()}}},77648:()=>{},79047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientConditionalRenderer:()=>o}),r(34526);var n=r(35371),s=r(6754),a=n.lazy(()=>r.e(7453).then(r.bind(r,87453)).then(e=>({default:e.ClientToolbar}))),o=({draft:e,isForcedDraft:t,enableDraftMode:r,disableDraftMode:o,revalidateTags:i,resolvedRef:l,getLatestBranches:c})=>{let[d,u]=n.useState(!1);n.useEffect(()=>{u(!0)},[]);let h=`bshb-preview-${l.repoHash}`,m=n.useCallback(e=>{},[h]),[p,f]=n.useState(m),[b,g]=n.useState();return(n.useLayoutEffect(()=>{if(e||t)return void g(!1);let r=m("url-only");if(!r)return void g(!1);f(r),g(!0)},[e,t,m]),n.useEffect(()=>{let e=new URL(window.location.href),t="true"===e.searchParams.get("__bshb-odr"),r=e.searchParams.get("__bshb-odr-token"),n=e.searchParams.get("__bshb-odr-ref");t&&r&&i({bshbPreviewToken:r,...n?{ref:n}:{}}).then(({success:e,message:t})=>{document.documentElement.dataset.basehubOdrStatus=e?"success":"error",e||(document.documentElement.dataset.basehubOdrErrorMessage="Response failed"),t&&(document.documentElement.dataset.basehubOdrMessage=t)}).catch(e=>{document.documentElement.dataset.basehubOdrStatus="error";let t="";try{t=e.message}catch(e){console.error(e),t="Unknown error"}document.documentElement.dataset.basehubOdrErrorMessage=t})},[i]),(p||t)&&d&&"undefined"!=typeof document)?(0,s.createPortal)(n.createElement(a,{disableDraftMode:o,enableDraftMode:r,draft:e,isForcedDraft:t,bshbPreviewToken:p,shouldAutoEnableDraft:b,seekAndStoreBshbPreviewToken:m,resolvedRef:l,getLatestBranches:c,bshbPreviewLSName:h}),document.body):null}},83590:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a}),r(60188);var n=r(29085);r(22683);var s=r(63632);let a=(...e)=>(0,s.QP)((0,n.$)(e))},84641:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\web\\\\app\\\\[locale]\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx","default")},84685:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});var n=r(99730),s=r(48585),a=r(22683);let o=({...e})=>{let{theme:t="system"}=(0,s.D)();return(0,n.jsx)(a.l,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},89620:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>d});var n=r(99730),s=r(1429),a=r(87805),o=r(35371),i=r(79796),l=r(42294);let c=()=>(0,i.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:l.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:l.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:l.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),d=e=>((0,o.useEffect)(()=>{try{let e=c();e.NEXT_PUBLIC_POSTHOG_KEY&&e.NEXT_PUBLIC_POSTHOG_HOST?s.Ay.init(e.NEXT_PUBLIC_POSTHOG_KEY,{api_host:"/ingest",ui_host:e.NEXT_PUBLIC_POSTHOG_HOST,person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0}):console.warn("PostHog environment variables not configured. Analytics disabled.")}catch(e){console.warn("PostHog initialization failed:",e)}},[]),(0,n.jsx)(a.so,{client:s.Ay,...e}))},90411:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>s});var n=r(6340);(0,n.registerClientReference)(function(){throw Error("Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","Tooltip"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipTrigger"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipContent");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipProvider")},97777:(e,t,r)=>{"use strict";r.d(t,{BK:()=>i,eu:()=>o,q5:()=>l});var n=r(99730);r(35371);var s=r(97480),a=r(83590);function o({className:e,...t}){return(0,n.jsx)(s.bL,{"data-slot":"avatar",className:(0,a.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function i({className:e,...t}){return(0,n.jsx)(s._V,{"data-slot":"avatar-image",className:(0,a.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,n.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,a.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}}};