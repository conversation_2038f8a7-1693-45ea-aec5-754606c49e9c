import React, { useState, useEffect } from "react"
import { BarChart3, Download, Upload, Trash2, RefreshCw } from "lucide-react"

import { But<PERSON> } from "@src/components/ui"
import { SectionHeader } from "./SectionHeader"
import { Section } from "./Section"
import {
	getUsageStats,
	getUsageStatsAsync,
	getUsageStatsForPeriod,
	getUsageStatsForPeriodAsync,
	getUsageByModel,
	clearUsageStats,
	exportUsageData,
	importUsageData,
	debugStorageKeys,
} from "../../utils/usage-tracking"
import { vscode } from "../../utils/vscode"

interface UsageSettingsProps {
	// No props needed for now
}

export const UsageSettings: React.FC<UsageSettingsProps> = () => {
	const [usageStats, setUsageStats] = useState<{
		totalCubentUnits: number
		totalMessages: number
		entries: Array<{
			timestamp: number
			modelId: string
			cubentUnits: number
			messageCount: number
			provider: string
			configName: string
		}>
		lastUpdated: number
	}>({
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	})
	const [selectedPeriod, setSelectedPeriod] = useState<number | null>(null) // null = all time
	const [modelBreakdown, setModelBreakdown] = useState<Record<string, { cubentUnits: number; messages: number }>>({})
	const [isLoading, setIsLoading] = useState(false)

	// Load usage stats using server-first approach with localStorage fallback
	const loadUsageStats = async () => {
		console.log("UsageSettings: Loading usage stats...")
		console.log("UsageSettings: Debug storage keys first...")
		debugStorageKeys() // Debug what's actually in localStorage

		// DEBUG: Test server connection
		console.log("🔍 DEBUGGING: Testing server connection...")
		vscode.postMessage({
			type: "getServerUsageStats",
			messageId: `debug-${Date.now()}`,
			days: selectedPeriod || undefined,
		})

		try {
			// Use server-first approach with localStorage fallback
			let stats
			if (selectedPeriod) {
				stats = await getUsageStatsForPeriodAsync(selectedPeriod)
			} else {
				stats = await getUsageStatsAsync()
			}

			console.log("UsageSettings: Got stats from usage-tracking:", stats)
			setUsageStats(stats)

			// Calculate model breakdown using the proper function
			const breakdown = getUsageByModel(selectedPeriod || undefined)
			console.log("UsageSettings: Model breakdown:", breakdown)
			setModelBreakdown(breakdown)
		} catch (error) {
			console.error("UsageSettings: Error loading usage stats:", error)
			// Fallback to localStorage-only approach
			try {
				let fallbackStats
				if (selectedPeriod) {
					fallbackStats = getUsageStatsForPeriod(selectedPeriod)
				} else {
					fallbackStats = getUsageStats()
				}
				setUsageStats(fallbackStats)

				const fallbackBreakdown = getUsageByModel(selectedPeriod || undefined)
				setModelBreakdown(fallbackBreakdown)
			} catch (fallbackError) {
				console.error("UsageSettings: Fallback also failed:", fallbackError)
				setUsageStats({
					totalCubentUnits: 0,
					totalMessages: 0,
					entries: [],
					lastUpdated: Date.now(),
				})
				setModelBreakdown({})
			}
		}
	}

	// Load stats on component mount and when period changes
	useEffect(() => {
		loadUsageStats()
	}, [selectedPeriod])

	// Listen for localStorage changes to automatically refresh data
	useEffect(() => {
		const handleStorageChange = (e: StorageEvent) => {
			// Listen for any cubent usage stats changes (user-specific keys)
			if (e.key && e.key.startsWith("cubent-usage-stats")) {
				console.log("UsageSettings: localStorage changed, refreshing data")
				loadUsageStats()
			}
		}

		// Listen for storage events (from other tabs/windows)
		window.addEventListener("storage", handleStorageChange)

		// Also set up a periodic refresh to catch changes in the same tab
		const interval = setInterval(() => {
			loadUsageStats()
		}, 5000) // Refresh every 5 seconds

		return () => {
			window.removeEventListener("storage", handleStorageChange)
			clearInterval(interval)
		}
	}, [selectedPeriod])

	// Refresh stats
	const handleRefresh = () => {
		setIsLoading(true)
		setTimeout(() => {
			loadUsageStats()
			setIsLoading(false)
		}, 100)
	}

	// Clear all usage data
	const handleClearData = () => {
		if (window.confirm("Are you sure you want to clear all usage data? This action cannot be undone.")) {
			clearUsageStats()
			loadUsageStats()
		}
	}

	// DEBUG: Test server tracking
	const testServerTracking = () => {
		console.log("🧪 TESTING: Sending test usage to server...")
		vscode.postMessage({
			type: "trackUserUsage",
			data: {
				modelId: "test-model",
				provider: "test-provider",
				configName: "test-config",
				cubentUnits: 0.5,
				messageCount: 1,
				timestamp: Date.now(),
			},
		})
	}

	// DEBUG: Get usage tracking status
	const checkTrackingStatus = () => {
		console.log("🔍 CHECKING: Usage tracking status...")
		vscode.postMessage({
			type: "getUsageTrackingStatus",
			messageId: `status-${Date.now()}`,
		})
	}

	// DEBUG: Force process usage queue
	const forceProcessQueue = () => {
		console.log("🔄 FORCING: Process usage queue...")
		vscode.postMessage({
			type: "forceProcessUsageQueue",
		})
	}

	// Export usage data
	const handleExport = () => {
		const data = exportUsageData()
		const blob = new Blob([data], { type: "application/json" })
		const url = URL.createObjectURL(blob)
		const a = document.createElement("a")
		a.href = url
		a.download = `cubent-usage-${new Date().toISOString().split("T")[0]}.json`
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	}

	// Import usage data
	const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (file) {
			const reader = new FileReader()
			reader.onload = (e) => {
				const content = e.target?.result as string
				if (content) {
					const success = importUsageData(content)
					if (success) {
						loadUsageStats()
						alert("Usage data imported successfully!")
					} else {
						alert("Failed to import usage data. Please check the file format.")
					}
				}
			}
			reader.readAsText(file)
		}
		// Reset the input
		event.target.value = ""
	}

	// Format date
	const formatDate = (timestamp: number) => {
		return new Date(timestamp).toLocaleDateString()
	}

	// Get top models by usage
	const topModels = Object.entries(modelBreakdown)
		.sort(([, a], [, b]) => b.cubentUnits - a.cubentUnits)
		.slice(0, 5)

	return (
		<div>
			<SectionHeader>
				<div className="flex items-center gap-2">
					<BarChart3 className="w-4" />
					<div>Cubent Units Usage</div>
				</div>
			</SectionHeader>

			<Section>
				{/* Overview Stats */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h4 className="text-sm font-medium text-vscode-foreground">Usage Overview</h4>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={handleRefresh}
								disabled={isLoading}
								className="flex items-center gap-1">
								<RefreshCw className={`w-3 h-3 ${isLoading ? "animate-spin" : ""}`} />
								Refresh
							</Button>
						</div>
					</div>

					{/* Period Selector */}
					<div className="flex items-center gap-2 text-sm">
						<span className="text-vscode-descriptionForeground">Time period:</span>
						<select
							value={selectedPeriod || "all"}
							onChange={(e) =>
								setSelectedPeriod(e.target.value === "all" ? null : parseInt(e.target.value))
							}
							className="bg-vscode-input-background text-vscode-input-foreground border border-vscode-input-border rounded px-2 py-1">
							<option value="all">All time</option>
							<option value="1">Last 24 hours</option>
							<option value="7">Last 7 days</option>
							<option value="30">Last 30 days</option>
						</select>
					</div>

					{/* Stats Cards */}
					<div className="grid grid-cols-2 gap-4">
						<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
							<div className="text-2xl font-bold text-vscode-foreground">
								{usageStats.totalCubentUnits.toFixed(2)}
							</div>
							<div className="text-sm text-vscode-descriptionForeground">Total Cubent Units</div>
						</div>
						<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
							<div className="text-2xl font-bold text-vscode-foreground">
								{usageStats.totalMessages.toLocaleString()}
							</div>
							<div className="text-sm text-vscode-descriptionForeground">Messages Sent</div>
						</div>
					</div>

					{/* Top Models */}
					{topModels.length > 0 && (
						<div>
							<h5 className="text-sm font-medium text-vscode-foreground mb-2">Top Models by Usage</h5>
							<div className="space-y-2">
								{topModels.map(([modelId, stats]) => (
									<div key={modelId} className="flex items-center justify-between text-sm">
										<span className="text-vscode-foreground font-mono">{modelId}</span>
										<div className="flex items-center gap-4 text-vscode-descriptionForeground">
											<span>{stats.cubentUnits.toFixed(2)} units</span>
											<span>{stats.messages} msgs</span>
										</div>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Last Updated */}
					{usageStats.lastUpdated && (
						<div className="text-xs text-vscode-descriptionForeground">
							Last updated: {formatDate(usageStats.lastUpdated)}
						</div>
					)}
				</div>
			</Section>

			{/* Data Management */}
			<Section>
				<div className="space-y-4">
					<h4 className="text-sm font-medium text-vscode-foreground">Data Management</h4>

					<div className="flex items-center gap-2">
						<Button variant="outline" size="sm" onClick={handleExport} className="flex items-center gap-1">
							<Download className="w-3 h-3" />
							Export Data
						</Button>

						<label className="cursor-pointer">
							<Button variant="outline" size="sm" className="flex items-center gap-1" asChild>
								<span>
									<Upload className="w-3 h-3" />
									Import Data
								</span>
							</Button>
							<input type="file" accept=".json" onChange={handleImport} className="hidden" />
						</label>

						<Button
							variant="outline"
							size="sm"
							onClick={handleClearData}
							className="flex items-center gap-1 text-red-600 hover:text-red-700">
							<Trash2 className="w-3 h-3" />
							Clear Data
						</Button>

						<Button
							variant="outline"
							size="sm"
							onClick={testServerTracking}
							className="flex items-center gap-1 text-blue-600 hover:text-blue-700">
							🧪 Test Server
						</Button>

						<Button
							variant="outline"
							size="sm"
							onClick={checkTrackingStatus}
							className="flex items-center gap-1 text-green-600 hover:text-green-700">
							🔍 Check Status
						</Button>

						<Button
							variant="outline"
							size="sm"
							onClick={forceProcessQueue}
							className="flex items-center gap-1 text-orange-600 hover:text-orange-700">
							🔄 Force Sync
						</Button>
					</div>

					<div className="text-xs text-vscode-descriptionForeground">
						Usage data is stored locally in your browser. Export your data to back it up or transfer it to
						another device.
					</div>
				</div>
			</Section>
		</div>
	)
}
