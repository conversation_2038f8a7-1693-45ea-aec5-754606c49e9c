{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/api/app/layout.tsx"], "sourcesContent": ["import type { ReactNode } from 'react';\n\ntype RootLayoutProperties = {\n  readonly children: ReactNode;\n};\n\nconst RootLayout = ({ children }: RootLayoutProperties) => (\n  <html lang=\"en\">\n    <body>{children}</body>\n  </html>\n);\n\nexport default RootLayout;\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAwB,iBACpD,6VAAC;QAAK,MAAK;kBACT,cAAA,6VAAC;sBAAM;;;;;;;;;;;uCAII", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/next%4015.3.2_%40babel%2Bcore%407.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4MAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}