
> @qapt-coder/vscode-e2e@ check-types C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\apps\vscode-e2e
> tsc -p tsconfig.esm.json --noEmit

src/suite/index.ts(2,8): error TS1259: Module '"mocha"' can only be default-imported using the 'esModuleInterop' flag
src/suite/index.ts(6,33): error TS2307: Cannot find module '@qapt-coder/types' or its corresponding type declarations.
src/suite/subtasks.test.ts(3,35): error TS2307: Cannot find module '@qapt-coder/types' or its corresponding type declarations.
src/suite/task.test.ts(3,35): error TS2307: Cannot find module '@qapt-coder/types' or its corresponding type declarations.
src/suite/utils.ts(1,33): error TS2307: Cannot find module '@qapt-coder/types' or its corresponding type declarations.
src/types/global.d.ts(1,33): error TS2307: Cannot find module '@qapt-coder/types' or its corresponding type declarations.
tsconfig.esm.json(2,13): error TS6053: File '@qapt-coder/config-typescript/base.json' not found.
../../node_modules/.pnpm/glob@11.0.2/node_modules/glob/dist/commonjs/pattern.d.ts(19,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/glob@11.0.2/node_modules/glob/dist/commonjs/walker.d.ts(54,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/lru-cache@11.1.0/node_modules/lru-cache/dist/commonjs/index.d.ts(20,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/lru-cache@11.1.0/node_modules/lru-cache/dist/commonjs/index.d.ts(866,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/minimatch@10.0.1/node_modules/minimatch/dist/commonjs/ast.d.ts(4,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/path-scurry@2.0.0/node_modules/path-scurry/dist/commonjs/index.d.ts(115,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
../../node_modules/.pnpm/path-scurry@2.0.0/node_modules/path-scurry/dist/commonjs/index.d.ts(586,5): error TS18028: Private identifiers are only available when targeting ECMAScript 2015 and higher.
 ELIFECYCLE  Command failed with exit code 2.
