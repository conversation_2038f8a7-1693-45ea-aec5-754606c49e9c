# User Management System for Cubent

This document describes the comprehensive user management system implemented for Cubent, inspired by the Refact extension's approach but enhanced with modern features like subscription tiers, usage tracking, and trial management.

## Overview

The user management system provides:

- **User Authentication** - Integration with existing Clerk authentication
- **Subscription Management** - Multiple tiers with different quotas and features
- **Usage Tracking** - Real-time monitoring of tokens, costs, and API requests
- **Trial Management** - 14-day free trials with extension capabilities
- **Quota Enforcement** - Automatic limiting based on subscription tier
- **Upgrade Prompts** - Smart notifications and upgrade suggestions

## Architecture

### Core Services

1. **UserManagementService** (`src/core/user/UserManagementService.ts`)

    - Manages user profiles and subscription information
    - Handles user creation, updates, and preferences
    - Integrates with Clerk authentication system

2. **UsageTrackingService** (`src/core/user/UsageTrackingService.ts`)

    - Tracks API usage in real-time
    - Monitors tokens, costs, and request counts
    - Generates usage alerts and warnings
    - Handles automatic counter resets

3. **TrialManagementService** (`src/core/user/TrialManagementService.ts`)

    - Manages trial periods and extensions
    - Handles trial expiry notifications
    - Provides upgrade prompts and suggestions

4. **UserManagementIntegration** (`src/core/user/UserManagementIntegration.ts`)

    - Coordinates all user management services
    - Provides unified API for the extension
    - Handles webview message routing

5. **SupabaseUserService** (`src/core/user/SupabaseUserService.ts`)
    - Database integration for persistent storage
    - Handles user data synchronization
    - Provides backup for local storage

### Database Schema

The system uses Supabase PostgreSQL with the following tables:

- **user_profiles** - Core user information and subscription details
- **usage_quotas** - Subscription tier limits and features
- **usage_metrics** - Real-time usage tracking per user
- **model_usage** - Detailed breakdown by AI model
- **usage_alerts** - User notifications and warnings
- **api_usage_logs** - Detailed API call history
- **subscription_history** - Subscription change tracking

## Subscription Tiers

### Free Trial

- **Duration**: 14 days (extendable up to 2 times, 7 days each)
- **Token Limit**: 100,000 tokens/month
- **Cost Limit**: $10/month
- **Rate Limits**: 50 requests/hour, 500 requests/day
- **Models**: Claude 3.5 Sonnet, GPT-4o Mini, Gemini 1.5 Flash
- **Features**: Basic functionality only

### Basic ($19/month)

- **Token Limit**: 1,000,000 tokens/month
- **Cost Limit**: $50/month
- **Rate Limits**: 200 requests/hour, 2,000 requests/day
- **Models**: All standard models (Claude, GPT-4, Gemini)
- **Features**: Codebase indexing, custom modes, export history

### Pro ($49/month)

- **Token Limit**: 5,000,000 tokens/month
- **Cost Limit**: $200/month
- **Rate Limits**: 500 requests/hour, 5,000 requests/day
- **Models**: All models including reasoning models (o1, Claude Thinking)
- **Features**: All Basic features + reasoning models, priority support

### Enterprise ($199/month)

- **Token Limit**: 20,000,000 tokens/month
- **Cost Limit**: $1,000/month
- **Rate Limits**: 2,000 requests/hour, 20,000 requests/day
- **Models**: All available models
- **Features**: All features, dedicated support, custom integrations

## Usage Tracking

### Real-time Monitoring

- **Token Usage**: Input + output tokens per request
- **Cost Tracking**: Actual API costs per model
- **Request Counting**: Hourly and daily rate limiting
- **Model Breakdown**: Usage statistics per AI model

### Automatic Resets

- **Monthly**: Tokens and costs reset on the 1st of each month
- **Hourly**: Request counters reset every hour
- **Daily**: Daily request counters reset at midnight

### Alerts and Warnings

- **80% Warning**: Alert when approaching limits
- **95% Critical**: Final warning before cutoff
- **100% Limit**: Service suspension until reset or upgrade

## Trial Management

### Trial Features

- **14-day Duration**: Standard trial period
- **2 Extensions**: Up to 7 days each (total 28 days possible)
- **Automatic Notifications**: 7, 3, and 1 day warnings
- **Upgrade Prompts**: Smart suggestions based on usage patterns

### Extension Criteria

- Available when 3 or fewer days remain
- Maximum 2 extensions per user
- Requires user action (not automatic)

## Integration Points

### Authentication

```typescript
// Integrates with existing Clerk authentication
this.authService.on("user-info", this.handleUserInfoUpdate.bind(this))
this.authService.on("logged-out", this.handleLogout.bind(this))
```

### API Request Filtering

```typescript
// Check before making API requests
const canMakeRequest = userManagementIntegration.canMakeApiRequest(modelId)
if (!canMakeRequest.allowed) {
	// Show upgrade prompt or error message
	return
}
```

### Usage Tracking

```typescript
// Track usage after API calls
await userManagementIntegration.trackApiUsage(modelId, usage, cost)
```

### Model Filtering

```typescript
// Filter available models based on subscription
const availableModels = userManagementIntegration.getAvailableModels(allModels)
```

## UI Components

### Usage Display (`webview-ui/src/components/user/UsageDisplay.tsx`)

- Real-time usage statistics
- Progress bars for limits
- Model usage breakdown
- Trial status and countdown

### Settings Panel (`webview-ui/src/components/user/UserManagementSettings.tsx`)

- Account information
- Subscription status
- Usage preferences
- Notification settings
- Upgrade options

## Configuration

### Environment Variables

```bash
# Supabase configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Feature flags
ENABLE_USER_MANAGEMENT=true
ENABLE_USAGE_TRACKING=true
ENABLE_TRIAL_MANAGEMENT=true
```

### VS Code Settings

```json
{
	"cubent.userManagement.enabled": true,
	"cubent.usageTracking.detailed": true,
	"cubent.trial.notifications": true
}
```

## Implementation Status

### ✅ Completed

- Core service architecture
- Database schema and functions
- Type definitions and interfaces
- Basic UI components
- Extension integration points

### 🚧 In Progress

- Supabase integration testing
- UI component integration
- Billing system integration
- Advanced analytics

### 📋 Planned

- Usage analytics dashboard
- Advanced trial management
- Enterprise features
- Mobile app integration

## Usage Examples

### Check User Permissions

```typescript
const userManagement = getUserManagementIntegration()
const canUseModel = userManagement?.canMakeApiRequest("Claude Sonnet 4")

if (!canUseModel?.allowed) {
	vscode.window.showErrorMessage(canUseModel.reason)
	return
}
```

### Track API Usage

```typescript
// After successful API call
await userManagement?.trackApiUsage(modelId, {
	type: "usage",
	inputTokens: 1000,
	outputTokens: 500,
	totalCost: 0.05,
})
```

### Display Usage Information

```typescript
// In webview component
const usageStats = userManagement?.getServices().usageTracking.getUsageStats()
const trialInfo = userManagement?.getServices().trialManagement.getTrialInfo()
```

## Security Considerations

- **Row Level Security**: Database policies ensure users only access their own data
- **API Key Protection**: Secure handling of authentication tokens
- **Rate Limiting**: Prevents abuse and ensures fair usage
- **Data Encryption**: Sensitive data encrypted at rest and in transit

## Monitoring and Analytics

- **Usage Patterns**: Track how users interact with different models
- **Conversion Metrics**: Monitor trial-to-paid conversion rates
- **Performance Monitoring**: Track API response times and error rates
- **Cost Analysis**: Monitor actual vs. estimated costs per user

This user management system provides a robust foundation for managing users, subscriptions, and usage in the Cubent extension while maintaining security, performance, and user experience standards.
