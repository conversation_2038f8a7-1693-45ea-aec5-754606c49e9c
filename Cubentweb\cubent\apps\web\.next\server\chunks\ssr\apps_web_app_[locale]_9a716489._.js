module.exports = {

"[project]/apps/web/app/[locale]/opengraph-image.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/opengraph-image.5c19e39e.png");}}),
"[project]/apps/web/app/[locale]/opengraph-image.png.mjs { IMAGE => \"[project]/apps/web/app/[locale]/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$opengraph$2d$image$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/app/[locale]/opengraph-image.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$opengraph$2d$image$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 1200,
    height: 630
};
}}),

};

//# sourceMappingURL=apps_web_app_%5Blocale%5D_9a716489._.js.map