{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/legal/layout.tsx"], "sourcesContent": ["import { Toolbar } from '@repo/cms/components/toolbar';\nimport type { ReactNode } from 'react';\n\ntype LegalLayoutProps = {\n  children: ReactNode;\n};\n\nconst LegalLayout = ({ children }: LegalLayoutProps) => (\n  <>\n    {children}\n    <Toolbar />\n  </>\n);\n\nexport default LegalLayout;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAOA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAoB,iBACjD;;YACG;0BACD,6VAAC,wJAAA,CAAA,UAAO;;;;;;;uCAIG", "debugId": null}}]}