{"version": 3, "sources": [], "sections": [{"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,+QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,wOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,wOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,wOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,wOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,wOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,QAAQ,GAAG,CAAC,eAAe;YAC5C,YAAY,QAAQ,GAAG,CAAC,UAAU;YAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;YAC1C,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;QAC5D;IACF", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/instrumentation.ts"], "sourcesContent": ["import { init } from '@sentry/nextjs';\nimport { keys } from './keys';\n\nconst opts = {\n  dsn: keys().NEXT_PUBLIC_SENTRY_DSN,\n};\n\nexport const initializeSentry = () => {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    init(opts);\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    init(opts);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,OAAO;IACX,KAAK,CAAA,GAAA,sIAAA,CAAA,OAAI,AAAD,IAAI,sBAAsB;AACpC;AAEO,MAAM,mBAAmB;IAC9B,wCAA2C;QACzC,CAAA,GAAA,oRAAA,CAAA,OAAI,AAAD,EAAE;IACP;IAEA,uCAAyC;;IAEzC;AACF", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/instrumentation.ts"], "sourcesContent": ["import { initializeSentry } from '@repo/observability/instrumentation';\n\nexport const register = initializeSentry();\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD", "debugId": null}}]}