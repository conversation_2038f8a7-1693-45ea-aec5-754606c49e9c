{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "util.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/config/util.ts"], "sourcesContent": ["import * as fs from 'fs';\nimport { sync as resolveSync } from 'resolve';\n\n/**\n * Returns the version of Next.js installed in the project, or undefined if it cannot be determined.\n */\nexport function getNextjsVersion(): string | undefined {\n  const nextjsPackageJsonPath = resolveNextjsPackageJson();\n  if (nextjsPackageJsonPath) {\n    try {\n      const nextjsPackageJson: { version: string } = JSON.parse(\n        fs.readFileSync(nextjsPackageJsonPath, { encoding: 'utf-8' }),\n      );\n      return nextjsPackageJson.version;\n    } catch {\n      // noop\n    }\n  }\n\n  return undefined;\n}\n\nfunction resolveNextjsPackageJson(): string | undefined {\n  try {\n    return resolveSync('next/package.json', { basedir: process.cwd() });\n  } catch {\n    return undefined;\n  }\n}\n"], "names": ["resolveSync"], "mappings": ";;;;;AAGA;;CAEA,GACO,SAAS,gBAAgB,GAAuB;IACrD,MAAM,qBAAA,GAAwB,wBAAwB,EAAE;IACxD,IAAI,qBAAqB,EAAE;QACzB,IAAI;YACF,MAAM,iBAAiB,GAAwB,IAAI,CAAC,KAAK,CACvD,EAAE,CAAC,YAAY,CAAC,qBAAqB,EAAE;gBAAE,QAAQ,EAAE,OAAQ;YAAA,CAAC,CAAC;YAE/D,OAAO,iBAAiB,CAAC,OAAO;QACtC,EAAM,OAAM;QACZ,OAAA;QACA;IACA;IAEE,OAAO,SAAS;AAClB;AAEA,SAAS,wBAAwB,GAAuB;IACtD,IAAI;QACF,OAAOA,QAAAA,IAAW,CAAC,mBAAmB,EAAE;YAAE,OAAO,EAAE,OAAO,CAAC,GAAG,EAAC;QAAA,CAAG,CAAC;IACvE,EAAI,OAAM;QACN,OAAO,SAAS;IACpB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "file": "webpackPluginOptions.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/config/webpackPluginOptions.ts"], "sourcesContent": ["import type { SentryWebpackPluginOptions } from '@sentry/webpack-plugin';\nimport * as path from 'path';\nimport type { BuildContext, NextConfigObject, SentryBuildOptions } from './types';\n\n/**\n * Combine default and user-provided SentryWebpackPlugin options, accounting for whether we're building server files or\n * client files.\n */\nexport function getWebpackPluginOptions(\n  buildContext: BuildContext,\n  sentryBuildOptions: SentryBuildOptions,\n  releaseName: string | undefined,\n): SentryWebpackPluginOptions {\n  const { isServer, config: userNextConfig, dir, nextRuntime } = buildContext;\n\n  const prefixInsert = !isServer ? 'Client' : nextRuntime === 'edge' ? 'Edge' : 'Node.js';\n\n  // We need to convert paths to posix because Glob patterns use `\\` to escape\n  // glob characters. This clashes with Windows path separators.\n  // See: https://www.npmjs.com/package/glob\n  const projectDir = dir.replace(/\\\\/g, '/');\n  // `.next` is the default directory\n  const distDir = (userNextConfig as NextConfigObject).distDir?.replace(/\\\\/g, '/') ?? '.next';\n  const distDirAbsPath = path.posix.join(projectDir, distDir);\n\n  const sourcemapUploadAssets: string[] = [];\n  const sourcemapUploadIgnore: string[] = [];\n\n  if (isServer) {\n    sourcemapUploadAssets.push(\n      path.posix.join(distDirAbsPath, 'server', '**'), // This is normally where Next.js outputs things\n      path.posix.join(distDirAbsPath, 'serverless', '**'), // This was the output location for serverless Next.js\n    );\n  } else {\n    if (sentryBuildOptions.widenClientFileUpload) {\n      sourcemapUploadAssets.push(path.posix.join(distDirAbsPath, 'static', 'chunks', '**'));\n    } else {\n      sourcemapUploadAssets.push(\n        path.posix.join(distDirAbsPath, 'static', 'chunks', 'pages', '**'),\n        path.posix.join(distDirAbsPath, 'static', 'chunks', 'app', '**'),\n      );\n    }\n\n    // TODO: We should think about uploading these when `widenClientFileUpload` is `true`. They may be useful in some situations.\n    sourcemapUploadIgnore.push(\n      path.posix.join(distDirAbsPath, 'static', 'chunks', 'framework-*'),\n      path.posix.join(distDirAbsPath, 'static', 'chunks', 'framework.*'),\n      path.posix.join(distDirAbsPath, 'static', 'chunks', 'main-*'),\n      path.posix.join(distDirAbsPath, 'static', 'chunks', 'polyfills-*'),\n      path.posix.join(distDirAbsPath, 'static', 'chunks', 'webpack-*'),\n    );\n  }\n\n  return {\n    authToken: sentryBuildOptions.authToken,\n    headers: sentryBuildOptions.headers,\n    org: sentryBuildOptions.org,\n    project: sentryBuildOptions.project,\n    telemetry: sentryBuildOptions.telemetry,\n    debug: sentryBuildOptions.debug,\n    reactComponentAnnotation: {\n      ...sentryBuildOptions.reactComponentAnnotation,\n      ...sentryBuildOptions.unstable_sentryWebpackPluginOptions?.reactComponentAnnotation,\n    },\n    silent: sentryBuildOptions.silent,\n    url: sentryBuildOptions.sentryUrl,\n    sourcemaps: {\n      disable: sentryBuildOptions.sourcemaps?.disable,\n      rewriteSources(source) {\n        if (source.startsWith('webpack://_N_E/')) {\n          return source.replace('webpack://_N_E/', '');\n        } else if (source.startsWith('webpack://')) {\n          return source.replace('webpack://', '');\n        } else {\n          return source;\n        }\n      },\n      assets: sentryBuildOptions.sourcemaps?.assets ?? sourcemapUploadAssets,\n      ignore: sentryBuildOptions.sourcemaps?.ignore ?? sourcemapUploadIgnore,\n      filesToDeleteAfterUpload: sentryBuildOptions.sourcemaps?.deleteSourcemapsAfterUpload\n        ? [\n            // We only care to delete client bundle source maps because they would be the ones being served.\n            // Removing the server source maps crashes Vercel builds for (thus far) unknown reasons:\n            // https://github.com/getsentry/sentry-javascript/issues/13099\n            path.posix.join(distDirAbsPath, 'static', '**', '*.js.map'),\n            path.posix.join(distDirAbsPath, 'static', '**', '*.mjs.map'),\n            path.posix.join(distDirAbsPath, 'static', '**', '*.cjs.map'),\n          ]\n        : undefined,\n      ...sentryBuildOptions.unstable_sentryWebpackPluginOptions?.sourcemaps,\n    },\n    release:\n      releaseName !== undefined\n        ? {\n            inject: false, // The webpack plugin's release injection breaks the `app` directory - we inject the release manually with the value injection loader instead.\n            name: releaseName,\n            create: sentryBuildOptions.release?.create,\n            finalize: sentryBuildOptions.release?.finalize,\n            dist: sentryBuildOptions.release?.dist,\n            vcsRemote: sentryBuildOptions.release?.vcsRemote,\n            setCommits: sentryBuildOptions.release?.setCommits,\n            deploy: sentryBuildOptions.release?.deploy,\n            ...sentryBuildOptions.unstable_sentryWebpackPluginOptions?.release,\n          }\n        : {\n            inject: false,\n            create: false,\n            finalize: false,\n          },\n    bundleSizeOptimizations: {\n      ...sentryBuildOptions.bundleSizeOptimizations,\n    },\n    _metaOptions: {\n      loggerPrefixOverride: `[@sentry/nextjs - ${prefixInsert}]`,\n      telemetry: {\n        metaFramework: 'nextjs',\n      },\n    },\n    ...sentryBuildOptions.unstable_sentryWebpackPluginOptions,\n  };\n}\n"], "names": [], "mappings": ";;;;AAIA;;;CAGA,GACO,SAAS,uBAAuB,CACrC,YAAY,EACZ,kBAAkB,EAClB,WAAW;IAEX,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,EAAE,WAAY,EAAA,GAAI,YAAY;IAE3E,MAAM,YAAA,GAAe,CAAC,WAAW,QAAA,GAAW,WAAA,KAAgB,MAAA,GAAS,MAAA,GAAS,SAAS;IAEzF,4EAAA;IACA,8DAAA;IACA,0CAAA;IACE,MAAM,UAAW,GAAE,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAC5C,mCAAA;IACE,MAAM,OAAQ,GAAE,AAAC,cAAA,CAAoC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAA,IAAK,OAAO;IAC5F,MAAM,cAAA,GAAiB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;IAE3D,MAAM,qBAAqB,GAAa,EAAE;IAC1C,MAAM,qBAAqB,GAAa,EAAE;IAE1C,IAAI,QAAQ,EAAE;QACZ,qBAAqB,CAAC,IAAI,CACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,EAC/C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC;IAEzD,OAAS;QACL,IAAI,kBAAkB,CAAC,qBAAqB,EAAE;YAC5C,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC3F,OAAW;YACL,qBAAqB,CAAC,IAAI,CACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,EAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;QAExE;QAEA,6HAAA;QACI,qBAAqB,CAAC,IAAI,CACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,EAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,EAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,EAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC;IAEtE;IAEE,OAAO;QACL,SAAS,EAAE,kBAAkB,CAAC,SAAS;QACvC,OAAO,EAAE,kBAAkB,CAAC,OAAO;QACnC,GAAG,EAAE,kBAAkB,CAAC,GAAG;QAC3B,OAAO,EAAE,kBAAkB,CAAC,OAAO;QACnC,SAAS,EAAE,kBAAkB,CAAC,SAAS;QACvC,KAAK,EAAE,kBAAkB,CAAC,KAAK;QAC/B,wBAAwB,EAAE;YACxB,GAAG,kBAAkB,CAAC,wBAAwB;YAC9C,GAAG,kBAAkB,CAAC,mCAAmC,EAAE,wBAAwB;QACzF,CAAK;QACD,MAAM,EAAE,kBAAkB,CAAC,MAAM;QACjC,GAAG,EAAE,kBAAkB,CAAC,SAAS;QACjC,UAAU,EAAE;YACV,OAAO,EAAE,kBAAkB,CAAC,UAAU,EAAE,OAAO;YAC/C,cAAc,EAAC,MAAM,EAAE;gBACrB,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;oBACxC,OAAO,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBACtD,CAAQ,MAAO,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;oBAC1C,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;gBACjD,OAAe;oBACL,OAAO,MAAM;gBACvB;YACA,CAAO;YACD,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,MAAA,IAAU,qBAAqB;YACtE,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,MAAA,IAAU,qBAAqB;YACtE,wBAAwB,EAAE,kBAAkB,CAAC,UAAU,EAAE,8BACrD;gBACV,gGAAA;gBACA,wFAAA;gBACA,8DAAA;gBACY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC;gBAC3D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC;gBAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC;aACxE,GACU,SAAS;YACb,GAAG,kBAAkB,CAAC,mCAAmC,EAAE,UAAU;QAC3E,CAAK;QACD,OAAO,EACL,gBAAgB,YACZ;YACE,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,kBAAkB,CAAC,OAAO,EAAE,MAAM;YAC1C,QAAQ,EAAE,kBAAkB,CAAC,OAAO,EAAE,QAAQ;YAC9C,IAAI,EAAE,kBAAkB,CAAC,OAAO,EAAE,IAAI;YACtC,SAAS,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS;YAChD,UAAU,EAAE,kBAAkB,CAAC,OAAO,EAAE,UAAU;YAClD,MAAM,EAAE,kBAAkB,CAAC,OAAO,EAAE,MAAM;YAC1C,GAAG,kBAAkB,CAAC,mCAAmC,EAAE,OAAO;QAC9E,IACU;YACE,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,KAAK;QAC3B,CAAW;QACP,uBAAuB,EAAE;YACvB,GAAG,kBAAkB,CAAC,uBAAuB;QACnD,CAAK;QACD,YAAY,EAAE;YACZ,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC,CAAC;YAC1D,SAAS,EAAE;gBACT,aAAa,EAAE,QAAQ;YAC/B,CAAO;QACP,CAAK;QACD,GAAG,kBAAkB,CAAC,mCAAmC;IAC7D,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "file": "webpack.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/config/webpack.ts"], "sourcesContent": ["/* eslint-disable complexity */\n/* eslint-disable max-lines */\n\nimport { escapeStringForRegex, loadModule, logger, parseSemver } from '@sentry/core';\nimport * as chalk from 'chalk';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { sync as resolveSync } from 'resolve';\nimport type { VercelCronsConfig } from '../common/types';\n// Note: If you need to import a type from Webpack, do it in `types.ts` and export it from there. Otherwise, our\n// circular dependency check thinks this file is importing from itself. See https://github.com/pahen/madge/issues/306.\nimport type {\n  BuildContext,\n  EntryPropertyObject,\n  IgnoreWarningsOption,\n  NextConfigObject,\n  SentryBuildOptions,\n  WebpackConfigFunction,\n  WebpackConfigObject,\n  WebpackConfigObjectWithModuleRules,\n  WebpackEntryProperty,\n} from './types';\nimport { getNextjsVersion } from './util';\nimport { getWebpackPluginOptions } from './webpackPluginOptions';\n\n// Next.js runs webpack 3 times, once for the client, the server, and for edge. Because we don't want to print certain\n// warnings 3 times, we keep track of them here.\nlet showedMissingGlobalErrorWarningMsg = false;\n\n/**\n * Construct the function which will be used as the nextjs config's `webpack` value.\n *\n * Sets:\n *   - `devtool`, to ensure high-quality sourcemaps are generated\n *   - `entry`, to include user's sentry config files (where `Sentry.init` is called) in the build\n *   - `plugins`, to add SentryWebpackPlugin\n *\n * @param userNextConfig The user's existing nextjs config, as passed to `withSentryConfig`\n * @param userSentryOptions The user's SentryWebpackPlugin config, as passed to `withSentryConfig`\n * @returns The function to set as the nextjs config's `webpack` value\n */\nexport function constructWebpackConfigFunction(\n  userNextConfig: NextConfigObject = {},\n  userSentryOptions: SentryBuildOptions = {},\n  releaseName: string | undefined,\n): WebpackConfigFunction {\n  // Will be called by nextjs and passed its default webpack configuration and context data about the build (whether\n  // we're building server or client, whether we're in dev, what version of webpack we're using, etc). Note that\n  // `incomingConfig` and `buildContext` are referred to as `config` and `options` in the nextjs docs.\n  return function newWebpackFunction(\n    incomingConfig: WebpackConfigObject,\n    buildContext: BuildContext,\n  ): WebpackConfigObject {\n    const { isServer, dev: isDev, dir: projectDir } = buildContext;\n    const runtime = isServer ? (buildContext.nextRuntime === 'edge' ? 'edge' : 'server') : 'client';\n    // Default page extensions per https://github.com/vercel/next.js/blob/f1dbc9260d48c7995f6c52f8fbcc65f08e627992/packages/next/server/config-shared.ts#L161\n    const pageExtensions = userNextConfig.pageExtensions || ['tsx', 'ts', 'jsx', 'js'];\n    const dotPrefixedPageExtensions = pageExtensions.map(ext => `.${ext}`);\n    const pageExtensionRegex = pageExtensions.map(escapeStringForRegex).join('|');\n\n    // We add `.ts` and `.js` back in because `pageExtensions` might not be relevant to the instrumentation file\n    // e.g. user's setting `.mdx`. In that case we still want to default look up\n    // `instrumentation.ts` and `instrumentation.js`\n    const instrumentationFile = getInstrumentationFile(projectDir, dotPrefixedPageExtensions.concat(['.ts', '.js']));\n\n    if (runtime !== 'client') {\n      warnAboutDeprecatedConfigFiles(projectDir, instrumentationFile, runtime);\n    }\n    if (runtime === 'server') {\n      const nextJsVersion = getNextjsVersion();\n      const { major } = parseSemver(nextJsVersion || '');\n      // was added in v15 (https://github.com/vercel/next.js/pull/67539)\n      if (major && major >= 15) {\n        warnAboutMissingOnRequestErrorHandler(instrumentationFile);\n      }\n    }\n\n    let rawNewConfig = { ...incomingConfig };\n\n    // if user has custom webpack config (which always takes the form of a function), run it so we have actual values to\n    // work with\n    if ('webpack' in userNextConfig && typeof userNextConfig.webpack === 'function') {\n      rawNewConfig = userNextConfig.webpack(rawNewConfig, buildContext);\n    }\n\n    // This mutates `rawNewConfig` in place, but also returns it in order to switch its type to one in which\n    // `newConfig.module.rules` is required, so we don't have to keep asserting its existence\n    const newConfig = setUpModuleRules(rawNewConfig);\n\n    // Add a loader which will inject code that sets global values\n    addValueInjectionLoader(newConfig, userNextConfig, userSentryOptions, buildContext, releaseName);\n\n    addOtelWarningIgnoreRule(newConfig);\n\n    let pagesDirPath: string | undefined;\n    const maybePagesDirPath = path.join(projectDir, 'pages');\n    const maybeSrcPagesDirPath = path.join(projectDir, 'src', 'pages');\n    if (fs.existsSync(maybePagesDirPath) && fs.lstatSync(maybePagesDirPath).isDirectory()) {\n      pagesDirPath = maybePagesDirPath;\n    } else if (fs.existsSync(maybeSrcPagesDirPath) && fs.lstatSync(maybeSrcPagesDirPath).isDirectory()) {\n      pagesDirPath = maybeSrcPagesDirPath;\n    }\n\n    let appDirPath: string | undefined;\n    const maybeAppDirPath = path.join(projectDir, 'app');\n    const maybeSrcAppDirPath = path.join(projectDir, 'src', 'app');\n    if (fs.existsSync(maybeAppDirPath) && fs.lstatSync(maybeAppDirPath).isDirectory()) {\n      appDirPath = maybeAppDirPath;\n    } else if (fs.existsSync(maybeSrcAppDirPath) && fs.lstatSync(maybeSrcAppDirPath).isDirectory()) {\n      appDirPath = maybeSrcAppDirPath;\n    }\n\n    const apiRoutesPath = pagesDirPath ? path.join(pagesDirPath, 'api') : undefined;\n\n    const middlewareLocationFolder = pagesDirPath\n      ? path.join(pagesDirPath, '..')\n      : appDirPath\n        ? path.join(appDirPath, '..')\n        : projectDir;\n\n    const staticWrappingLoaderOptions = {\n      appDir: appDirPath,\n      pagesDir: pagesDirPath,\n      pageExtensionRegex,\n      excludeServerRoutes: userSentryOptions.excludeServerRoutes,\n      nextjsRequestAsyncStorageModulePath: getRequestAsyncStorageModuleLocation(\n        projectDir,\n        rawNewConfig.resolve?.modules,\n      ),\n    };\n\n    const normalizeLoaderResourcePath = (resourcePath: string): string => {\n      // `resourcePath` may be an absolute path or a path relative to the context of the webpack config\n      let absoluteResourcePath: string;\n      if (path.isAbsolute(resourcePath)) {\n        absoluteResourcePath = resourcePath;\n      } else {\n        absoluteResourcePath = path.join(projectDir, resourcePath);\n      }\n\n      return path.normalize(absoluteResourcePath);\n    };\n\n    const isPageResource = (resourcePath: string): boolean => {\n      const normalizedAbsoluteResourcePath = normalizeLoaderResourcePath(resourcePath);\n      return (\n        pagesDirPath !== undefined &&\n        normalizedAbsoluteResourcePath.startsWith(pagesDirPath + path.sep) &&\n        !normalizedAbsoluteResourcePath.startsWith(apiRoutesPath + path.sep) &&\n        dotPrefixedPageExtensions.some(ext => normalizedAbsoluteResourcePath.endsWith(ext))\n      );\n    };\n\n    const isApiRouteResource = (resourcePath: string): boolean => {\n      const normalizedAbsoluteResourcePath = normalizeLoaderResourcePath(resourcePath);\n      return (\n        normalizedAbsoluteResourcePath.startsWith(apiRoutesPath + path.sep) &&\n        dotPrefixedPageExtensions.some(ext => normalizedAbsoluteResourcePath.endsWith(ext))\n      );\n    };\n\n    const possibleMiddlewareLocations = pageExtensions.map(middlewareFileEnding => {\n      return path.join(middlewareLocationFolder, `middleware.${middlewareFileEnding}`);\n    });\n    const isMiddlewareResource = (resourcePath: string): boolean => {\n      const normalizedAbsoluteResourcePath = normalizeLoaderResourcePath(resourcePath);\n      return possibleMiddlewareLocations.includes(normalizedAbsoluteResourcePath);\n    };\n\n    const isServerComponentResource = (resourcePath: string): boolean => {\n      const normalizedAbsoluteResourcePath = normalizeLoaderResourcePath(resourcePath);\n\n      // \".js, .jsx, or .tsx file extensions can be used for Pages\"\n      // https://beta.nextjs.org/docs/routing/pages-and-layouts#pages:~:text=.js%2C%20.jsx%2C%20or%20.tsx%20file%20extensions%20can%20be%20used%20for%20Pages.\n      return (\n        appDirPath !== undefined &&\n        normalizedAbsoluteResourcePath.startsWith(appDirPath + path.sep) &&\n        !!normalizedAbsoluteResourcePath.match(\n          // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n          new RegExp(`[\\\\\\\\/](page|layout|loading|head|not-found)\\\\.(${pageExtensionRegex})$`),\n        )\n      );\n    };\n\n    const isRouteHandlerResource = (resourcePath: string): boolean => {\n      const normalizedAbsoluteResourcePath = normalizeLoaderResourcePath(resourcePath);\n      return (\n        appDirPath !== undefined &&\n        normalizedAbsoluteResourcePath.startsWith(appDirPath + path.sep) &&\n        !!normalizedAbsoluteResourcePath.match(\n          // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n          new RegExp(`[\\\\\\\\/]route\\\\.(${pageExtensionRegex})$`),\n        )\n      );\n    };\n\n    if (isServer && userSentryOptions.autoInstrumentServerFunctions !== false) {\n      // It is very important that we insert our loaders at the beginning of the array because we expect any sort of transformations/transpilations (e.g. TS -> JS) to already have happened.\n\n      // Wrap pages\n      newConfig.module.rules.unshift({\n        test: isPageResource,\n        use: [\n          {\n            loader: path.resolve(__dirname, 'loaders', 'wrappingLoader.js'),\n            options: {\n              ...staticWrappingLoaderOptions,\n              wrappingTargetKind: 'page',\n            },\n          },\n        ],\n      });\n\n      let vercelCronsConfig: VercelCronsConfig = undefined;\n      try {\n        if (process.env.VERCEL && userSentryOptions.automaticVercelMonitors) {\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          vercelCronsConfig = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'vercel.json'), 'utf8')).crons;\n          if (vercelCronsConfig) {\n            logger.info(\n              `${chalk.cyan(\n                'info',\n              )} - Creating Sentry cron monitors for your Vercel Cron Jobs. You can disable this feature by setting the ${chalk.bold.cyan(\n                'automaticVercelMonitors',\n              )} option to false in you Next.js config.`,\n            );\n          }\n        }\n      } catch (e) {\n        if ((e as { code: string }).code === 'ENOENT') {\n          // noop if file does not exist\n        } else {\n          // log but noop\n          logger.error(\n            `${chalk.red(\n              'error',\n            )} - Sentry failed to read vercel.json for automatic cron job monitoring instrumentation`,\n            e,\n          );\n        }\n      }\n\n      // Wrap api routes\n      newConfig.module.rules.unshift({\n        test: isApiRouteResource,\n        use: [\n          {\n            loader: path.resolve(__dirname, 'loaders', 'wrappingLoader.js'),\n            options: {\n              ...staticWrappingLoaderOptions,\n              vercelCronsConfig,\n              wrappingTargetKind: 'api-route',\n            },\n          },\n        ],\n      });\n\n      // Wrap middleware\n      if (userSentryOptions.autoInstrumentMiddleware ?? true) {\n        newConfig.module.rules.unshift({\n          test: isMiddlewareResource,\n          use: [\n            {\n              loader: path.resolve(__dirname, 'loaders', 'wrappingLoader.js'),\n              options: {\n                ...staticWrappingLoaderOptions,\n                wrappingTargetKind: 'middleware',\n              },\n            },\n          ],\n        });\n      }\n    }\n\n    if (isServer && userSentryOptions.autoInstrumentAppDirectory !== false) {\n      // Wrap server components\n      newConfig.module.rules.unshift({\n        test: isServerComponentResource,\n        use: [\n          {\n            loader: path.resolve(__dirname, 'loaders', 'wrappingLoader.js'),\n            options: {\n              ...staticWrappingLoaderOptions,\n              wrappingTargetKind: 'server-component',\n            },\n          },\n        ],\n      });\n\n      // Wrap route handlers\n      newConfig.module.rules.unshift({\n        test: isRouteHandlerResource,\n        use: [\n          {\n            loader: path.resolve(__dirname, 'loaders', 'wrappingLoader.js'),\n            options: {\n              ...staticWrappingLoaderOptions,\n              wrappingTargetKind: 'route-handler',\n            },\n          },\n        ],\n      });\n    }\n\n    if (appDirPath) {\n      const hasGlobalErrorFile = pageExtensions\n        .map(extension => `global-error.${extension}`)\n        .some(\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          globalErrorFile => fs.existsSync(path.join(appDirPath!, globalErrorFile)),\n        );\n\n      if (\n        !hasGlobalErrorFile &&\n        !showedMissingGlobalErrorWarningMsg &&\n        !process.env.SENTRY_SUPPRESS_GLOBAL_ERROR_HANDLER_FILE_WARNING\n      ) {\n        // eslint-disable-next-line no-console\n        console.log(\n          `${chalk.yellow(\n            'warn',\n          )}  - It seems like you don't have a global error handler set up. It is recommended that you add a ${chalk.cyan(\n            'global-error.js',\n          )} file with Sentry instrumentation so that React rendering errors are reported to Sentry. Read more: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#react-render-errors-in-app-router (you can suppress this warning by setting SENTRY_SUPPRESS_GLOBAL_ERROR_HANDLER_FILE_WARNING=1 as environment variable)`,\n        );\n        showedMissingGlobalErrorWarningMsg = true;\n      }\n    }\n\n    if (!isServer) {\n      // Tell webpack to inject the client config files (containing the client-side `Sentry.init()` call) into the appropriate output\n      // bundles. Store a separate reference to the original `entry` value to avoid an infinite loop. (If we don't do\n      // this, we'll have a statement of the form `x.y = () => f(x.y)`, where one of the things `f` does is call `x.y`.\n      // Since we're setting `x.y` to be a callback (which, by definition, won't run until some time later), by the time\n      // the function runs (causing `f` to run, causing `x.y` to run), `x.y` will point to the callback itself, rather\n      // than its original value. So calling it will call the callback which will call `f` which will call `x.y` which\n      // will call the callback which will call `f` which will call `x.y`... and on and on. Theoretically this could also\n      // be fixed by using `bind`, but this is way simpler.)\n      const origEntryProperty = newConfig.entry;\n      newConfig.entry = async () => addSentryToClientEntryProperty(origEntryProperty, buildContext);\n\n      const clientSentryConfigFileName = getClientSentryConfigFile(projectDir);\n      if (clientSentryConfigFileName) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `[@sentry/nextjs] DEPRECATION WARNING: It is recommended renaming your \\`${clientSentryConfigFileName}\\` file, or moving its content to \\`instrumentation-client.ts\\`. When using Turbopack \\`${clientSentryConfigFileName}\\` will no longer work. Read more about the \\`instrumentation-client.ts\\` file: https://nextjs.org/docs/app/api-reference/file-conventions/instrumentation-client`,\n        );\n      }\n    }\n\n    const isStaticExport = userNextConfig?.output === 'export';\n\n    // We don't want to do any webpack plugin stuff OR any source maps stuff in dev mode or for the server on static-only builds.\n    // Symbolication for dev-mode errors is done elsewhere.\n    if (!(isDev || (isStaticExport && isServer))) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const { sentryWebpackPlugin } = loadModule<{ sentryWebpackPlugin: any }>('@sentry/webpack-plugin', module) ?? {};\n\n      if (sentryWebpackPlugin) {\n        if (!userSentryOptions.sourcemaps?.disable) {\n          // Source maps can be configured in 3 ways:\n          // 1. (next config): productionBrowserSourceMaps\n          // 2. (next config): experimental.serverSourceMaps\n          // 3. custom webpack configuration\n          //\n          // We only update this if no explicit value is set\n          // (Next.js defaults to `false`: https://github.com/vercel/next.js/blob/5f4f96c133bd6b10954812cc2fef6af085b82aa5/packages/next/src/build/webpack/config/blocks/base.ts#L61)\n          if (!newConfig.devtool) {\n            logger.info(`[@sentry/nextjs] Automatically enabling source map generation for ${runtime} build.`);\n            // `hidden-source-map` produces the same sourcemaps as `source-map`, but doesn't include the `sourceMappingURL`\n            // comment at the bottom. For folks who aren't publicly hosting their sourcemaps, this is helpful because then\n            // the browser won't look for them and throw errors into the console when it can't find them. Because this is a\n            // front-end-only problem, and because `sentry-cli` handles sourcemaps more reliably with the comment than\n            // without, the option to use `hidden-source-map` only applies to the client-side build.\n            if (isServer) {\n              newConfig.devtool = 'source-map';\n            } else {\n              newConfig.devtool = 'hidden-source-map';\n            }\n          }\n\n          // enable source map deletion if not explicitly disabled\n          if (!isServer && userSentryOptions.sourcemaps?.deleteSourcemapsAfterUpload === undefined) {\n            logger.warn(\n              '[@sentry/nextjs] Source maps will be automatically deleted after being uploaded to Sentry. If you want to keep the source maps, set the `sourcemaps.deleteSourcemapsAfterUpload` option to false in `withSentryConfig()`. If you do not want to generate and upload sourcemaps at all, set the `sourcemaps.disable` option to true.',\n            );\n            userSentryOptions.sourcemaps = {\n              ...userSentryOptions.sourcemaps,\n              deleteSourcemapsAfterUpload: true,\n            };\n          }\n        }\n\n        newConfig.plugins = newConfig.plugins || [];\n        const sentryWebpackPluginInstance = sentryWebpackPlugin(\n          getWebpackPluginOptions(buildContext, userSentryOptions, releaseName),\n        );\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        sentryWebpackPluginInstance._name = 'sentry-webpack-plugin'; // For tests and debugging. Serves no other purpose.\n        newConfig.plugins.push(sentryWebpackPluginInstance);\n      }\n    }\n\n    if (userSentryOptions.disableLogger) {\n      newConfig.plugins = newConfig.plugins || [];\n      newConfig.plugins.push(\n        new buildContext.webpack.DefinePlugin({\n          __SENTRY_DEBUG__: false,\n        }),\n      );\n    }\n\n    return newConfig;\n  };\n}\n\n/**\n * Modify the webpack `entry` property so that the code in `sentry.client.config.js` is\n * included in the the necessary bundles.\n *\n * @param currentEntryProperty The value of the property before Sentry code has been injected\n * @param buildContext Object passed by nextjs containing metadata about the build\n * @returns The value which the new `entry` property (which will be a function) will return (TODO: this should return\n * the function, rather than the function's return value)\n */\nasync function addSentryToClientEntryProperty(\n  currentEntryProperty: WebpackEntryProperty,\n  buildContext: BuildContext,\n): Promise<EntryPropertyObject> {\n  // The `entry` entry in a webpack config can be a string, array of strings, object, or function. By default, nextjs\n  // sets it to an async function which returns the promise of an object of string arrays. Because we don't know whether\n  // someone else has come along before us and changed that, we need to check a few things along the way. The one thing\n  // we know is that it won't have gotten *simpler* in form, so we only need to worry about the object and function\n  // options. See https://webpack.js.org/configuration/entry-context/#entry.\n\n  const { dir: projectDir, dev: isDevMode } = buildContext;\n\n  const newEntryProperty =\n    typeof currentEntryProperty === 'function' ? await currentEntryProperty() : { ...currentEntryProperty };\n\n  const clientSentryConfigFileName = getClientSentryConfigFile(projectDir);\n  const instrumentationClientFileName = getInstrumentationClientFile(projectDir);\n\n  const filesToInject = [];\n  if (clientSentryConfigFileName) {\n    // we need to turn the filename into a path so webpack can find it\n    filesToInject.push(`./${clientSentryConfigFileName}`);\n  }\n  if (instrumentationClientFileName) {\n    // we need to turn the filename into a path so webpack can find it\n    filesToInject.push(`./${instrumentationClientFileName}`);\n  }\n\n  // inject into all entry points which might contain user's code\n  for (const entryPointName in newEntryProperty) {\n    if (\n      entryPointName === 'pages/_app' ||\n      // entrypoint for `/app` pages\n      entryPointName === 'main-app'\n    ) {\n      addFilesToWebpackEntryPoint(newEntryProperty, entryPointName, filesToInject, isDevMode);\n    }\n  }\n\n  return newEntryProperty;\n}\n\n/**\n * Gets the content of the user's instrumentation file\n */\nfunction getInstrumentationFile(projectDir: string, dotPrefixedExtensions: string[]): string | null {\n  const paths = dotPrefixedExtensions.flatMap(extension => [\n    ['src', `instrumentation${extension}`],\n    [`instrumentation${extension}`],\n  ]);\n\n  for (const pathSegments of paths) {\n    try {\n      return fs.readFileSync(path.resolve(projectDir, ...pathSegments), { encoding: 'utf-8' });\n    } catch (e) {\n      // no-op\n    }\n  }\n\n  return null;\n}\n\n/**\n * Make sure the instrumentation file has a `onRequestError` Handler\n */\nfunction warnAboutMissingOnRequestErrorHandler(instrumentationFile: string | null): void {\n  if (!instrumentationFile) {\n    if (!process.env.SENTRY_SUPPRESS_INSTRUMENTATION_FILE_WARNING) {\n      // eslint-disable-next-line no-console\n      console.warn(\n        chalk.yellow(\n          '[@sentry/nextjs] Could not find a Next.js instrumentation file. This indicates an incomplete configuration of the Sentry SDK. An instrumentation file is required for the Sentry SDK to be initialized on the server: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#create-initialization-config-files (you can suppress this warning by setting SENTRY_SUPPRESS_INSTRUMENTATION_FILE_WARNING=1 as environment variable)',\n        ),\n      );\n    }\n    return;\n  }\n\n  if (!instrumentationFile.includes('onRequestError')) {\n    // eslint-disable-next-line no-console\n    console.warn(\n      chalk.yellow(\n        '[@sentry/nextjs] Could not find `onRequestError` hook in instrumentation file. This indicates outdated configuration of the Sentry SDK. Use `Sentry.captureRequestError` to instrument the `onRequestError` hook: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#errors-from-nested-react-server-components',\n      ),\n    );\n  }\n}\n\n/**\n * Searches for old `sentry.(server|edge).config.ts` files and Next.js instrumentation hooks and warns if there are \"old\"\n * config files and no signs of them inside the instrumentation hook.\n *\n * @param projectDir The root directory of the project, where config files would be located\n * @param platform Either \"server\" or \"edge\", so that we know which file to look for\n */\nfunction warnAboutDeprecatedConfigFiles(\n  projectDir: string,\n  instrumentationFile: string | null,\n  platform: 'server' | 'edge',\n): void {\n  const hasInstrumentationHookWithIndicationsOfSentry =\n    instrumentationFile &&\n    (instrumentationFile.includes('@sentry/') ||\n      instrumentationFile.match(/sentry\\.(server|edge)\\.config(\\.(ts|js))?/));\n\n  if (hasInstrumentationHookWithIndicationsOfSentry) {\n    return;\n  }\n\n  for (const filename of [`sentry.${platform}.config.ts`, `sentry.${platform}.config.js`]) {\n    if (fs.existsSync(path.resolve(projectDir, filename))) {\n      // eslint-disable-next-line no-console\n      console.warn(\n        `[@sentry/nextjs] It appears you've configured a \\`${filename}\\` file. Please ensure to put this file's content into the \\`register()\\` function of a Next.js instrumentation file instead. To ensure correct functionality of the SDK, \\`Sentry.init\\` must be called inside of an instrumentation file. Learn more about setting up an instrumentation file in Next.js: https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation. You can safely delete the \\`${filename}\\` file afterward.`,\n      );\n    }\n  }\n}\n\n/**\n * Searches for a `sentry.client.config.ts|js` file and returns its file name if it finds one. (ts being prioritized)\n *\n * @param projectDir The root directory of the project, where config files would be located\n */\nfunction getClientSentryConfigFile(projectDir: string): string | void {\n  const possibilities = ['sentry.client.config.ts', 'sentry.client.config.js'];\n\n  for (const filename of possibilities) {\n    if (fs.existsSync(path.resolve(projectDir, filename))) {\n      return filename;\n    }\n  }\n}\n\n/**\n * Searches for a `instrumentation-client.ts|js` file and returns its file name if it finds one. (ts being prioritized)\n *\n * @param projectDir The root directory of the project, where config files would be located\n */\nfunction getInstrumentationClientFile(projectDir: string): string | void {\n  const possibilities = [\n    ['src', 'instrumentation-client.js'],\n    ['src', 'instrumentation-client.ts'],\n    ['instrumentation-client.js'],\n    ['instrumentation-client.ts'],\n  ];\n\n  for (const pathParts of possibilities) {\n    if (fs.existsSync(path.resolve(projectDir, ...pathParts))) {\n      return path.join(...pathParts);\n    }\n  }\n}\n\n/**\n * Add files to a specific element of the given `entry` webpack config property.\n *\n * @param entryProperty The existing `entry` config object\n * @param entryPointName The key where the file should be injected\n * @param filesToInsert An array of paths to the injected files\n */\nfunction addFilesToWebpackEntryPoint(\n  entryProperty: EntryPropertyObject,\n  entryPointName: string,\n  filesToInsert: string[],\n  isDevMode: boolean,\n): void {\n  // BIG FAT NOTE: Order of insertion seems to matter here. If we insert the new files before the `currentEntrypoint`s,\n  // the Next.js dev server breaks. Because we generally still want the SDK to be initialized as early as possible we\n  // still keep it at the start of the entrypoints if we are not in dev mode.\n\n  // can be a string, array of strings, or object whose `import` property is one of those two\n  const currentEntryPoint = entryProperty[entryPointName];\n  let newEntryPoint = currentEntryPoint;\n\n  if (typeof currentEntryPoint === 'string' || Array.isArray(currentEntryPoint)) {\n    newEntryPoint = Array.isArray(currentEntryPoint) ? currentEntryPoint : [currentEntryPoint];\n    if (newEntryPoint.some(entry => filesToInsert.includes(entry))) {\n      return;\n    }\n\n    if (isDevMode) {\n      // Inserting at beginning breaks dev mode so we insert at the end\n      newEntryPoint.push(...filesToInsert);\n    } else {\n      // In other modes we insert at the beginning so that the SDK initializes as early as possible\n      newEntryPoint.unshift(...filesToInsert);\n    }\n  }\n  // descriptor object (webpack 5+)\n  else if (typeof currentEntryPoint === 'object' && 'import' in currentEntryPoint) {\n    const currentImportValue = currentEntryPoint.import;\n    const newImportValue = Array.isArray(currentImportValue) ? currentImportValue : [currentImportValue];\n    if (newImportValue.some(entry => filesToInsert.includes(entry))) {\n      return;\n    }\n\n    if (isDevMode) {\n      // Inserting at beginning breaks dev mode so we insert at the end\n      newImportValue.push(...filesToInsert);\n    } else {\n      // In other modes we insert at the beginning so that the SDK initializes as early as possible\n      newImportValue.unshift(...filesToInsert);\n    }\n\n    newEntryPoint = {\n      ...currentEntryPoint,\n      import: newImportValue,\n    };\n  }\n  // malformed entry point (use `console.error` rather than `logger.error` because it will always be printed, regardless\n  // of SDK settings)\n  else {\n    // eslint-disable-next-line no-console\n    console.error(\n      'Sentry Logger [Error]:',\n      `Could not inject SDK initialization code into entry point ${entryPointName}, as its current value is not in a recognized format.\\n`,\n      'Expected: string | Array<string> | { [key:string]: any, import: string | Array<string> }\\n',\n      `Got: ${currentEntryPoint}`,\n    );\n  }\n\n  if (newEntryPoint) {\n    entryProperty[entryPointName] = newEntryPoint;\n  }\n}\n\n/**\n * Ensure that `newConfig.module.rules` exists. Modifies the given config in place but also returns it in order to\n * change its type.\n *\n * @param newConfig A webpack config object which may or may not contain `module` and `module.rules`\n * @returns The same object, with an empty `module.rules` array added if necessary\n */\nfunction setUpModuleRules(newConfig: WebpackConfigObject): WebpackConfigObjectWithModuleRules {\n  newConfig.module = {\n    ...newConfig.module,\n    rules: [...(newConfig.module?.rules || [])],\n  };\n  // Surprising that we have to assert the type here, since we've demonstrably guaranteed the existence of\n  // `newConfig.module.rules` just above, but ¯\\_(ツ)_/¯\n  return newConfig as WebpackConfigObjectWithModuleRules;\n}\n\n/**\n * Adds loaders to inject values on the global object based on user configuration.\n */\n// TODO: Remove this loader and replace it with a nextConfig.env (https://web.archive.org/web/20240917153554/https://nextjs.org/docs/app/api-reference/next-config-js/env) or define based (https://github.com/vercel/next.js/discussions/71476) approach.\n// In order to remove this loader though we need to make sure the minimum supported Next.js version includes this PR (https://github.com/vercel/next.js/pull/61194), otherwise the nextConfig.env based approach will not work, as our SDK code is not processed by Next.js.\nfunction addValueInjectionLoader(\n  newConfig: WebpackConfigObjectWithModuleRules,\n  userNextConfig: NextConfigObject,\n  userSentryOptions: SentryBuildOptions,\n  buildContext: BuildContext,\n  releaseName: string | undefined,\n): void {\n  const assetPrefix = userNextConfig.assetPrefix || userNextConfig.basePath || '';\n\n  const isomorphicValues = {\n    // `rewritesTunnel` set by the user in Next.js config\n    _sentryRewritesTunnelPath:\n      userSentryOptions.tunnelRoute !== undefined && userNextConfig.output !== 'export'\n        ? `${userNextConfig.basePath ?? ''}${userSentryOptions.tunnelRoute}`\n        : undefined,\n\n    // The webpack plugin's release injection breaks the `app` directory so we inject the release manually here instead.\n    // Having a release defined in dev-mode spams releases in Sentry so we only set one in non-dev mode\n    SENTRY_RELEASE: releaseName && !buildContext.dev ? { id: releaseName } : undefined,\n    _sentryBasePath: buildContext.dev ? userNextConfig.basePath : undefined,\n  };\n\n  const serverValues = {\n    ...isomorphicValues,\n    // Make sure that if we have a windows path, the backslashes are interpreted as such (rather than as escape\n    // characters)\n    _sentryRewriteFramesDistDir: userNextConfig.distDir?.replace(/\\\\/g, '\\\\\\\\') || '.next',\n  };\n\n  const clientValues = {\n    ...isomorphicValues,\n    // Get the path part of `assetPrefix`, minus any trailing slash. (We use a placeholder for the origin if\n    // `assetPrefix` doesn't include one. Since we only care about the path, it doesn't matter what it is.)\n    _sentryRewriteFramesAssetPrefixPath: assetPrefix\n      ? new URL(assetPrefix, 'http://dogs.are.great').pathname.replace(/\\/$/, '')\n      : '',\n    _sentryAssetPrefix: userNextConfig.assetPrefix,\n    _sentryExperimentalThirdPartyOriginStackFrames: userSentryOptions._experimental?.thirdPartyOriginStackFrames\n      ? 'true'\n      : undefined,\n  };\n\n  if (buildContext.isServer) {\n    newConfig.module.rules.push({\n      // TODO: Find a more bulletproof way of matching. For now this is fine and doesn't hurt anyone. It merely sets some globals.\n      test: /(src[\\\\/])?instrumentation.(js|ts)/,\n      use: [\n        {\n          loader: path.resolve(__dirname, 'loaders/valueInjectionLoader.js'),\n          options: {\n            values: serverValues,\n          },\n        },\n      ],\n    });\n  } else {\n    newConfig.module.rules.push({\n      test: /sentry\\.client\\.config\\.(jsx?|tsx?)/,\n      use: [\n        {\n          loader: path.resolve(__dirname, 'loaders/valueInjectionLoader.js'),\n          options: {\n            values: clientValues,\n          },\n        },\n      ],\n    });\n  }\n}\n\nfunction resolveNextPackageDirFromDirectory(basedir: string): string | undefined {\n  try {\n    return path.dirname(resolveSync('next/package.json', { basedir }));\n  } catch {\n    // Should not happen in theory\n    return undefined;\n  }\n}\n\nconst POTENTIAL_REQUEST_ASYNC_STORAGE_LOCATIONS = [\n  // Original location of RequestAsyncStorage\n  // https://github.com/vercel/next.js/blob/46151dd68b417e7850146d00354f89930d10b43b/packages/next/src/client/components/request-async-storage.ts\n  'next/dist/client/components/request-async-storage.js',\n  // Introduced in Next.js 13.4.20\n  // https://github.com/vercel/next.js/blob/e1bc270830f2fc2df3542d4ef4c61b916c802df3/packages/next/src/client/components/request-async-storage.external.ts\n  'next/dist/client/components/request-async-storage.external.js',\n  // Introduced in Next.js 15.0.0-canary.180\n  // https://github.com/vercel/next.js/blob/541167b9b0fed6af9f36472e632863ffec41f18c/packages/next/src/server/app-render/work-unit-async-storage.external.ts\n  'next/dist/server/app-render/work-unit-async-storage.external.js',\n  // Introduced in Next.js 15.0.0-canary.182\n  // https://github.com/vercel/next.js/blob/f35159e5e80138ca7373f57b47edcaae3bcf1728/packages/next/src/client/components/work-unit-async-storage.external.ts\n  'next/dist/client/components/work-unit-async-storage.external.js',\n];\n\nfunction getRequestAsyncStorageModuleLocation(\n  webpackContextDir: string,\n  webpackResolvableModuleLocations: string[] | undefined,\n): string | undefined {\n  if (webpackResolvableModuleLocations === undefined) {\n    return undefined;\n  }\n\n  const absoluteWebpackResolvableModuleLocations = webpackResolvableModuleLocations.map(loc =>\n    path.resolve(webpackContextDir, loc),\n  );\n\n  for (const webpackResolvableLocation of absoluteWebpackResolvableModuleLocations) {\n    const nextPackageDir = resolveNextPackageDirFromDirectory(webpackResolvableLocation);\n    if (nextPackageDir) {\n      const asyncLocalStorageLocation = POTENTIAL_REQUEST_ASYNC_STORAGE_LOCATIONS.find(loc =>\n        fs.existsSync(path.join(nextPackageDir, '..', loc)),\n      );\n      if (asyncLocalStorageLocation) {\n        return asyncLocalStorageLocation;\n      }\n    }\n  }\n\n  return undefined;\n}\n\nfunction addOtelWarningIgnoreRule(newConfig: WebpackConfigObjectWithModuleRules): void {\n  const ignoreRules = [\n    // Inspired by @matmannion: https://github.com/getsentry/sentry-javascript/issues/12077#issuecomment-2180307072\n    (warning, compilation) => {\n      // This is wrapped in try-catch because we are vendoring types for this hook and we can't be 100% sure that we are accessing API that is there\n      try {\n        if (!warning.module) {\n          return false;\n        }\n\n        const isDependencyThatMayRaiseCriticalDependencyMessage =\n          /@opentelemetry\\/instrumentation/.test(warning.module.readableIdentifier(compilation.requestShortener)) ||\n          /@prisma\\/instrumentation/.test(warning.module.readableIdentifier(compilation.requestShortener));\n        const isCriticalDependencyMessage = /Critical dependency/.test(warning.message);\n\n        return isDependencyThatMayRaiseCriticalDependencyMessage && isCriticalDependencyMessage;\n      } catch {\n        return false;\n      }\n    },\n    // We provide these objects in addition to the hook above to provide redundancy in case the hook fails.\n    { module: /@opentelemetry\\/instrumentation/, message: /Critical dependency/ },\n    { module: /@prisma\\/instrumentation/, message: /Critical dependency/ },\n    { module: /require-in-the-middle/, message: /Critical dependency/ },\n  ] satisfies IgnoreWarningsOption;\n\n  if (newConfig.ignoreWarnings === undefined) {\n    newConfig.ignoreWarnings = ignoreRules;\n  } else if (Array.isArray(newConfig.ignoreWarnings)) {\n    newConfig.ignoreWarnings.push(...ignoreRules);\n  }\n}\n"], "names": ["escapeStringForRegex", "getNextjsVersion", "parseSemver", "logger", "loadModule", "getWebpackPluginOptions", "resolveSync"], "mappings": ";;;;;;;;;;AAAA,6BAAA,GACA,4BAAA,GAwBA,sHAAA;AACA,gDAAA;AACA,IAAI,kCAAA,GAAqC,KAAK;AAE9C;;;;;;;;;;;CAWA,GACO,SAAS,8BAA8B,CAC5C,cAAc,GAAqB,CAAA,CAAE,EACrC,iBAAiB,GAAuB,CAAA,CAAE,EAC1C,WAAW;IAEb,kHAAA;IACA,8GAAA;IACA,oGAAA;IACE,OAAO,SAAS,kBAAkB,CAChC,cAAc,EACd,YAAY;QAEZ,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAW,EAAA,GAAI,YAAY;QAC9D,MAAM,OAAQ,GAAE,QAAS,GAAG,YAAY,CAAC,WAAY,KAAI,SAAS,MAAA,GAAS,QAAQ,GAAI,QAAQ;QACnG,yJAAA;QACI,MAAM,cAAA,GAAiB,cAAc,CAAC,cAAe,IAAG;YAAC,KAAK;YAAE,IAAI;YAAE,KAAK;YAAE,IAAI;SAAC;QAClF,MAAM,yBAAA,GAA4B,cAAc,CAAC,GAAG,EAAC,GAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA,CAAA;QACA,MAAA,kBAAA,GAAA,cAAA,CAAA,GAAA,CAAAA,KAAAA,oBAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;QAEA,4GAAA;QACA,4EAAA;QACA,gDAAA;QACA,MAAA,mBAAA,GAAA,sBAAA,CAAA,UAAA,EAAA,yBAAA,CAAA,MAAA,CAAA;YAAA,KAAA;YAAA,KAAA;SAAA,CAAA,CAAA;QAEA,IAAA,OAAA,KAAA,QAAA,EAAA;YACA,8BAAA,CAAA,UAAA,EAAA,mBAAA,EAAA,OAAA,CAAA;QACA;QACA,IAAA,OAAA,KAAA,QAAA,EAAA;YACA,MAAA,aAAA,GAAAC,KAAAA,gBAAA,EAAA;YACA,MAAA,EAAA,KAAA,EAAA,GAAAC,KAAAA,WAAA,CAAA,aAAA,IAAA,EAAA,CAAA;YACA,kEAAA;YACA,IAAA,KAAA,IAAA,KAAA,IAAA,EAAA,EAAA;gBACA,qCAAA,CAAA,mBAAA,CAAA;YACA;QACA;QAEA,IAAA,YAAA,GAAA;YAAA,GAAA,cAAA;QAAA,CAAA;QAEA,oHAAA;QACA,YAAA;QACA,IAAA,SAAA,IAAA,cAAA,IAAA,OAAA,cAAA,CAAA,OAAA,KAAA,UAAA,EAAA;YACA,YAAA,GAAA,cAAA,CAAA,OAAA,CAAA,YAAA,EAAA,YAAA,CAAA;QACA;QAEA,wGAAA;QACA,yFAAA;QACA,MAAA,SAAA,GAAA,gBAAA,CAAA,YAAA,CAAA;QAEA,8DAAA;QACA,uBAAA,CAAA,SAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,WAAA,CAAA;QAEA,wBAAA,CAAA,SAAA,CAAA;QAEA,IAAA,YAAA;QACA,MAAA,iBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA,CAAA;QACA,MAAA,oBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,EAAA,OAAA,CAAA;QACA,IAAA,EAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,SAAA,CAAA,iBAAA,CAAA,CAAA,WAAA,EAAA,EAAA;YACA,YAAA,GAAA,iBAAA;QACA,CAAA,MAAA,IAAA,EAAA,CAAA,UAAA,CAAA,oBAAA,CAAA,IAAA,EAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,CAAA,WAAA,EAAA,EAAA;YACA,YAAA,GAAA,oBAAA;QACA;QAEA,IAAA,UAAA;QACA,MAAA,eAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,CAAA;QACA,MAAA,kBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,EAAA,KAAA,CAAA;QACA,IAAA,EAAA,CAAA,UAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,SAAA,CAAA,eAAA,CAAA,CAAA,WAAA,EAAA,EAAA;YACA,UAAA,GAAA,eAAA;QACA,CAAA,MAAA,IAAA,EAAA,CAAA,UAAA,CAAA,kBAAA,CAAA,IAAA,EAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,CAAA,WAAA,EAAA,EAAA;YACA,UAAA,GAAA,kBAAA;QACA;QAEA,MAAA,aAAA,GAAA,YAAA,GAAA,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA,KAAA,CAAA,GAAA,SAAA;QAEA,MAAA,wBAAA,GAAA,eACA,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA,IAAA,IACA,aACA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,IAAA,IACA,UAAA;QAEA,MAAA,2BAAA,GAAA;YACA,MAAA,EAAA,UAAA;YACA,QAAA,EAAA,YAAA;YACA,kBAAA;YACA,mBAAA,EAAA,iBAAA,CAAA,mBAAA;YACA,mCAAA,EAAA,oCAAA,CACA,UAAA,EACA,YAAA,CAAA,OAAA,EAAA,OAAA;QAEA,CAAA;QAEA,MAAA,2BAAA,GAAA,CAAA,YAAA,KAAA;YACA,iGAAA;YACA,IAAA,oBAAA;YACA,IAAA,IAAA,CAAA,UAAA,CAAA,YAAA,CAAA,EAAA;gBACA,oBAAA,GAAA,YAAA;YACA,CAAA,MAAA;gBACA,oBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,YAAA,CAAA;YACA;YAEA,OAAA,IAAA,CAAA,SAAA,CAAA,oBAAA,CAAA;QACA,CAAA;QAEA,MAAA,cAAA,GAAA,CAAA,YAAA,KAAA;YACA,MAAA,8BAAA,GAAA,2BAAA,CAAA,YAAA,CAAA;YACA,OACA,YAAA,KAAA,SAAA,IACA,8BAAA,CAAA,UAAA,CAAA,YAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IACA,CAAA,8BAAA,CAAA,UAAA,CAAA,aAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IACA,yBAAA,CAAA,IAAA,EAAA,GAAA,GAAA,8BAAA,CAAA,QAAA,CAAA,GAAA,CAAA;QAEA,CAAA;QAEA,MAAA,kBAAA,GAAA,CAAA,YAAA,KAAA;YACA,MAAA,8BAAA,GAAA,2BAAA,CAAA,YAAA,CAAA;YACA,OACA,8BAAA,CAAA,UAAA,CAAA,aAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IACA,yBAAA,CAAA,IAAA,EAAA,GAAA,GAAA,8BAAA,CAAA,QAAA,CAAA,GAAA,CAAA;QAEA,CAAA;QAEA,MAAA,2BAAA,GAAA,cAAA,CAAA,GAAA,EAAA,oBAAA,IAAA;YACA,OAAA,IAAA,CAAA,IAAA,CAAA,wBAAA,EAAA,CAAA,WAAA,EAAA,oBAAA,CAAA,CAAA,CAAA;QACA,CAAA,CAAA;QACA,MAAA,oBAAA,GAAA,CAAA,YAAA,KAAA;YACA,MAAA,8BAAA,GAAA,2BAAA,CAAA,YAAA,CAAA;YACA,OAAA,2BAAA,CAAA,QAAA,CAAA,8BAAA,CAAA;QACA,CAAA;QAEA,MAAA,yBAAA,GAAA,CAAA,YAAA,KAAA;YACA,MAAA,8BAAA,GAAA,2BAAA,CAAA,YAAA,CAAA;YAEA,6DAAA;YACA,wJAAA;YACA,OACA,UAAA,KAAA,SAAA,IACA,8BAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IACA,CAAA,CAAA,8BAAA,CAAA,KAAA,CACA,sEAAA;YACA,IAAA,MAAA,CAAA,CAAA,+CAAA,EAAA,kBAAA,CAAA,EAAA,CAAA,CAAA;QAGA,CAAA;QAEA,MAAA,sBAAA,GAAA,CAAA,YAAA,KAAA;YACA,MAAA,8BAAA,GAAA,2BAAA,CAAA,YAAA,CAAA;YACA,OACA,UAAA,KAAA,SAAA,IACA,8BAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IACA,CAAA,CAAA,8BAAA,CAAA,KAAA,CACA,sEAAA;YACA,IAAA,MAAA,CAAA,CAAA,gBAAA,EAAA,kBAAA,CAAA,EAAA,CAAA,CAAA;QAGA,CAAA;QAEA,IAAA,QAAA,IAAA,iBAAA,CAAA,6BAAA,KAAA,KAAA,EAAA;YACA,uLAAA;YAEA,aAAA;YACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA;gBACA,IAAA,EAAA,cAAA;gBACA,GAAA,EAAA;oBACA;wBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,CAAA;wBACA,OAAA,EAAA;4BACA,GAAA,2BAAA;4BACA,kBAAA,EAAA,MAAA;wBACA,CAAA;oBACA,CAAA;iBACA;YACA,CAAA,CAAA;YAEA,IAAA,iBAAA,GAAA,SAAA;YACA,IAAA;gBACA,IAAA,OAAA,CAAA,GAAA,CAAA,MAAA,IAAA,iBAAA,CAAA,uBAAA,EAAA;oBACA,sEAAA;oBACA,iBAAA,GAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,EAAA,EAAA,aAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA;oBACA,IAAA,iBAAA,EAAA;wBACAC,KAAAA,MAAA,CAAA,IAAA,CACA,CAAA,EAAA,KAAA,CAAA,IAAA,CACA,MAAA,EACA,wGAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CACA,yBAAA,EACA,uCAAA,CAAA;oBAEA;gBACA;YACA,CAAA,CAAA,OAAA,CAAA,EAAA;gBACA,IAAA,CAAA,CAAA,IAAA,KAAA,QAAA,EAAA,CAEA;qBAAA;oBACA,eAAA;oBACAA,KAAAA,MAAA,CAAA,KAAA,CACA,CAAA,EAAA,KAAA,CAAA,GAAA,CACA,OAAA,EACA,sFAAA,CAAA,EACA,CAAA;gBAEA;YACA;YAEA,kBAAA;YACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA;gBACA,IAAA,EAAA,kBAAA;gBACA,GAAA,EAAA;oBACA;wBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,CAAA;wBACA,OAAA,EAAA;4BACA,GAAA,2BAAA;4BACA,iBAAA;4BACA,kBAAA,EAAA,WAAA;wBACA,CAAA;oBACA,CAAA;iBACA;YACA,CAAA,CAAA;YAEA,kBAAA;YACA,IAAA,iBAAA,CAAA,wBAAA,IAAA,IAAA,EAAA;gBACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA;oBACA,IAAA,EAAA,oBAAA;oBACA,GAAA,EAAA;wBACA;4BACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,CAAA;4BACA,OAAA,EAAA;gCACA,GAAA,2BAAA;gCACA,kBAAA,EAAA,YAAA;4BACA,CAAA;wBACA,CAAA;qBACA;gBACA,CAAA,CAAA;YACA;QACA;QAEA,IAAA,QAAA,IAAA,iBAAA,CAAA,0BAAA,KAAA,KAAA,EAAA;YACA,yBAAA;YACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA;gBACA,IAAA,EAAA,yBAAA;gBACA,GAAA,EAAA;oBACA;wBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,CAAA;wBACA,OAAA,EAAA;4BACA,GAAA,2BAAA;4BACA,kBAAA,EAAA,kBAAA;wBACA,CAAA;oBACA,CAAA;iBACA;YACA,CAAA,CAAA;YAEA,sBAAA;YACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA;gBACA,IAAA,EAAA,sBAAA;gBACA,GAAA,EAAA;oBACA;wBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,CAAA;wBACA,OAAA,EAAA;4BACA,GAAA,2BAAA;4BACA,kBAAA,EAAA,eAAA;wBACA,CAAA;oBACA,CAAA;iBACA;YACA,CAAA,CAAA;QACA;QAEA,IAAA,UAAA,EAAA;YACA,MAAA,kBAAA,GAAA,eACA,GAAA,EAAA,SAAA,GAAA,CAAA,aAAA,EAAA,SAAA,CAAA,CAAA,EACA,IAAA,CACA,oEAAA;aACA,eAAA,GAAA,EAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,eAAA,CAAA,CAAA;YAGA,IACA,CAAA,kBAAA,IACA,CAAA,kCAAA,IACA,CAAA,OAAA,CAAA,GAAA,CAAA,iDAAA,EACA;gBACA,sCAAA;gBACA,OAAA,CAAA,GAAA,CACA,CAAA,EAAA,KAAA,CAAA,MAAA,CACA,MAAA,EACA,iGAAA,EAAA,KAAA,CAAA,IAAA,CACA,iBAAA,EACA,oUAAA,CAAA;gBAEA,kCAAA,GAAA,IAAA;YACA;QACA;QAEA,IAAA,CAAA,QAAA,EAAA;YACA,+HAAA;YACA,+GAAA;YACA,iHAAA;YACA,kHAAA;YACA,gHAAA;YACA,gHAAA;YACA,mHAAA;YACA,sDAAA;YACA,MAAA,iBAAA,GAAA,SAAA,CAAA,KAAA;YACA,SAAA,CAAA,KAAA,GAAA,UAAA,8BAAA,CAAA,iBAAA,EAAA,YAAA,CAAA;YAEA,MAAA,0BAAA,GAAA,yBAAA,CAAA,UAAA,CAAA;YACA,IAAA,0BAAA,EAAA;gBACA,sCAAA;gBACA,OAAA,CAAA,IAAA,CACA,CAAA,wEAAA,EAAA,0BAAA,CAAA,wFAAA,EAAA,0BAAA,CAAA,iKAAA,CAAA;YAEA;QACA;QAEA,MAAA,cAAA,GAAA,cAAA,EAAA,MAAA,KAAA,QAAA;QAEA,6HAAA;QACA,uDAAA;QACA,IAAA,CAAA,CAAA,KAAA,IAAA,cAAA,IAAA,QAAA,CAAA,EAAA;YACA,8DAAA;YACA,MAAA,EAAA,mBAAA,EAAA,GAAAC,KAAAA,UAAA,CAAA,wBAAA,EAAA,MAAA,CAAA,IAAA,CAAA,CAAA;YAEA,IAAA,mBAAA,EAAA;gBACA,IAAA,CAAA,iBAAA,CAAA,UAAA,EAAA,OAAA,EAAA;oBACA,2CAAA;oBACA,gDAAA;oBACA,kDAAA;oBACA,kCAAA;oBACA,EAAA;oBACA,kDAAA;oBACA,2KAAA;oBACA,IAAA,CAAA,SAAA,CAAA,OAAA,EAAA;wBACAD,KAAAA,MAAA,CAAA,IAAA,CAAA,CAAA,kEAAA,EAAA,OAAA,CAAA,OAAA,CAAA,CAAA;wBACA,+GAAA;wBACA,8GAAA;wBACA,+GAAA;wBACA,0GAAA;wBACA,wFAAA;wBACA,IAAA,QAAA,EAAA;4BACA,SAAA,CAAA,OAAA,GAAA,YAAA;wBACA,CAAA,MAAA;4BACA,SAAA,CAAA,OAAA,GAAA,mBAAA;wBACA;oBACA;oBAEA,wDAAA;oBACA,IAAA,CAAA,QAAA,IAAA,iBAAA,CAAA,UAAA,EAAA,2BAAA,KAAA,SAAA,EAAA;wBACAA,KAAAA,MAAA,CAAA,IAAA,CACA,qUAAA;wBAEA,iBAAA,CAAA,UAAA,GAAA;4BACA,GAAA,iBAAA,CAAA,UAAA;4BACA,2BAAA,EAAA,IAAA;wBACA,CAAA;oBACA;gBACA;gBAEA,SAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA,IAAA,EAAA;gBACA,MAAA,2BAAA,GAAA,mBAAA,CACAE,qBAAAA,uBAAA,CAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,CAAA;gBAEA,sEAAA;gBACA,2BAAA,CAAA,KAAA,GAAA,uBAAA,CAAA,CAAA,oDAAA;gBACA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,2BAAA,CAAA;YACA;QACA;QAEA,IAAA,iBAAA,CAAA,aAAA,EAAA;YACA,SAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA,IAAA,EAAA;YACA,SAAA,CAAA,OAAA,CAAA,IAAA,CACA,IAAA,YAAA,CAAA,OAAA,CAAA,YAAA,CAAA;gBACA,gBAAA,EAAA,KAAA;YACA,CAAA,CAAA;QAEA;QAEA,OAAA,SAAA;IACA,CAAA;AACA;AAEA;;;;;;;;CAQA,GACA,eAAA,8BAAA,CACA,oBAAA,EACA,YAAA;IAEA,mHAAA;IACA,sHAAA;IACA,qHAAA;IACA,iHAAA;IACA,0EAAA;IAEA,MAAA,EAAA,GAAA,EAAA,UAAA,EAAA,GAAA,EAAA,SAAA,EAAA,GAAA,YAAA;IAEA,MAAA,gBAAA,GACA,OAAA,oBAAA,KAAA,UAAA,GAAA,MAAA,oBAAA,EAAA,GAAA;QAAA,GAAA,oBAAA;IAAA,CAAA;IAEA,MAAA,0BAAA,GAAA,yBAAA,CAAA,UAAA,CAAA;IACA,MAAA,6BAAA,GAAA,4BAAA,CAAA,UAAA,CAAA;IAEA,MAAA,aAAA,GAAA,EAAA;IACA,IAAA,0BAAA,EAAA;QACA,kEAAA;QACA,aAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA,0BAAA,CAAA,CAAA,CAAA;IACA;IACA,IAAA,6BAAA,EAAA;QACA,kEAAA;QACA,aAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA,6BAAA,CAAA,CAAA,CAAA;IACA;IAEA,+DAAA;IACA,IAAA,MAAA,cAAA,IAAA,gBAAA,CAAA;QACA,IACA,cAAA,KAAA,YAAA,IACA,8BAAA;QACA,cAAA,KAAA,YACA;YACA,2BAAA,CAAA,gBAAA,EAAA,cAAA,EAAA,aAAA,EAAA,SAAA,CAAA;QACA;IACA;IAEA,OAAA,gBAAA;AACA;AAEA;;CAEA,GACA,SAAA,sBAAA,CAAA,UAAA,EAAA,qBAAA,EAAA;IACA,MAAA,KAAA,GAAA,qBAAA,CAAA,OAAA,EAAA,SAAA,GAAA;YACA;gBAAA,KAAA;gBAAA,CAAA,eAAA,EAAA,SAAA,CAAA,CAAA;aAAA;YACA;gBAAA,CAAA,eAAA,EAAA,SAAA,CAAA,CAAA;aAAA;SACA,CAAA;IAEA,KAAA,MAAA,YAAA,IAAA,KAAA,CAAA;QACA,IAAA;YACA,OAAA,EAAA,CAAA,YAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,EAAA,GAAA,YAAA,CAAA,EAAA;gBAAA,QAAA,EAAA,OAAA;YAAA,CAAA,CAAA;QACA,CAAA,CAAA,OAAA,CAAA,EAAA;QACA,QAAA;QACA;IACA;IAEA,OAAA,IAAA;AACA;AAEA;;CAEA,GACA,SAAA,qCAAA,CAAA,mBAAA,EAAA;IACA,IAAA,CAAA,mBAAA,EAAA;QACA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,4CAAA,EAAA;YACA,sCAAA;YACA,OAAA,CAAA,IAAA,CACA,KAAA,CAAA,MAAA,CACA,obAAA;QAGA;QACA;IACA;IAEA,IAAA,CAAA,mBAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,EAAA;QACA,sCAAA;QACA,OAAA,CAAA,IAAA,CACA,KAAA,CAAA,MAAA,CACA,sUAAA;IAGA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,8BAAA,CACA,UAAA,EACA,mBAAA,EACA,QAAA;IAEA,MAAA,6CAAA,GACA,mBAAA,IACA,CAAA,mBAAA,CAAA,QAAA,CAAA,UAAA,CAAA,IACA,mBAAA,CAAA,KAAA,CAAA,2CAAA,CAAA,CAAA;IAEA,IAAA,6CAAA,EAAA;QACA;IACA;IAEA,KAAA,MAAA,QAAA,IAAA;QAAA,CAAA,OAAA,EAAA,QAAA,CAAA,UAAA,CAAA;QAAA,CAAA,OAAA,EAAA,QAAA,CAAA,UAAA,CAAA;KAAA,CAAA;QACA,IAAA,EAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA,EAAA;YACA,sCAAA;YACA,OAAA,CAAA,IAAA,CACA,CAAA,kDAAA,EAAA,QAAA,CAAA,0ZAAA,EAAA,QAAA,CAAA,kBAAA,CAAA;QAEA;IACA;AACA;AAEA;;;;CAIA,GACA,SAAA,yBAAA,CAAA,UAAA,EAAA;IACA,MAAA,aAAA,GAAA;QAAA,yBAAA;QAAA,yBAAA;KAAA;IAEA,KAAA,MAAA,QAAA,IAAA,aAAA,CAAA;QACA,IAAA,EAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA,EAAA;YACA,OAAA,QAAA;QACA;IACA;AACA;AAEA;;;;CAIA,GACA,SAAA,4BAAA,CAAA,UAAA,EAAA;IACA,MAAA,aAAA,GAAA;QACA;YAAA,KAAA;YAAA,2BAAA;SAAA;QACA;YAAA,KAAA;YAAA,2BAAA;SAAA;QACA;YAAA,2BAAA;SAAA;QACA;YAAA,2BAAA;SAAA;KACA;IAEA,KAAA,MAAA,SAAA,IAAA,aAAA,CAAA;QACA,IAAA,EAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,EAAA,GAAA,SAAA,CAAA,CAAA,EAAA;YACA,OAAA,IAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA;QACA;IACA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,2BAAA,CACA,aAAA,EACA,cAAA,EACA,aAAA,EACA,SAAA;IAEA,qHAAA;IACA,mHAAA;IACA,2EAAA;IAEA,2FAAA;IACA,MAAA,iBAAA,GAAA,aAAA,CAAA,cAAA,CAAA;IACA,IAAA,aAAA,GAAA,iBAAA;IAEA,IAAA,OAAA,iBAAA,KAAA,QAAA,IAAA,KAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,EAAA;QACA,aAAA,GAAA,KAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,iBAAA,GAAA;YAAA,iBAAA;SAAA;QACA,IAAA,aAAA,CAAA,IAAA,EAAA,KAAA,GAAA,aAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA;YACA;QACA;QAEA,IAAA,SAAA,EAAA;YACA,iEAAA;YACA,aAAA,CAAA,IAAA,CAAA,GAAA,aAAA,CAAA;QACA,CAAA,MAAA;YACA,6FAAA;YACA,aAAA,CAAA,OAAA,CAAA,GAAA,aAAA,CAAA;QACA;IACA,OAEA,IAAA,OAAA,iBAAA,KAAA,QAAA,IAAA,QAAA,IAAA,iBAAA,EAAA;QACA,MAAA,kBAAA,GAAA,iBAAA,CAAA,MAAA;QACA,MAAA,cAAA,GAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,GAAA,kBAAA,GAAA;YAAA,kBAAA;SAAA;QACA,IAAA,cAAA,CAAA,IAAA,EAAA,KAAA,GAAA,aAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA;YACA;QACA;QAEA,IAAA,SAAA,EAAA;YACA,iEAAA;YACA,cAAA,CAAA,IAAA,CAAA,GAAA,aAAA,CAAA;QACA,CAAA,MAAA;YACA,6FAAA;YACA,cAAA,CAAA,OAAA,CAAA,GAAA,aAAA,CAAA;QACA;QAEA,aAAA,GAAA;YACA,GAAA,iBAAA;YACA,MAAA,EAAA,cAAA;QACA,CAAA;IACA,OAGA;QACA,sCAAA;QACA,OAAA,CAAA,KAAA,CACA,wBAAA,EACA,CAAA,0DAAA,EAAA,cAAA,CAAA,uDAAA,CAAA,EACA,4FAAA,EACA,CAAA,KAAA,EAAA,iBAAA,CAAA,CAAA;IAEA;IAEA,IAAA,aAAA,EAAA;QACA,aAAA,CAAA,cAAA,CAAA,GAAA,aAAA;IACA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,gBAAA,CAAA,SAAA,EAAA;IACA,SAAA,CAAA,MAAA,GAAA;QACA,GAAA,SAAA,CAAA,MAAA;QACA,KAAA,EAAA,CAAA;eAAA,SAAA,CAAA,MAAA,EAAA,KAAA,IAAA,EAAA,CAAA;SAAA;IACA,CAAA;IACA,wGAAA;IACA,qDAAA;IACA,OAAA,SAAA;AACA;AAEA;;CAEA,GACA,0PAAA;AACA,4QAAA;AACA,SAAA,uBAAA,CACA,SAAA,EACA,cAAA,EACA,iBAAA,EACA,YAAA,EACA,WAAA;IAEA,MAAA,WAAA,GAAA,cAAA,CAAA,WAAA,IAAA,cAAA,CAAA,QAAA,IAAA,EAAA;IAEA,MAAA,gBAAA,GAAA;QACA,qDAAA;QACA,yBAAA,EACA,iBAAA,CAAA,WAAA,KAAA,SAAA,IAAA,cAAA,CAAA,MAAA,KAAA,WACA,CAAA,EAAA,cAAA,CAAA,QAAA,IAAA,EAAA,CAAA,EAAA,iBAAA,CAAA,WAAA,CAAA,CAAA,GACA,SAAA;QAEA,oHAAA;QACA,mGAAA;QACA,cAAA,EAAA,WAAA,IAAA,CAAA,YAAA,CAAA,GAAA,GAAA;YAAA,EAAA,EAAA,WAAA;QAAA,CAAA,GAAA,SAAA;QACA,eAAA,EAAA,YAAA,CAAA,GAAA,GAAA,cAAA,CAAA,QAAA,GAAA,SAAA;IACA,CAAA;IAEA,MAAA,YAAA,GAAA;QACA,GAAA,gBAAA;QACA,2GAAA;QACA,cAAA;QACA,2BAAA,EAAA,cAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,EAAA,MAAA,CAAA,IAAA,OAAA;IACA,CAAA;IAEA,MAAA,YAAA,GAAA;QACA,GAAA,gBAAA;QACA,wGAAA;QACA,uGAAA;QACA,mCAAA,EAAA,cACA,IAAA,GAAA,CAAA,WAAA,EAAA,uBAAA,CAAA,CAAA,QAAA,CAAA,OAAA,CAAA,KAAA,EAAA,EAAA,IACA,EAAA;QACA,kBAAA,EAAA,cAAA,CAAA,WAAA;QACA,8CAAA,EAAA,iBAAA,CAAA,aAAA,EAAA,8BACA,SACA,SAAA;IACA,CAAA;IAEA,IAAA,YAAA,CAAA,QAAA,EAAA;QACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA;YACA,4HAAA;YACA,IAAA,EAAA,oCAAA;YACA,GAAA,EAAA;gBACA;oBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,iCAAA,CAAA;oBACA,OAAA,EAAA;wBACA,MAAA,EAAA,YAAA;oBACA,CAAA;gBACA,CAAA;aACA;QACA,CAAA,CAAA;IACA,CAAA,MAAA;QACA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA;YACA,IAAA,EAAA,qCAAA;YACA,GAAA,EAAA;gBACA;oBACA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,EAAA,iCAAA,CAAA;oBACA,OAAA,EAAA;wBACA,MAAA,EAAA,YAAA;oBACA,CAAA;gBACA,CAAA;aACA;QACA,CAAA,CAAA;IACA;AACA;AAEA,SAAA,kCAAA,CAAA,OAAA,EAAA;IACA,IAAA;QACA,OAAA,IAAA,CAAA,OAAA,CAAAC,QAAAA,IAAA,CAAA,mBAAA,EAAA;YAAA,OAAA;QAAA,CAAA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA;QACA,8BAAA;QACA,OAAA,SAAA;IACA;AACA;AAEA,MAAA,yCAAA,GAAA;IACA,2CAAA;IACA,+IAAA;IACA,sDAAA;IACA,gCAAA;IACA,wJAAA;IACA,+DAAA;IACA,0CAAA;IACA,0JAAA;IACA,iEAAA;IACA,0CAAA;IACA,0JAAA;IACA,iEAAA;CACA;AAEA,SAAA,oCAAA,CACA,iBAAA,EACA,gCAAA;IAEA,IAAA,gCAAA,KAAA,SAAA,EAAA;QACA,OAAA,SAAA;IACA;IAEA,MAAA,wCAAA,GAAA,gCAAA,CAAA,GAAA,EAAA,GAAA,GACA,IAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,GAAA,CAAA;IAGA,KAAA,MAAA,yBAAA,IAAA,wCAAA,CAAA;QACA,MAAA,cAAA,GAAA,kCAAA,CAAA,yBAAA,CAAA;QACA,IAAA,cAAA,EAAA;YACA,MAAA,yBAAA,GAAA,yCAAA,CAAA,IAAA,EAAA,GAAA,GACA,EAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA,IAAA,EAAA,GAAA,CAAA,CAAA;YAEA,IAAA,yBAAA,EAAA;gBACA,OAAA,yBAAA;YACA;QACA;IACA;IAEA,OAAA,SAAA;AACA;AAEA,SAAA,wBAAA,CAAA,SAAA,EAAA;IACA,MAAA,WAAA,GAAA;QACA,+GAAA;QACA,CAAA,OAAA,EAAA,WAAA,KAAA;YACA,8IAAA;YACA,IAAA;gBACA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA;oBACA,OAAA,KAAA;gBACA;gBAEA,MAAA,iDAAA,GACA,iCAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,CAAA,IACA,0BAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,CAAA;gBACA,MAAA,2BAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;gBAEA,OAAA,iDAAA,IAAA,2BAAA;YACA,CAAA,CAAA,OAAA;gBACA,OAAA,KAAA;YACA;QACA,CAAA;QACA,uGAAA;QACA;YAAA,MAAA,EAAA,iCAAA;YAAA,OAAA,EAAA,qBAAA;QAAA,CAAA;QACA;YAAA,MAAA,EAAA,0BAAA;YAAA,OAAA,EAAA,qBAAA;QAAA,CAAA;QACA;YAAA,MAAA,EAAA,uBAAA;YAAA,OAAA,EAAA,qBAAA;QAAA,CAAA;KACA;IAEA,IAAA,SAAA,CAAA,cAAA,KAAA,SAAA,EAAA;QACA,SAAA,CAAA,cAAA,GAAA,WAAA;IACA,CAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,SAAA,CAAA,cAAA,CAAA,EAAA;QACA,SAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,WAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "file": "withSentryConfig.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/config/withSentryConfig.ts"], "sourcesContent": ["/* eslint-disable max-lines */\n/* eslint-disable complexity */\nimport { isThenable, parseSemver } from '@sentry/core';\nimport { getSentryRelease } from '@sentry/node';\nimport * as childProcess from 'child_process';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport type {\n  ExportedNextConfig as NextConfig,\n  NextConfigFunction,\n  NextConfigObject,\n  SentryBuildOptions,\n} from './types';\nimport { getNextjsVersion } from './util';\nimport { constructWebpackConfigFunction } from './webpack';\n\nlet showedExportModeTunnelWarning = false;\n\n/**\n * Modifies the passed in Next.js configuration with automatic build-time instrumentation and source map upload.\n *\n * @param nextConfig A Next.js configuration object, as usually exported in `next.config.js` or `next.config.mjs`.\n * @param sentryBuildOptions Additional options to configure instrumentation and\n * @returns The modified config to be exported\n */\nexport function withSentryConfig<C>(nextConfig?: C, sentryBuildOptions: SentryBuildOptions = {}): C {\n  const castNextConfig = (nextConfig as NextConfig) || {};\n  if (typeof castNextConfig === 'function') {\n    return function (this: unknown, ...webpackConfigFunctionArgs: unknown[]): ReturnType<NextConfigFunction> {\n      const maybePromiseNextConfig: ReturnType<typeof castNextConfig> = castNextConfig.apply(\n        this,\n        webpackConfigFunctionArgs,\n      );\n\n      if (isThenable(maybePromiseNextConfig)) {\n        return maybePromiseNextConfig.then(promiseResultNextConfig => {\n          return getFinalConfigObject(promiseResultNextConfig, sentryBuildOptions);\n        });\n      }\n\n      return getFinalConfigObject(maybePromiseNextConfig, sentryBuildOptions);\n    } as C;\n  } else {\n    return getFinalConfigObject(castNextConfig, sentryBuildOptions) as C;\n  }\n}\n\n// Modify the materialized object form of the user's next config by deleting the `sentry` property and wrapping the\n// `webpack` property\nfunction getFinalConfigObject(\n  incomingUserNextConfigObject: NextConfigObject,\n  userSentryOptions: SentryBuildOptions,\n): NextConfigObject {\n  const releaseName = userSentryOptions.release?.name ?? getSentryRelease() ?? getGitRevision();\n\n  if (userSentryOptions?.tunnelRoute) {\n    if (incomingUserNextConfigObject.output === 'export') {\n      if (!showedExportModeTunnelWarning) {\n        showedExportModeTunnelWarning = true;\n        // eslint-disable-next-line no-console\n        console.warn(\n          '[@sentry/nextjs] The Sentry Next.js SDK `tunnelRoute` option will not work in combination with Next.js static exports. The `tunnelRoute` option uses serverside features that cannot be accessed in export mode. If you still want to tunnel Sentry events, set up your own tunnel: https://docs.sentry.io/platforms/javascript/troubleshooting/#using-the-tunnel-option',\n        );\n      }\n    } else {\n      setUpTunnelRewriteRules(incomingUserNextConfigObject, userSentryOptions.tunnelRoute);\n    }\n  }\n\n  setUpBuildTimeVariables(incomingUserNextConfigObject, userSentryOptions, releaseName);\n\n  const nextJsVersion = getNextjsVersion();\n\n  // Add the `clientTraceMetadata` experimental option based on Next.js version. The option got introduced in Next.js version 15.0.0 (actually 14.3.0-canary.64).\n  // Adding the option on lower versions will cause Next.js to print nasty warnings we wouldn't confront our users with.\n  if (nextJsVersion) {\n    const { major, minor } = parseSemver(nextJsVersion);\n    if (major !== undefined && minor !== undefined && (major >= 15 || (major === 14 && minor >= 3))) {\n      incomingUserNextConfigObject.experimental = incomingUserNextConfigObject.experimental || {};\n      incomingUserNextConfigObject.experimental.clientTraceMetadata = [\n        'baggage',\n        'sentry-trace',\n        ...(incomingUserNextConfigObject.experimental?.clientTraceMetadata || []),\n      ];\n    }\n  } else {\n    // eslint-disable-next-line no-console\n    console.log(\n      \"[@sentry/nextjs] The Sentry SDK was not able to determine your Next.js version. If you are using Next.js version 15 or greater, please add `experimental.clientTraceMetadata: ['sentry-trace', 'baggage']` to your Next.js config to enable pageload tracing for App Router.\",\n    );\n  }\n\n  // From Next.js version (15.0.0-canary.124) onwards, Next.js does no longer require the `experimental.instrumentationHook` option and will\n  // print a warning when it is set, so we need to conditionally provide it for lower versions.\n  if (nextJsVersion) {\n    const { major, minor, patch, prerelease } = parseSemver(nextJsVersion);\n    const isFullySupportedRelease =\n      major !== undefined &&\n      minor !== undefined &&\n      patch !== undefined &&\n      major >= 15 &&\n      ((minor === 0 && patch === 0 && prerelease === undefined) || minor > 0 || patch > 0);\n    const isSupportedV15Rc =\n      major !== undefined &&\n      minor !== undefined &&\n      patch !== undefined &&\n      prerelease !== undefined &&\n      major === 15 &&\n      minor === 0 &&\n      patch === 0 &&\n      prerelease.startsWith('rc.') &&\n      parseInt(prerelease.split('.')[1] || '', 10) > 0;\n    const isSupportedCanary =\n      minor !== undefined &&\n      patch !== undefined &&\n      prerelease !== undefined &&\n      major === 15 &&\n      minor === 0 &&\n      patch === 0 &&\n      prerelease.startsWith('canary.') &&\n      parseInt(prerelease.split('.')[1] || '', 10) >= 124;\n\n    if (!isFullySupportedRelease && !isSupportedV15Rc && !isSupportedCanary) {\n      if (incomingUserNextConfigObject.experimental?.instrumentationHook === false) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          '[@sentry/nextjs] You turned off the `experimental.instrumentationHook` option. Note that Sentry will not be initialized if you did not set it up inside `instrumentation.(js|ts)`.',\n        );\n      }\n      incomingUserNextConfigObject.experimental = {\n        instrumentationHook: true,\n        ...incomingUserNextConfigObject.experimental,\n      };\n    }\n  } else {\n    // If we cannot detect a Next.js version for whatever reason, the sensible default is to set the `experimental.instrumentationHook`, even though it may create a warning.\n    if (\n      incomingUserNextConfigObject.experimental &&\n      'instrumentationHook' in incomingUserNextConfigObject.experimental\n    ) {\n      if (incomingUserNextConfigObject.experimental.instrumentationHook === false) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          '[@sentry/nextjs] You set `experimental.instrumentationHook` to `false`. If you are using Next.js version 15 or greater, you can remove that option. If you are using Next.js version 14 or lower, you need to set `experimental.instrumentationHook` in your `next.config.(js|mjs)` to `true` for the SDK to be properly initialized in combination with `instrumentation.(js|ts)`.',\n        );\n      }\n    } else {\n      // eslint-disable-next-line no-console\n      console.log(\n        \"[@sentry/nextjs] The Sentry SDK was not able to determine your Next.js version. If you are using Next.js version 15 or greater, Next.js will probably show you a warning about the `experimental.instrumentationHook` being set. To silence Next.js' warning, explicitly set the `experimental.instrumentationHook` option in your `next.config.(js|mjs|ts)` to `undefined`. If you are on Next.js version 14 or lower, you can silence this particular warning by explicitly setting the `experimental.instrumentationHook` option in your `next.config.(js|mjs)` to `true`.\",\n      );\n      incomingUserNextConfigObject.experimental = {\n        instrumentationHook: true,\n        ...incomingUserNextConfigObject.experimental,\n      };\n    }\n  }\n\n  // We wanna check whether the user added a `onRouterTransitionStart` handler to their client instrumentation file.\n  const instrumentationClientFileContents = getInstrumentationClientFileContents();\n  if (\n    instrumentationClientFileContents !== undefined &&\n    !instrumentationClientFileContents.includes('onRouterTransitionStart')\n  ) {\n    // eslint-disable-next-line no-console\n    console.warn(\n      '[@sentry/nextjs] ACTION REQUIRED: To instrument navigations, the Sentry SDK requires you to export an `onRouterTransitionStart` hook from your `instrumentation-client.(js|ts)` file. You can do so by adding `export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;` to the file.',\n    );\n  }\n\n  if (nextJsVersion) {\n    const { major, minor, patch, prerelease } = parseSemver(nextJsVersion);\n    const isSupportedVersion =\n      major !== undefined &&\n      minor !== undefined &&\n      patch !== undefined &&\n      (major > 15 ||\n        (major === 15 && minor > 3) ||\n        (major === 15 && minor === 3 && patch === 0 && prerelease === undefined) ||\n        (major === 15 && minor === 3 && patch > 0));\n    const isSupportedCanary =\n      major !== undefined &&\n      minor !== undefined &&\n      patch !== undefined &&\n      prerelease !== undefined &&\n      major === 15 &&\n      minor === 3 &&\n      patch === 0 &&\n      prerelease.startsWith('canary.') &&\n      parseInt(prerelease.split('.')[1] || '', 10) >= 28;\n    const supportsClientInstrumentation = isSupportedCanary || isSupportedVersion;\n\n    if (!supportsClientInstrumentation && process.env.TURBOPACK) {\n      if (process.env.NODE_ENV === 'development') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `[@sentry/nextjs] WARNING: You are using the Sentry SDK with Turbopack (\\`next dev --turbo\\`). The Sentry SDK is compatible with Turbopack on Next.js version 15.3.0 or later. You are currently on ${nextJsVersion}. Please upgrade to a newer Next.js version to use the Sentry SDK with Turbopack. Note that the SDK will continue to work for non-Turbopack production builds. This warning is only about dev-mode.`,\n        );\n      } else if (process.env.NODE_ENV === 'production') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `[@sentry/nextjs] WARNING: You are using the Sentry SDK with Turbopack (\\`next build --turbo\\`). The Sentry SDK is compatible with Turbopack on Next.js version 15.3.0 or later. You are currently on ${nextJsVersion}. Please upgrade to a newer Next.js version to use the Sentry SDK with Turbopack. Note that as Turbopack is still experimental for production builds, some of the Sentry SDK features like source maps will not work. Follow this issue for progress on Sentry + Turbopack: https://github.com/getsentry/sentry-javascript/issues/8105.`,\n        );\n      }\n    }\n  }\n\n  return {\n    ...incomingUserNextConfigObject,\n    webpack: constructWebpackConfigFunction(incomingUserNextConfigObject, userSentryOptions, releaseName),\n  };\n}\n\n/**\n * Injects rewrite rules into the Next.js config provided by the user to tunnel\n * requests from the `tunnelPath` to Sentry.\n *\n * See https://nextjs.org/docs/api-reference/next.config.js/rewrites.\n */\nfunction setUpTunnelRewriteRules(userNextConfig: NextConfigObject, tunnelPath: string): void {\n  const originalRewrites = userNextConfig.rewrites;\n\n  // This function doesn't take any arguments at the time of writing but we future-proof\n  // here in case Next.js ever decides to pass some\n  userNextConfig.rewrites = async (...args: unknown[]) => {\n    const tunnelRouteRewrite = {\n      // Matched rewrite routes will look like the following: `[tunnelPath]?o=[orgid]&p=[projectid]`\n      // Nextjs will automatically convert `source` into a regex for us\n      source: `${tunnelPath}(/?)`,\n      has: [\n        {\n          type: 'query',\n          key: 'o', // short for orgId - we keep it short so matching is harder for ad-blockers\n          value: '(?<orgid>\\\\d*)',\n        },\n        {\n          type: 'query',\n          key: 'p', // short for projectId - we keep it short so matching is harder for ad-blockers\n          value: '(?<projectid>\\\\d*)',\n        },\n      ],\n      destination: 'https://o:orgid.ingest.sentry.io/api/:projectid/envelope/?hsts=0',\n    };\n\n    const tunnelRouteRewriteWithRegion = {\n      // Matched rewrite routes will look like the following: `[tunnelPath]?o=[orgid]&p=[projectid]?r=[region]`\n      // Nextjs will automatically convert `source` into a regex for us\n      source: `${tunnelPath}(/?)`,\n      has: [\n        {\n          type: 'query',\n          key: 'o', // short for orgId - we keep it short so matching is harder for ad-blockers\n          value: '(?<orgid>\\\\d*)',\n        },\n        {\n          type: 'query',\n          key: 'p', // short for projectId - we keep it short so matching is harder for ad-blockers\n          value: '(?<projectid>\\\\d*)',\n        },\n        {\n          type: 'query',\n          key: 'r', // short for region - we keep it short so matching is harder for ad-blockers\n          value: '(?<region>[a-z]{2})',\n        },\n      ],\n      destination: 'https://o:orgid.ingest.:region.sentry.io/api/:projectid/envelope/?hsts=0',\n    };\n\n    // Order of these is important, they get applied first to last.\n    const newRewrites = [tunnelRouteRewriteWithRegion, tunnelRouteRewrite];\n\n    if (typeof originalRewrites !== 'function') {\n      return newRewrites;\n    }\n\n    // @ts-expect-error Expected 0 arguments but got 1 - this is from the future-proofing mentioned above, so we don't care about it\n    const originalRewritesResult = await originalRewrites(...args);\n\n    if (Array.isArray(originalRewritesResult)) {\n      return [...newRewrites, ...originalRewritesResult];\n    } else {\n      return {\n        ...originalRewritesResult,\n        beforeFiles: [...newRewrites, ...(originalRewritesResult.beforeFiles || [])],\n      };\n    }\n  };\n}\n\nfunction setUpBuildTimeVariables(\n  userNextConfig: NextConfigObject,\n  userSentryOptions: SentryBuildOptions,\n  releaseName: string | undefined,\n): void {\n  const assetPrefix = userNextConfig.assetPrefix || userNextConfig.basePath || '';\n  const basePath = userNextConfig.basePath ?? '';\n  const rewritesTunnelPath =\n    userSentryOptions.tunnelRoute !== undefined && userNextConfig.output !== 'export'\n      ? `${basePath}${userSentryOptions.tunnelRoute}`\n      : undefined;\n\n  const buildTimeVariables: Record<string, string> = {\n    // Make sure that if we have a windows path, the backslashes are interpreted as such (rather than as escape\n    // characters)\n    _sentryRewriteFramesDistDir: userNextConfig.distDir?.replace(/\\\\/g, '\\\\\\\\') || '.next',\n    // Get the path part of `assetPrefix`, minus any trailing slash. (We use a placeholder for the origin if\n    // `assetPrefix` doesn't include one. Since we only care about the path, it doesn't matter what it is.)\n    _sentryRewriteFramesAssetPrefixPath: assetPrefix\n      ? new URL(assetPrefix, 'http://dogs.are.great').pathname.replace(/\\/$/, '')\n      : '',\n  };\n\n  if (userNextConfig.assetPrefix) {\n    buildTimeVariables._assetsPrefix = userNextConfig.assetPrefix;\n  }\n\n  if (userSentryOptions._experimental?.thirdPartyOriginStackFrames) {\n    buildTimeVariables._experimentalThirdPartyOriginStackFrames = 'true';\n  }\n\n  if (rewritesTunnelPath) {\n    buildTimeVariables._sentryRewritesTunnelPath = rewritesTunnelPath;\n  }\n\n  if (basePath) {\n    buildTimeVariables._sentryBasePath = basePath;\n  }\n\n  if (userNextConfig.assetPrefix) {\n    buildTimeVariables._sentryAssetPrefix = userNextConfig.assetPrefix;\n  }\n\n  if (userSentryOptions._experimental?.thirdPartyOriginStackFrames) {\n    buildTimeVariables._experimentalThirdPartyOriginStackFrames = 'true';\n  }\n\n  if (releaseName) {\n    buildTimeVariables._sentryRelease = releaseName;\n  }\n\n  if (typeof userNextConfig.env === 'object') {\n    userNextConfig.env = { ...buildTimeVariables, ...userNextConfig.env };\n  } else if (userNextConfig.env === undefined) {\n    userNextConfig.env = buildTimeVariables;\n  }\n}\n\nfunction getGitRevision(): string | undefined {\n  let gitRevision: string | undefined;\n  try {\n    gitRevision = childProcess\n      .execSync('git rev-parse HEAD', { stdio: ['ignore', 'pipe', 'ignore'] })\n      .toString()\n      .trim();\n  } catch (e) {\n    // noop\n  }\n  return gitRevision;\n}\n\nfunction getInstrumentationClientFileContents(): string | void {\n  const potentialInstrumentationClientFileLocations = [\n    ['src', 'instrumentation-client.ts'],\n    ['src', 'instrumentation-client.js'],\n    ['instrumentation-client.ts'],\n    ['instrumentation-client.ts'],\n  ];\n\n  for (const pathSegments of potentialInstrumentationClientFileLocations) {\n    try {\n      return fs.readFileSync(path.join(process.cwd(), ...pathSegments), 'utf-8');\n    } catch {\n      // noop\n    }\n  }\n}\n"], "names": ["isThenable", "getSentryRelease", "getNextjsVersion", "parseSemver", "constructWebpackConfigFunction"], "mappings": ";;;;;;;;;;AAAA,4BAAA,GACA,6BAAA,GAeA,IAAI,6BAAA,GAAgC,KAAK;AAEzC;;;;;;CAMA,GACO,SAAS,gBAAgB,CAAI,UAAU,EAAM,kBAAkB,GAAuB,CAAA,CAAE,EAAK;IAClG,MAAM,iBAAiB,AAAC,cAA6B,CAAA,CAAE;IACvD,IAAI,OAAO,cAAe,KAAI,UAAU,EAAE;QACxC,OAAO,SAAyB,GAAG,yBAAyB,EAA6C;YACvG,MAAM,sBAAsB,GAAsC,cAAc,CAAC,KAAK,CACpF,IAAI,EACJ,yBAAyB;YAG3B,IAAIA,KAAAA,UAAU,CAAC,sBAAsB,CAAC,EAAE;gBACtC,OAAO,sBAAsB,CAAC,IAAI,EAAC,2BAA2B;oBAC5D,OAAO,oBAAoB,CAAC,uBAAuB,EAAE,kBAAkB,CAAC;gBAClF,CAAS,CAAC;YACV;YAEM,OAAO,oBAAoB,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;QAC7E,CAAM;IACN,OAAS;QACL,OAAO,oBAAoB,CAAC,cAAc,EAAE,kBAAkB,CAAE;IACpE;AACA;AAEA,mHAAA;AACA,qBAAA;AACA,SAAS,oBAAoB,CAC3B,4BAA4B,EAC5B,iBAAiB;IAEjB,MAAM,WAAA,GAAc,iBAAiB,CAAC,OAAO,EAAE,IAAK,IAAGC,KAAAA,gBAAgB,EAAC,IAAK,cAAc,EAAE;IAE7F,IAAI,iBAAiB,EAAE,WAAW,EAAE;QAClC,IAAI,4BAA4B,CAAC,MAAO,KAAI,QAAQ,EAAE;YACpD,IAAI,CAAC,6BAA6B,EAAE;gBAClC,6BAAA,GAAgC,IAAI;gBAC5C,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,0WAA0W;YAEpX;QACA,OAAW;YACL,uBAAuB,CAAC,4BAA4B,EAAE,iBAAiB,CAAC,WAAW,CAAC;QAC1F;IACA;IAEE,uBAAuB,CAAC,4BAA4B,EAAE,iBAAiB,EAAE,WAAW,CAAC;IAErF,MAAM,aAAA,GAAgBC,KAAAA,gBAAgB,EAAE;IAE1C,+JAAA;IACA,sHAAA;IACE,IAAI,aAAa,EAAE;QACjB,MAAM,EAAE,KAAK,EAAE,KAAA,EAAA,GAAUC,KAAAA,WAAW,CAAC,aAAa,CAAC;QACnD,IAAI,KAAA,KAAU,SAAA,IAAa,KAAM,KAAI,SAAU,IAAA,CAAI,KAAA,IAAS,EAAG,IAAI,KAAA,KAAU,EAAA,IAAM,KAAA,IAAS,CAAC,AAAC,CAAC,EAAE;YAC/F,4BAA4B,CAAC,YAAa,GAAE,4BAA4B,CAAC,YAAA,IAAgB,CAAA,CAAE;YAC3F,4BAA4B,CAAC,YAAY,CAAC,mBAAA,GAAsB;gBAC9D,SAAS;gBACT,cAAc;mBACV,4BAA4B,CAAC,YAAY,EAAE,mBAAoB,IAAG,EAAE,CAAC;aAC1E;QACP;IACA,OAAS;QACT,sCAAA;QACI,OAAO,CAAC,GAAG,CACT,8QAA8Q;IAEpR;IAEA,0IAAA;IACA,6FAAA;IACE,IAAI,aAAa,EAAE;QACjB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAA,EAAa,GAAEA,KAAAA,WAAW,CAAC,aAAa,CAAC;QACtE,MAAM,uBAAwB,GAC5B,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,KAAA,IAAS,EAAG,IAClB,CAAO,AAAC,KAAM,KAAI,KAAK,KAAA,KAAU,CAAA,IAAK,UAAA,KAAe,SAAS,IAAK,KAAM,GAAE,KAAK,KAAA,GAAQ,CAAC,CAAC;QACtF,MAAM,gBAAiB,GACrB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,UAAA,KAAe,SAAU,IACzB,KAAA,KAAU,EAAG,IACb,KAAA,KAAU,CAAE,IACZ,KAAA,KAAU,CAAE,IACZ,UAAU,CAAC,UAAU,CAAC,KAAK,CAAE,IAC7B,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,IAAK,EAAE,EAAE,EAAE,CAAA,GAAI,CAAC;QAClD,MAAM,iBAAkB,GACtB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,UAAA,KAAe,SAAU,IACzB,KAAA,KAAU,EAAG,IACb,KAAA,KAAU,CAAE,IACZ,KAAA,KAAU,CAAE,IACZ,UAAU,CAAC,UAAU,CAAC,SAAS,CAAE,IACjC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,IAAK,EAAE,EAAE,EAAE,CAAA,IAAK,GAAG;QAErD,IAAI,CAAC,uBAAwB,IAAG,CAAC,gBAAiB,IAAG,CAAC,iBAAiB,EAAE;YACvE,IAAI,4BAA4B,CAAC,YAAY,EAAE,mBAAA,KAAwB,KAAK,EAAE;gBACpF,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,oLAAoL;YAE9L;YACM,4BAA4B,CAAC,YAAA,GAAe;gBAC1C,mBAAmB,EAAE,IAAI;gBACzB,GAAG,4BAA4B,CAAC,YAAY;YACpD,CAAO;QACP;IACA,OAAS;QACT,yKAAA;QACI,IACE,4BAA4B,CAAC,YAAa,IAC1C,qBAAA,IAAyB,4BAA4B,CAAC,YAAA,EACtD;YACA,IAAI,4BAA4B,CAAC,YAAY,CAAC,mBAAA,KAAwB,KAAK,EAAE;gBACnF,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,qXAAqX;YAE/X;QACA,OAAW;YACX,sCAAA;YACM,OAAO,CAAC,GAAG,CACT,+iBAA+iB;YAEjjB,4BAA4B,CAAC,YAAA,GAAe;gBAC1C,mBAAmB,EAAE,IAAI;gBACzB,GAAG,4BAA4B,CAAC,YAAY;YACpD,CAAO;QACP;IACA;IAEA,kHAAA;IACE,MAAM,iCAAA,GAAoC,oCAAoC,EAAE;IAChF,IACE,iCAAA,KAAsC,SAAU,IAChD,CAAC,iCAAiC,CAAC,QAAQ,CAAC,yBAAyB,GACrE;QACJ,sCAAA;QACI,OAAO,CAAC,IAAI,CACV,0SAA0S;IAEhT;IAEE,IAAI,aAAa,EAAE;QACjB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAA,EAAa,GAAEA,KAAAA,WAAW,CAAC,aAAa,CAAC;QACtE,MAAM,kBAAmB,GACvB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IAC1B,CAAO,KAAM,GAAE,EAAG,IACT,UAAU,EAAA,IAAM,KAAM,GAAE,CAAC,CAAE,GAC3B,KAAM,KAAI,EAAG,IAAG,UAAU,CAAA,IAAK,KAAA,KAAU,CAAE,IAAG,UAAW,KAAI,SAAS,CAAE,GACxE,KAAA,KAAU,EAAA,IAAM,KAAA,KAAU,CAAA,IAAK,KAAA,GAAQ,CAAC,AAAC,CAAC;QAC/C,MAAM,iBAAkB,GACtB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,KAAA,KAAU,SAAU,IACpB,UAAA,KAAe,SAAU,IACzB,KAAA,KAAU,EAAG,IACb,KAAA,KAAU,CAAE,IACZ,KAAA,KAAU,CAAE,IACZ,UAAU,CAAC,UAAU,CAAC,SAAS,CAAE,IACjC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,IAAK,EAAE,EAAE,EAAE,CAAA,IAAK,EAAE;QACpD,MAAM,6BAAA,GAAgC,iBAAA,IAAqB,kBAAkB;QAE7E,IAAI,CAAC,6BAA8B,IAAG,OAAO,CAAC,GAAG,CAAC,SAAS,mBAAE;YAC3D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAA,KAAa,WAAe,EAAF;gBAChD,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,CAAC,mMAAmM,EAAE,aAAa,CAAC,mMAAmM,CAAC;YAEla,CAAM,MAAO,IAAI,OAAO,CAAC,GAAG,CAAC,QAAA,KAAa,YAAY,EAAE;;YAKxD;QACA;IACA;IAEE,OAAO;QACL,GAAG,4BAA4B;QAC/B,OAAO,EAAEC,QAAAA,8BAA8B,CAAC,4BAA4B,EAAE,iBAAiB,EAAE,WAAW,CAAC;IACzG,CAAG;AACH;AAEA;;;;;CAKA,GACA,SAAS,uBAAuB,CAAC,cAAc,EAAoB,UAAU,EAAgB;IAC3F,MAAM,gBAAA,GAAmB,cAAc,CAAC,QAAQ;IAElD,sFAAA;IACA,iDAAA;IACE,cAAc,CAAC,QAAS,GAAE,OAAO,GAAG,IAAI,KAAgB;QACtD,MAAM,qBAAqB;YAC/B,8FAAA;YACA,iEAAA;YACM,MAAM,EAAE,CAAC,EAAA,UAAA,CAAA,IAAA,CAAA;YACA,GAAA,EAAA;gBACA;oBACA,IAAA,EAAA,OAAA;oBACA,GAAA,EAAA,GAAA;oBACA,KAAA,EAAA,gBAAA;gBACA,CAAA;gBACA;oBACA,IAAA,EAAA,OAAA;oBACA,GAAA,EAAA,GAAA;oBACA,KAAA,EAAA,oBAAA;gBACA,CAAA;aACA;YACA,WAAA,EAAA,kEAAA;QACA,CAAA;QAEA,MAAA,4BAAA,GAAA;YACA,yGAAA;YACA,iEAAA;YACA,MAAA,EAAA,CAAA,EAAA,UAAA,CAAA,IAAA,CAAA;YACA,GAAA,EAAA;gBACA;oBACA,IAAA,EAAA,OAAA;oBACA,GAAA,EAAA,GAAA;oBACA,KAAA,EAAA,gBAAA;gBACA,CAAA;gBACA;oBACA,IAAA,EAAA,OAAA;oBACA,GAAA,EAAA,GAAA;oBACA,KAAA,EAAA,oBAAA;gBACA,CAAA;gBACA;oBACA,IAAA,EAAA,OAAA;oBACA,GAAA,EAAA,GAAA;oBACA,KAAA,EAAA,qBAAA;gBACA,CAAA;aACA;YACA,WAAA,EAAA,0EAAA;QACA,CAAA;QAEA,+DAAA;QACA,MAAA,WAAA,GAAA;YAAA,4BAAA;YAAA,kBAAA;SAAA;QAEA,IAAA,OAAA,gBAAA,KAAA,UAAA,EAAA;YACA,OAAA,WAAA;QACA;QAEA,gIAAA;QACA,MAAA,sBAAA,GAAA,MAAA,gBAAA,CAAA,GAAA,IAAA,CAAA;QAEA,IAAA,KAAA,CAAA,OAAA,CAAA,sBAAA,CAAA,EAAA;YACA,OAAA,CAAA;mBAAA,WAAA,EAAA;mBAAA,sBAAA;aAAA;QACA,CAAA,MAAA;YACA,OAAA;gBACA,GAAA,sBAAA;gBACA,WAAA,EAAA,CAAA;uBAAA,WAAA,EAAA;uBAAA,sBAAA,CAAA,WAAA,IAAA,EAAA,CAAA;iBAAA;YACA,CAAA;QACA;IACA,CAAA;AACA;AAEA,SAAA,uBAAA,CACA,cAAA,EACA,iBAAA,EACA,WAAA;IAEA,MAAA,WAAA,GAAA,cAAA,CAAA,WAAA,IAAA,cAAA,CAAA,QAAA,IAAA,EAAA;IACA,MAAA,QAAA,GAAA,cAAA,CAAA,QAAA,IAAA,EAAA;IACA,MAAA,kBAAA,GACA,iBAAA,CAAA,WAAA,KAAA,SAAA,IAAA,cAAA,CAAA,MAAA,KAAA,WACA,CAAA,EAAA,QAAA,CAAA,EAAA,iBAAA,CAAA,WAAA,CAAA,CAAA,GACA,SAAA;IAEA,MAAA,kBAAA,GAAA;QACA,2GAAA;QACA,cAAA;QACA,2BAAA,EAAA,cAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,EAAA,MAAA,CAAA,IAAA,OAAA;QACA,wGAAA;QACA,uGAAA;QACA,mCAAA,EAAA,cACA,IAAA,GAAA,CAAA,WAAA,EAAA,uBAAA,CAAA,CAAA,QAAA,CAAA,OAAA,CAAA,KAAA,EAAA,EAAA,IACA,EAAA;IACA,CAAA;IAEA,IAAA,cAAA,CAAA,WAAA,EAAA;QACA,kBAAA,CAAA,aAAA,GAAA,cAAA,CAAA,WAAA;IACA;IAEA,IAAA,iBAAA,CAAA,aAAA,EAAA,2BAAA,EAAA;QACA,kBAAA,CAAA,wCAAA,GAAA,MAAA;IACA;IAEA,IAAA,kBAAA,EAAA;QACA,kBAAA,CAAA,yBAAA,GAAA,kBAAA;IACA;IAEA,IAAA,QAAA,EAAA;QACA,kBAAA,CAAA,eAAA,GAAA,QAAA;IACA;IAEA,IAAA,cAAA,CAAA,WAAA,EAAA;QACA,kBAAA,CAAA,kBAAA,GAAA,cAAA,CAAA,WAAA;IACA;IAEA,IAAA,iBAAA,CAAA,aAAA,EAAA,2BAAA,EAAA;QACA,kBAAA,CAAA,wCAAA,GAAA,MAAA;IACA;IAEA,IAAA,WAAA,EAAA;QACA,kBAAA,CAAA,cAAA,GAAA,WAAA;IACA;IAEA,IAAA,OAAA,cAAA,CAAA,GAAA,KAAA,QAAA,EAAA;QACA,cAAA,CAAA,GAAA,GAAA;YAAA,GAAA,kBAAA;YAAA,GAAA,cAAA,CAAA,GAAA;QAAA,CAAA;IACA,CAAA,MAAA,IAAA,cAAA,CAAA,GAAA,KAAA,SAAA,EAAA;QACA,cAAA,CAAA,GAAA,GAAA,kBAAA;IACA;AACA;AAEA,SAAA,cAAA,GAAA;IACA,IAAA,WAAA;IACA,IAAA;QACA,WAAA,GAAA,aACA,QAAA,CAAA,oBAAA,EAAA;YAAA,KAAA,EAAA;gBAAA,QAAA;gBAAA,MAAA;gBAAA,QAAA;aAAA;QAAA,CAAA,EACA,QAAA,GACA,IAAA,EAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;IACA,OAAA;IACA;IACA,OAAA,WAAA;AACA;AAEA,SAAA,oCAAA,GAAA;IACA,MAAA,2CAAA,GAAA;QACA;YAAA,KAAA;YAAA,2BAAA;SAAA;QACA;YAAA,KAAA;YAAA,2BAAA;SAAA;QACA;YAAA,2BAAA;SAAA;QACA;YAAA,2BAAA;SAAA;KACA;IAEA,KAAA,MAAA,YAAA,IAAA,2CAAA,CAAA;QACA,IAAA;YACA,OAAA,EAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,EAAA,EAAA,GAAA,YAAA,CAAA,EAAA,OAAA,CAAA;QACA,CAAA,CAAA,OAAA;QACA,OAAA;QACA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": ";;;AAEA;;;;CAIA,GACO,MAAM,WAAY,GAAiB,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "file": "devErrorSymbolicationEventProcessor.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/devErrorSymbolicationEventProcessor.ts"], "sourcesContent": ["import type { Event, EventHint } from '@sentry/core';\nimport { G<PERSON><PERSON><PERSON>L_OBJ, logger, parseSemver, suppressTracing } from '@sentry/core';\nimport type { StackFrame } from 'stacktrace-parser';\nimport * as stackTraceParser from 'stacktrace-parser';\nimport { DEBUG_BUILD } from './debug-build';\n\ntype OriginalStackFrameResponse = {\n  originalStackFrame: StackFrame;\n  originalCodeFrame: string | null;\n  sourcePackage?: string;\n};\n\nconst globalWithInjectedValues = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  _sentryBasePath?: string;\n  next?: {\n    version?: string;\n  };\n};\n\n/**\n * Event processor that will symbolicate errors by using the webpack/nextjs dev server that is used to show stack traces\n * in the dev overlay.\n */\nexport async function devErrorSymbolicationEventProcessor(event: Event, hint: EventHint): Promise<Event | null> {\n  // Filter out spans for requests resolving source maps for stack frames in dev mode\n  if (event.type === 'transaction') {\n    event.spans = event.spans?.filter(span => {\n      const httpUrlAttribute: unknown = span.data?.['http.url'];\n      if (typeof httpUrlAttribute === 'string') {\n        return !httpUrlAttribute.includes('__nextjs_original-stack-frame'); // could also be __nextjs_original-stack-frames (plural)\n      }\n\n      return true;\n    });\n  }\n\n  // Due to changes across Next.js versions, there are a million things that can go wrong here so we just try-catch the\n  // entire event processor. Symbolicated stack traces are just a nice to have.\n  try {\n    if (hint.originalException && hint.originalException instanceof Error && hint.originalException.stack) {\n      const frames = stackTraceParser.parse(hint.originalException.stack);\n\n      const nextjsVersion = globalWithInjectedValues.next?.version || '0.0.0';\n      const parsedNextjsVersion = nextjsVersion ? parseSemver(nextjsVersion) : {};\n\n      let resolvedFrames: ({\n        originalCodeFrame: string | null;\n        originalStackFrame: StackFrame | null;\n      } | null)[];\n\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      if (parsedNextjsVersion.major! > 15 || (parsedNextjsVersion.major === 15 && parsedNextjsVersion.minor! >= 2)) {\n        const r = await resolveStackFrames(frames);\n        if (r === null) {\n          return event;\n        }\n        resolvedFrames = r;\n      } else {\n        resolvedFrames = await Promise.all(\n          frames.map(frame => resolveStackFrame(frame, hint.originalException as Error)),\n        );\n      }\n\n      if (event.exception?.values?.[0]?.stacktrace?.frames) {\n        event.exception.values[0].stacktrace.frames = event.exception.values[0].stacktrace.frames.map(\n          (frame, i, frames) => {\n            const resolvedFrame = resolvedFrames[frames.length - 1 - i];\n            if (!resolvedFrame?.originalStackFrame || !resolvedFrame.originalCodeFrame) {\n              return {\n                ...frame,\n                platform: frame.filename?.startsWith('node:internal') ? 'nodejs' : undefined, // simple hack that will prevent a source mapping error from showing up\n                in_app: false,\n              };\n            }\n\n            const { contextLine, preContextLines, postContextLines } = parseOriginalCodeFrame(\n              resolvedFrame.originalCodeFrame,\n            );\n\n            return {\n              ...frame,\n              pre_context: preContextLines,\n              context_line: contextLine,\n              post_context: postContextLines,\n              function: resolvedFrame.originalStackFrame.methodName,\n              filename: resolvedFrame.originalStackFrame.file || undefined,\n              lineno: resolvedFrame.originalStackFrame.lineNumber || undefined,\n              colno: resolvedFrame.originalStackFrame.column || undefined,\n            };\n          },\n        );\n      }\n    }\n  } catch (e) {\n    return event;\n  }\n\n  return event;\n}\n\nasync function resolveStackFrame(\n  frame: StackFrame,\n  error: Error,\n): Promise<{ originalCodeFrame: string | null; originalStackFrame: StackFrame | null } | null> {\n  try {\n    if (!(frame.file?.startsWith('webpack-internal:') || frame.file?.startsWith('file:'))) {\n      return null;\n    }\n\n    const params = new URLSearchParams();\n    params.append('isServer', String(false)); // doesn't matter since it is overwritten by isAppDirectory\n    params.append('isEdgeServer', String(false)); // doesn't matter since it is overwritten by isAppDirectory\n    params.append('isAppDirectory', String(true)); // will force server to do more thorough checking\n    params.append('errorMessage', error.toString());\n    Object.keys(frame).forEach(key => {\n      params.append(key, (frame[key as keyof typeof frame] ?? '').toString());\n    });\n\n    let basePath = process.env._sentryBasePath ?? globalWithInjectedValues._sentryBasePath ?? '';\n\n    // Prefix the basepath with a slash if it doesn't have one\n    if (basePath !== '' && !basePath.match(/^\\//)) {\n      basePath = `/${basePath}`;\n    }\n\n    const controller = new AbortController();\n    const timer = setTimeout(() => controller.abort(), 3000);\n    const res = await suppressTracing(() =>\n      fetch(\n        `${\n          // eslint-disable-next-line no-restricted-globals\n          typeof window === 'undefined' ? 'http://localhost:3000' : '' // TODO: handle the case where users define a different port\n        }${basePath}/__nextjs_original-stack-frame?${params.toString()}`,\n        {\n          signal: controller.signal,\n        },\n      ).finally(() => {\n        clearTimeout(timer);\n      }),\n    );\n\n    if (!res.ok || res.status === 204) {\n      return null;\n    }\n\n    const body: OriginalStackFrameResponse = await res.json();\n\n    return {\n      originalCodeFrame: body.originalCodeFrame,\n      originalStackFrame: body.originalStackFrame,\n    };\n  } catch (e) {\n    DEBUG_BUILD && logger.error('Failed to symbolicate event with Next.js dev server', e);\n    return null;\n  }\n}\n\nasync function resolveStackFrames(\n  frames: StackFrame[],\n): Promise<{ originalCodeFrame: string | null; originalStackFrame: StackFrame | null }[] | null> {\n  try {\n    const postBody = {\n      frames: frames\n        .filter(frame => {\n          return !!frame.file;\n        })\n        .map(frame => {\n          // https://github.com/vercel/next.js/blob/df0573a478baa8b55478a7963c473dddd59a5e40/packages/next/src/client/components/react-dev-overlay/server/middleware-turbopack.ts#L129\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          frame.file = frame.file!.replace(/^rsc:\\/\\/React\\/[^/]+\\//, '').replace(/\\?\\d+$/, '');\n\n          return {\n            file: frame.file,\n            methodName: frame.methodName ?? '<unknown>',\n            arguments: [],\n            lineNumber: frame.lineNumber ?? 0,\n            column: frame.column ?? 0,\n          };\n        }),\n      isServer: false,\n      isEdgeServer: false,\n      isAppDirectory: true,\n    };\n\n    let basePath = process.env._sentryBasePath ?? globalWithInjectedValues._sentryBasePath ?? '';\n\n    // Prefix the basepath with a slash if it doesn't have one\n    if (basePath !== '' && !basePath.match(/^\\//)) {\n      basePath = `/${basePath}`;\n    }\n\n    const controller = new AbortController();\n    const timer = setTimeout(() => controller.abort(), 3000);\n\n    const res = await fetch(\n      `${\n        // eslint-disable-next-line no-restricted-globals\n        typeof window === 'undefined' ? 'http://localhost:3000' : '' // TODO: handle the case where users define a different port\n      }${basePath}/__nextjs_original-stack-frames`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        signal: controller.signal,\n        body: JSON.stringify(postBody),\n      },\n    ).finally(() => {\n      clearTimeout(timer);\n    });\n\n    if (!res.ok || res.status === 204) {\n      return null;\n    }\n\n    const body: { value: OriginalStackFrameResponse }[] = await res.json();\n\n    return body.map(frame => {\n      return {\n        originalCodeFrame: frame.value.originalCodeFrame,\n        originalStackFrame: frame.value.originalStackFrame,\n      };\n    });\n  } catch (e) {\n    DEBUG_BUILD && logger.error('Failed to symbolicate event with Next.js dev server', e);\n    return null;\n  }\n}\n\nfunction parseOriginalCodeFrame(codeFrame: string): {\n  contextLine: string | undefined;\n  preContextLines: string[];\n  postContextLines: string[];\n} {\n  const preProcessedLines = codeFrame\n    // Remove ASCII control characters that are used for syntax highlighting\n    .replace(\n      // eslint-disable-next-line no-control-regex\n      /[\\u001b\\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, // https://stackoverflow.com/a/29497680\n      '',\n    )\n    .split('\\n')\n    // Remove line that is supposed to indicate where the error happened\n    .filter(line => !line.match(/^\\s*\\|/))\n    // Find the error line\n    .map(line => ({\n      line,\n      isErrorLine: !!line.match(/^>/),\n    }))\n    // Remove the leading part that is just for prettier output\n    .map(lineObj => ({\n      ...lineObj,\n      line: lineObj.line.replace(/^.*\\|/, ''),\n    }));\n\n  const preContextLines = [];\n  let contextLine: string | undefined = undefined;\n  const postContextLines = [];\n\n  let reachedContextLine = false;\n\n  for (const preProcessedLine of preProcessedLines) {\n    if (preProcessedLine.isErrorLine) {\n      contextLine = preProcessedLine.line;\n      reachedContextLine = true;\n    } else if (reachedContextLine) {\n      postContextLines.push(preProcessedLine.line);\n    } else {\n      preContextLines.push(preProcessedLine.line);\n    }\n  }\n\n  return {\n    contextLine,\n    preContextLines,\n    postContextLines,\n  };\n}\n"], "names": ["GLOBAL_OBJ", "parseSemver", "suppressTracing", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;AAYA,MAAM,wBAAA,GAA2BA,KAAAA,UAAAA;AAOjC;;;CAGA,GACO,eAAe,mCAAmC,CAAC,KAAK,EAAS,IAAI,EAAoC;IAChH,mFAAA;IACE,IAAI,KAAK,CAAC,IAAK,KAAI,aAAa,EAAE;QAChC,KAAK,CAAC,KAAA,GAAQ,KAAK,CAAC,KAAK,EAAE,MAAM,EAAC,IAAA,IAAQ;YACxC,MAAM,gBAAgB,GAAY,IAAI,CAAC,IAAI,EAAA,CAAG,UAAU,CAAC;YACzD,IAAI,OAAO,gBAAiB,KAAI,QAAQ,EAAE;gBACxC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAA,CAAA,wDAAA;YAC1E;YAEM,OAAO,IAAI;QACjB,CAAK,CAAC;IACN;IAEA,qHAAA;IACA,6EAAA;IACE,IAAI;QACF,IAAI,IAAI,CAAC,iBAAA,IAAqB,IAAI,CAAC,iBAAkB,YAAW,SAAS,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACrG,MAAM,MAAA,GAAS,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAEnE,MAAM,gBAAgB,wBAAwB,CAAC,IAAI,EAAE,OAAQ,IAAG,OAAO;YACvE,MAAM,mBAAoB,GAAE,aAAc,0BAAEC,KAAAA,WAAW,CAAC,aAAa,CAAA,GAAI,EAAE;YAE3E,IAAI;YAKV,oEAAA;YACM,IAAI,mBAAmB,CAAC,KAAK,GAAI,EAAG,IAAI,mBAAmB,CAAC,KAAM,KAAI,EAAG,IAAG,mBAAmB,CAAC,KAAK,IAAK,CAAC,CAAC,CAAE;gBAC5G,MAAM,CAAE,GAAE,MAAM,kBAAkB,CAAC,MAAM,CAAC;gBAC1C,IAAI,CAAE,KAAI,IAAI,EAAE;oBACd,OAAO,KAAK;gBACtB;gBACQ,cAAA,GAAiB,CAAC;YAC1B,OAAa;gBACL,cAAe,GAAE,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,EAAC,QAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAA,EAA2B,CAAC;YAExF;YAEM,IAAI,KAAK,CAAC,SAAS,EAAE,MAAM,EAAA,CAAG,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE;gBACpD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAO,GAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAC3F,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,KAAK;oBACpB,MAAM,aAAA,GAAgB,cAAc,CAAC,MAAM,CAAC,MAAA,GAAS,CAAA,GAAI,CAAC,CAAC;oBAC3D,IAAI,CAAC,aAAa,EAAE,kBAAmB,IAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE;wBAC1E,OAAO;4BACL,GAAG,KAAK;4BACR,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,eAAe,CAAA,GAAI,QAAA,GAAW,SAAS;4BAC5E,MAAM,EAAE,KAAK;wBAC7B,CAAe;oBACf;oBAEY,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAA,EAAmB,GAAE,sBAAsB,CAC/E,aAAa,CAAC,iBAAiB;oBAGjC,OAAO;wBACL,GAAG,KAAK;wBACR,WAAW,EAAE,eAAe;wBAC5B,YAAY,EAAE,WAAW;wBACzB,YAAY,EAAE,gBAAgB;wBAC9B,QAAQ,EAAE,aAAa,CAAC,kBAAkB,CAAC,UAAU;wBACrD,QAAQ,EAAE,aAAa,CAAC,kBAAkB,CAAC,IAAA,IAAQ,SAAS;wBAC5D,MAAM,EAAE,aAAa,CAAC,kBAAkB,CAAC,UAAA,IAAc,SAAS;wBAChE,KAAK,EAAE,aAAa,CAAC,kBAAkB,CAAC,MAAA,IAAU,SAAS;oBACzE,CAAa;gBACb,CAAW;YAEX;QACA;IACA,CAAI,CAAA,OAAO,CAAC,EAAE;QACV,OAAO,KAAK;IAChB;IAEE,OAAO,KAAK;AACd;AAEA,eAAe,iBAAiB,CAC9B,KAAK,EACL,KAAK;IAEL,IAAI;QACF,IAAI,CAAA,CAAE,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;YACrF,OAAO,IAAI;QACjB;QAEI,MAAM,MAAO,GAAE,IAAI,eAAe,EAAE;QACpC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,CAAA,2DAAA;QACxC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,CAAA,2DAAA;QAC5C,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA,CAAA,iDAAA;QAC7C,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAC,GAAA,IAAO;YAChC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAI,CAAA,IAA0B,EAAE,EAAE,QAAQ,EAAE,CAAC;QAC7E,CAAK,CAAC;QAEF,IAAI,QAAA,GAAW,OAAO,CAAC,GAAG,CAAC,eAAA,IAAmB,wBAAwB,CAAC,eAAA,IAAmB,EAAE;QAEhG,0DAAA;QACI,IAAI,QAAS,KAAI,EAAG,IAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC7C,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QACA;QAEA,MAAA,UAAA,GAAA,IAAA,eAAA,EAAA;QACA,MAAA,KAAA,GAAA,UAAA,CAAA,IAAA,UAAA,CAAA,KAAA,EAAA,EAAA,IAAA,CAAA;QACA,MAAA,GAAA,GAAA,MAAAC,KAAAA,eAAA,CAAA,IACA,KAAA,CACA,CAAA,EACA,iDAAA;YACA,OAAA,MAAA,KAAA,WAAA,GAAA,uBAAA,GAAA,EAAA,CAAA,4DAAA;eACA,QAAA,CAAA,+BAAA,EAAA,MAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EACA;gBACA,MAAA,EAAA,UAAA,CAAA,MAAA;YACA,CAAA,EACA,OAAA,CAAA,MAAA;gBACA,YAAA,CAAA,KAAA,CAAA;YACA,CAAA,CAAA;QAGA,IAAA,CAAA,GAAA,CAAA,EAAA,IAAA,GAAA,CAAA,MAAA,KAAA,GAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,MAAA,IAAA,GAAA,MAAA,GAAA,CAAA,IAAA,EAAA;QAEA,OAAA;YACA,iBAAA,EAAA,IAAA,CAAA,iBAAA;YACA,kBAAA,EAAA,IAAA,CAAA,kBAAA;QACA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;QACAC,WAAAA,WAAA,IAAAC,KAAAA,MAAA,CAAA,KAAA,CAAA,qDAAA,EAAA,CAAA,CAAA;QACA,OAAA,IAAA;IACA;AACA;AAEA,eAAA,kBAAA,CACA,MAAA;IAEA,IAAA;QACA,MAAA,QAAA,GAAA;YACA,MAAA,EAAA,OACA,MAAA,EAAA,KAAA,IAAA;gBACA,OAAA,CAAA,CAAA,KAAA,CAAA,IAAA;YACA,CAAA,EACA,GAAA,EAAA,KAAA,IAAA;gBACA,4KAAA;gBACA,oEAAA;gBACA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,QAAA,EAAA,EAAA,CAAA;gBAEA,OAAA;oBACA,IAAA,EAAA,KAAA,CAAA,IAAA;oBACA,UAAA,EAAA,KAAA,CAAA,UAAA,IAAA,WAAA;oBACA,SAAA,EAAA,EAAA;oBACA,UAAA,EAAA,KAAA,CAAA,UAAA,IAAA,CAAA;oBACA,MAAA,EAAA,KAAA,CAAA,MAAA,IAAA,CAAA;gBACA,CAAA;YACA,CAAA,CAAA;YACA,QAAA,EAAA,KAAA;YACA,YAAA,EAAA,KAAA;YACA,cAAA,EAAA,IAAA;QACA,CAAA;QAEA,IAAA,QAAA,GAAA,OAAA,CAAA,GAAA,CAAA,eAAA,IAAA,wBAAA,CAAA,eAAA,IAAA,EAAA;QAEA,0DAAA;QACA,IAAA,QAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA;YACA,QAAA,GAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA;QACA;QAEA,MAAA,UAAA,GAAA,IAAA,eAAA,EAAA;QACA,MAAA,KAAA,GAAA,UAAA,CAAA,IAAA,UAAA,CAAA,KAAA,EAAA,EAAA,IAAA,CAAA;QAEA,MAAA,GAAA,GAAA,MAAA,KAAA,CACA,CAAA,EACA,iDAAA;QACA,OAAA,MAAA,KAAA,WAAA,GAAA,uBAAA,GAAA,EAAA,CAAA,4DAAA;WACA,QAAA,CAAA,+BAAA,CAAA,EACA;YACA,MAAA,EAAA,MAAA;YACA,OAAA,EAAA;gBACA,cAAA,EAAA,kBAAA;YACA,CAAA;YACA,MAAA,EAAA,UAAA,CAAA,MAAA;YACA,IAAA,EAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA;QACA,CAAA,EACA,OAAA,CAAA,MAAA;YACA,YAAA,CAAA,KAAA,CAAA;QACA,CAAA,CAAA;QAEA,IAAA,CAAA,GAAA,CAAA,EAAA,IAAA,GAAA,CAAA,MAAA,KAAA,GAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,MAAA,IAAA,GAAA,MAAA,GAAA,CAAA,IAAA,EAAA;QAEA,OAAA,IAAA,CAAA,GAAA,EAAA,KAAA,IAAA;YACA,OAAA;gBACA,iBAAA,EAAA,KAAA,CAAA,KAAA,CAAA,iBAAA;gBACA,kBAAA,EAAA,KAAA,CAAA,KAAA,CAAA,kBAAA;YACA,CAAA;QACA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;QACAD,WAAAA,WAAA,IAAAC,KAAAA,MAAA,CAAA,KAAA,CAAA,qDAAA,EAAA,CAAA,CAAA;QACA,OAAA,IAAA;IACA;AACA;AAEA,SAAA,sBAAA,CAAA,SAAA;IAKA,MAAA,iBAAA,GAAA,SACA,wEAAA;KACA,OAAA,CACA,4CAAA;IACA,6EAAA,EACA,EAAA,EAEA,KAAA,CAAA,IAAA,CACA,oEAAA;KACA,MAAA,EAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CACA,sBAAA;KACA,GAAA,EAAA,IAAA,GAAA,CAAA;YACA,IAAA;YACA,WAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA,CACA,2DAAA;KACA,GAAA,EAAA,OAAA,GAAA,CAAA;YACA,GAAA,OAAA;YACA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,OAAA,EAAA,EAAA,CAAA;QACA,CAAA,CAAA,CAAA;IAEA,MAAA,eAAA,GAAA,EAAA;IACA,IAAA,WAAA,GAAA,SAAA;IACA,MAAA,gBAAA,GAAA,EAAA;IAEA,IAAA,kBAAA,GAAA,KAAA;IAEA,KAAA,MAAA,gBAAA,IAAA,iBAAA,CAAA;QACA,IAAA,gBAAA,CAAA,WAAA,EAAA;YACA,WAAA,GAAA,gBAAA,CAAA,IAAA;YACA,kBAAA,GAAA,IAAA;QACA,CAAA,MAAA,IAAA,kBAAA,EAAA;YACA,gBAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA;QACA,CAAA,MAAA;YACA,eAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA;QACA;IACA;IAEA,OAAA;QACA,WAAA;QACA,eAAA;QACA,gBAAA;IACA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "file": "getVercelEnv.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/getVercelEnv.ts"], "sourcesContent": ["/**\n * Returns an environment setting value determined by Vercel's `VERCEL_ENV` environment variable.\n *\n * @param isClient Flag to indicate whether to use the `NEXT_PUBLIC_` prefixed version of the environment variable.\n */\nexport function getVercelEnv(isClient: boolean): string | undefined {\n  const vercelEnvVar = isClient ? process.env.NEXT_PUBLIC_VERCEL_ENV : process.env.VERCEL_ENV;\n  return vercelEnvVar ? `vercel-${vercelEnvVar}` : undefined;\n}\n"], "names": [], "mappings": ";;;AAAA;;;;CAIA,GACO,SAAS,YAAY,CAAC,QAAQ,EAA+B;IAClE,MAAM,YAAA,GAAe,QAAA,GAAW,OAAO,CAAC,GAAG,CAAC,sBAAA,GAAyB,OAAO,CAAC,GAAG,CAAC,UAAU;IAC3F,OAAO,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA,GAAA,SAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "file": "span-attributes-with-logic-attached.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/span-attributes-with-logic-attached.ts"], "sourcesContent": ["/**\n * If this attribute is attached to a transaction, the Next.js SDK will drop that transaction.\n */\nexport const TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION = 'sentry.drop_transaction';\n\nexport const TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL = 'sentry.sentry_trace_backfill';\n\nexport const TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL = 'sentry.route_backfill';\n"], "names": [], "mappings": ";;;AAAA;;CAEA,GACO,MAAM,wCAAyC,GAAE;AAEjD,MAAM,sCAAuC,GAAE;AAE/C,MAAM,sCAAuC,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "file": "isBuild.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/utils/isBuild.ts"], "sourcesContent": ["import { PHASE_PRODUCTION_BUILD } from 'next/constants';\n\n/**\n * Decide if the currently running process is part of the build phase or happening at runtime.\n */\nexport function isBuild(): boolean {\n  return process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD;\n}\n"], "names": ["PHASE_PRODUCTION_BUILD"], "mappings": ";;;;AAEA;;CAEA,GACO,SAAS,OAAO,GAAY;IACjC,OAAO,OAAO,CAAC,GAAG,CAAC,UAAA,KAAeA,UAAAA,sBAAsB;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "file": "distDirRewriteFramesIntegration.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/server/distDirRewriteFramesIntegration.ts"], "sourcesContent": ["import { defineIntegration, escapeStringForRegex, rewriteFramesIntegration } from '@sentry/core';\nimport * as path from 'path';\n\nexport const distDirRewriteFramesIntegration = defineIntegration(({ distDirName }: { distDirName: string }) => {\n  // nextjs always puts the build directory at the project root level, which is also where you run `next start` from, so\n  // we can read in the project directory from the currently running process\n  const distDirAbsPath = path.resolve(distDirName).replace(/(\\/|\\\\)$/, ''); // We strip trailing slashes because \"app:///_next\" also doesn't have one\n\n  // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- user input is escaped\n  const SOURCEMAP_FILENAME_REGEX = new RegExp(escapeStringForRegex(distDirAbsPath));\n\n  const rewriteFramesInstance = rewriteFramesIntegration({\n    iteratee: frame => {\n      frame.filename = frame.filename?.replace(SOURCEMAP_FILENAME_REGEX, 'app:///_next');\n      return frame;\n    },\n  });\n\n  return {\n    ...rewriteFramesInstance,\n    name: 'DistDirRewriteFrames',\n  };\n});\n"], "names": ["defineIntegration", "escapeStringForRegex", "rewriteFramesIntegration"], "mappings": ";;;;;AAGO,MAAM,+BAAgC,GAAEA,KAAAA,iBAAiB,CAAC,CAAC,EAAE,WAAA,EAAa,KAA8B;IAC/G,sHAAA;IACA,0EAAA;IACE,MAAM,cAAA,GAAiB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA,CAAA,yEAAA;IAE1E,+FAAA;IACE,MAAM,wBAAyB,GAAE,IAAI,MAAM,CAACC,KAAAA,oBAAoB,CAAC,cAAc,CAAC,CAAC;IAEjF,MAAM,qBAAA,GAAwBC,KAAAA,wBAAwB,CAAC;QACrD,QAAQ,GAAE,KAAA,IAAS;YACjB,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,wBAAwB,EAAE,cAAc,CAAC;YAClF,OAAO,KAAK;QAClB,CAAK;IACL,CAAG,CAAC;IAEF,OAAO;QACL,GAAG,qBAAqB;QACxB,IAAI,EAAE,sBAAsB;IAChC,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "file": "responseEnd.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/utils/responseEnd.ts"], "sourcesContent": ["import type { Span } from '@sentry/core';\nimport { fill, flush, logger, setHttpStatus } from '@sentry/core';\nimport type { ServerResponse } from 'http';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { ResponseEndMethod, WrappedResponseEndMethod } from '../types';\n\n/**\n * Wrap `res.end()` so that it ends the span and flushes events before letting the request finish.\n *\n * Note: This wraps a sync method with an async method. While in general that's not a great idea in terms of keeping\n * things in the right order, in this case it's safe, because the native `.end()` actually *is* (effectively) async, and\n * its run actually *is* (literally) awaited, just manually so (which reflects the fact that the core of the\n * request/response code in Node by far predates the introduction of `async`/`await`). When `.end()` is done, it emits\n * the `prefinish` event, and only once that fires does request processing continue. See\n * https://github.com/nodejs/node/commit/7c9b607048f13741173d397795bac37707405ba7.\n *\n * Also note: `res.end()` isn't called until *after* all response data and headers have been sent, so blocking inside of\n * `end` doesn't delay data getting to the end user. See\n * https://nodejs.org/api/http.html#responseenddata-encoding-callback.\n *\n * @param span The span tracking the request\n * @param res: The request's corresponding response\n */\nexport function autoEndSpanOnResponseEnd(span: Span, res: ServerResponse): void {\n  const wrapEndMethod = (origEnd: ResponseEndMethod): WrappedResponseEndMethod => {\n    return function sentryWrappedEnd(this: ServerResponse, ...args: unknown[]) {\n      finishSpan(span, this);\n      return origEnd.call(this, ...args);\n    };\n  };\n\n  // Prevent double-wrapping\n  // res.end may be undefined during build when using `next export` to statically export a Next.js app\n  if (res.end && !(res.end as WrappedResponseEndMethod).__sentry_original__) {\n    fill(res, 'end', wrapEndMethod);\n  }\n}\n\n/** Finish the given response's span and set HTTP status data */\nexport function finishSpan(span: Span, res: ServerResponse): void {\n  setHttpStatus(span, res.statusCode);\n  span.end();\n}\n\n/**\n * Flushes pending Sentry events with a 2 second timeout and in a way that cannot create unhandled promise rejections.\n */\nexport async function flushSafelyWithTimeout(): Promise<void> {\n  try {\n    DEBUG_BUILD && logger.log('Flushing events...');\n    await flush(2000);\n    DEBUG_BUILD && logger.log('Done flushing events');\n  } catch (e) {\n    DEBUG_BUILD && logger.log('Error while flushing events:\\n', e);\n  }\n}\n"], "names": ["DEBUG_BUILD", "logger", "flush"], "mappings": ";;;;;AA4CA;;CAEA,GACO,eAAe,sBAAsB,GAAkB;IAC5D,IAAI;QACFA,WAAAA,WAAAA,IAAeC,KAAAA,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAC/C,MAAMC,KAAAA,KAAK,CAAC,IAAI,CAAC;QACjBF,WAAAA,WAAAA,IAAeC,KAAAA,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC;IACrD,CAAI,CAAA,OAAO,CAAC,EAAE;QACVD,WAAAA,WAAA,IAAeC,KAAAA,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE,CAAC,CAAC;IAClE;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "file": "_error.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/_error.ts"], "sourcesContent": ["import { captureException, httpRequestToRequestData, vercelWaitUntil, withScope } from '@sentry/core';\nimport type { NextPageContext } from 'next';\nimport { flushSafelyWithTimeout } from '../utils/responseEnd';\n\ntype ContextOrProps = {\n  req?: NextPageContext['req'];\n  res?: NextPageContext['res'];\n  err?: NextPageContext['err'] | string;\n  pathname?: string;\n  statusCode?: number;\n};\n\n/**\n * Capture the exception passed by nextjs to the `_error` page, adding context data as appropriate.\n *\n * @param contextOrProps The data passed to either `getInitialProps` or `render` by nextjs\n */\nexport async function captureUnderscoreErrorException(contextOrProps: ContextOrProps): Promise<void> {\n  const { req, res, err } = contextOrProps;\n\n  // 404s (and other 400-y friends) can trigger `_error`, but we don't want to send them to Sentry\n  const statusCode = res?.statusCode || contextOrProps.statusCode;\n  if (statusCode && statusCode < 500) {\n    return Promise.resolve();\n  }\n\n  // In previous versions of the suggested `_error.js` page in which this function is meant to be used, there was a\n  // workaround for https://github.com/vercel/next.js/issues/8592 which involved an extra call to this function, in the\n  // custom error component's `render` method, just in case it hadn't been called by `getInitialProps`. Now that that\n  // issue has been fixed, the second call is unnecessary, but since it lives in user code rather than our code, users\n  // have to be the ones to get rid of it, and guaraneteedly, not all of them will. So, rather than capture the error\n  // twice, we just bail if we sense we're in that now-extraneous second call. (We can tell which function we're in\n  // because Nextjs passes `pathname` to `getInitialProps` but not to `render`.)\n  if (!contextOrProps.pathname) {\n    return Promise.resolve();\n  }\n\n  withScope(scope => {\n    if (req) {\n      const normalizedRequest = httpRequestToRequestData(req);\n      scope.setSDKProcessingMetadata({ normalizedRequest });\n    }\n\n    // If third-party libraries (or users themselves) throw something falsy, we want to capture it as a message (which\n    // is what passing a string to `captureException` will wind up doing)\n    captureException(err || `_error.js called with falsy error (${err})`, {\n      mechanism: {\n        type: 'instrument',\n        handled: false,\n        data: {\n          function: '_error.getInitialProps',\n        },\n      },\n    });\n  });\n\n  vercelWaitUntil(flushSafelyWithTimeout());\n}\n"], "names": ["withScope", "httpRequestToRequestData", "captureException", "vercelWaitUntil", "flushSafelyWithTimeout"], "mappings": ";;;;;AAYA;;;;CAIA,GACO,eAAe,+BAA+B,CAAC,cAAc,EAAiC;IACnG,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAA,EAAM,GAAE,cAAc;IAE1C,gGAAA;IACE,MAAM,aAAa,GAAG,EAAE,UAAW,IAAG,cAAc,CAAC,UAAU;IAC/D,IAAI,UAAA,IAAc,UAAW,GAAE,GAAG,EAAE;QAClC,OAAO,OAAO,CAAC,OAAO,EAAE;IAC5B;IAEA,iHAAA;IACA,qHAAA;IACA,mHAAA;IACA,oHAAA;IACA,mHAAA;IACA,iHAAA;IACA,8EAAA;IACE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QAC5B,OAAO,OAAO,CAAC,OAAO,EAAE;IAC5B;IAEEA,KAAAA,SAAS,EAAC,KAAA,IAAS;QACjB,IAAI,GAAG,EAAE;YACP,MAAM,iBAAkB,GAAEC,KAAAA,wBAAwB,CAAC,GAAG,CAAC;YACvD,KAAK,CAAC,wBAAwB,CAAC;gBAAE,iBAAA;YAAA,CAAmB,CAAC;QAC3D;QAEA,kHAAA;QACA,qEAAA;QACIC,KAAAA,gBAAgB,CAAC,GAAA,IAAO,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;YACpE,SAAS,EAAE;gBACT,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE;oBACJ,QAAQ,EAAE,wBAAwB;gBAC5C,CAAS;YACT,CAAO;QACP,CAAK,CAAC;IACN,CAAG,CAAC;IAEFC,KAAAA,eAAe,CAACC,YAAAA,sBAAsB,EAAE,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "file": "wrapperUtils.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/utils/wrapperUtils.ts"], "sourcesContent": ["import {\n  captureException,\n  getActiveSpan,\n  getCurrentScope,\n  getIsolationScope,\n  getRootSpan,\n  getTraceData,\n  httpRequestToRequestData,\n} from '@sentry/core';\nimport type { IncomingMessage, ServerResponse } from 'http';\nimport { TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL } from '../span-attributes-with-logic-attached';\n\n/**\n * Wraps a function that potentially throws. If it does, the error is passed to `captureException` and rethrown.\n *\n * Note: This function turns the wrapped function into an asynchronous one.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withErrorInstrumentation<F extends (...args: any[]) => any>(\n  origFunction: F,\n): (...params: Parameters<F>) => Promise<ReturnType<F>> {\n  return async function (this: unknown, ...origFunctionArguments: Parameters<F>): Promise<ReturnType<F>> {\n    try {\n      return await origFunction.apply(this, origFunctionArguments);\n    } catch (e) {\n      // TODO: Extract error logic from `withSentry` in here or create a new wrapper with said logic or something like that.\n      captureException(e, { mechanism: { handled: false } });\n      throw e;\n    }\n  };\n}\n\n/**\n * Calls a server-side data fetching function (that takes a `req` and `res` object in its context) with tracing\n * instrumentation. A transaction will be created for the incoming request (if it doesn't already exist) in addition to\n * a span for the wrapped data fetching function.\n *\n * All of the above happens in an isolated domain, meaning all thrown errors will be associated with the correct span.\n *\n * @param origDataFetcher The data fetching method to call.\n * @param origFunctionArguments The arguments to call the data fetching method with.\n * @param req The data fetching function's request object.\n * @param res The data fetching function's response object.\n * @param options Options providing details for the created transaction and span.\n * @returns what the data fetching method call returned.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withTracedServerSideDataFetcher<F extends (...args: any[]) => Promise<any> | any>(\n  origDataFetcher: F,\n  req: IncomingMessage,\n  res: ServerResponse,\n  options: {\n    /** Parameterized route of the request - will be used for naming the transaction. */\n    requestedRouteName: string;\n    /** Name of the route the data fetcher was defined in - will be used for describing the data fetcher's span. */\n    dataFetcherRouteName: string;\n    /** Name of the data fetching method - will be used for describing the data fetcher's span. */\n    dataFetchingMethodName: string;\n  },\n): (...params: Parameters<F>) => Promise<{ data: ReturnType<F>; sentryTrace?: string; baggage?: string }> {\n  return async function (\n    this: unknown,\n    ...args: Parameters<F>\n  ): Promise<{ data: ReturnType<F>; sentryTrace?: string; baggage?: string }> {\n    const normalizedRequest = httpRequestToRequestData(req);\n    getCurrentScope().setTransactionName(`${options.dataFetchingMethodName} (${options.dataFetcherRouteName})`);\n    getIsolationScope().setSDKProcessingMetadata({ normalizedRequest });\n\n    const span = getActiveSpan();\n\n    // Only set the route backfill if the span is not for /_error\n    if (span && options.requestedRouteName !== '/_error') {\n      const root = getRootSpan(span);\n      root.setAttribute(TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL, options.requestedRouteName);\n    }\n\n    const { 'sentry-trace': sentryTrace, baggage } = getTraceData();\n\n    return {\n      sentryTrace: sentryTrace,\n      baggage: baggage,\n      data: await origDataFetcher.apply(this, args),\n    };\n  };\n}\n\n/**\n * Call a data fetcher and trace it. Only traces the function if there is an active transaction on the scope.\n *\n * We only do the following until we move transaction creation into this function: When called, the wrapped function\n * will also update the name of the active transaction with a parameterized route provided via the `options` argument.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport async function callDataFetcherTraced<F extends (...args: any[]) => Promise<any> | any>(\n  origFunction: F,\n  origFunctionArgs: Parameters<F>,\n): Promise<ReturnType<F>> {\n  try {\n    return await origFunction(...origFunctionArgs);\n  } catch (e) {\n    captureException(e, { mechanism: { handled: false } });\n    throw e;\n  }\n}\n"], "names": ["captureException", "httpRequestToRequestData", "getCurrentScope", "getIsolationScope", "getActiveSpan", "getRootSpan", "TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL", "getTraceData"], "mappings": ";;;;;AAYA;;;;CAIA,GACA,8DAAA;AACO,SAAS,wBAAwB,CACtC,YAAY;IAEZ,OAAO,eAA+B,GAAG,qBAAqB,EAAyC;QACrG,IAAI;YACF,OAAO,MAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,qBAAqB,CAAC;QAClE,CAAM,CAAA,OAAO,CAAC,EAAE;YAChB,sHAAA;YACMA,KAAAA,gBAAgB,CAAC,CAAC,EAAE;gBAAE,SAAS,EAAE;oBAAE,OAAO,EAAE,KAAM;gBAAA,CAAA;YAAA,CAAG,CAAC;YACtD,MAAM,CAAC;QACb;IACA,CAAG;AACH;AAEA;;;;;;;;;;;;;CAaA,GACA,8DAAA;AACO,SAAS,+BAA+B,CAC7C,eAAe,EACf,GAAG,EACH,GAAG,EACH,OAAA;IASA,OAAO,eAEL,GAAG,IAAA;QAEH,MAAM,iBAAkB,GAAEC,KAAAA,wBAAwB,CAAC,GAAG,CAAC;QACvDC,KAAAA,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAA,OAAA,CAAA,sBAAA,CAAA,EAAA,EAAA,OAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA;QACAC,KAAAA,iBAAA,EAAA,CAAA,wBAAA,CAAA;YAAA,iBAAA;QAAA,CAAA,CAAA;QAEA,MAAA,IAAA,GAAAC,KAAAA,aAAA,EAAA;QAEA,6DAAA;QACA,IAAA,IAAA,IAAA,OAAA,CAAA,kBAAA,KAAA,SAAA,EAAA;YACA,MAAA,IAAA,GAAAC,KAAAA,WAAA,CAAA,IAAA,CAAA;YACA,IAAA,CAAA,YAAA,CAAAC,gCAAAA,sCAAA,EAAA,OAAA,CAAA,kBAAA,CAAA;QACA;QAEA,MAAA,EAAA,cAAA,EAAA,WAAA,EAAA,OAAA,EAAA,GAAAC,KAAAA,YAAA,EAAA;QAEA,OAAA;YACA,WAAA,EAAA,WAAA;YACA,OAAA,EAAA,OAAA;YACA,IAAA,EAAA,MAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA;AAEA;;;;;CAKA,GACA,8DAAA;AACA,eAAA,qBAAA,CACA,YAAA,EACA,gBAAA;IAEA,IAAA;QACA,OAAA,MAAA,YAAA,CAAA,GAAA,gBAAA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;QACAP,KAAAA,gBAAA,CAAA,CAAA,EAAA;YAAA,SAAA,EAAA;gBAAA,OAAA,EAAA,KAAA;YAAA,CAAA;QAAA,CAAA,CAAA;QACA,MAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1534, "column": 0}, "map": {"version": 3, "file": "wrapGetStaticPropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapGetStaticPropsWithSentry.ts"], "sourcesContent": ["import type { GetStaticProps } from 'next';\nimport { isBuild } from '../utils/isBuild';\nimport { callDataFetcherTraced, withErrorInstrumentation } from '../utils/wrapperUtils';\n\ntype Props = { [key: string]: unknown };\n\n/**\n * Create a wrapped version of the user's exported `getStaticProps` function\n *\n * @param origGetStaticProps The user's `getStaticProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapGetStaticPropsWithSentry(\n  origGetStaticPropsa: GetStaticProps<Props>,\n  _parameterizedRoute: string,\n): GetStaticProps<Props> {\n  return new Proxy(origGetStaticPropsa, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<GetStaticProps<Props>>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const errorWrappedGetStaticProps = withErrorInstrumentation(wrappingTarget);\n      return callDataFetcherTraced(errorWrappedGetStaticProps, args);\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "callDataFetcherTraced"], "mappings": ";;;;;AAMA;;;;;;CAMA,GACO,SAAS,4BAA4B,CAC1C,mBAAmB,EACnB,mBAAmB;IAEnB,OAAO,IAAI,KAAK,CAAC,mBAAmB,EAAE;QACpC,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAwC;YACjF,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,0BAA2B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAC3E,OAAOC,aAAAA,qBAAqB,CAAC,0BAA0B,EAAE,IAAI,CAAC;QACpE,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1562, "column": 0}, "map": {"version": 3, "file": "wrapGetInitialPropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapGetInitialPropsWithSentry.ts"], "sourcesContent": ["import type { NextPage } from 'next';\nimport { isBuild } from '../utils/isBuild';\nimport { withErrorInstrumentation, withTracedServerSideDataFetcher } from '../utils/wrapperUtils';\n\ntype GetInitialProps = Required<NextPage>['getInitialProps'];\n\n/**\n * Create a wrapped version of the user's exported `getInitialProps` function\n *\n * @param origGetInitialProps The user's `getInitialProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapGetInitialPropsWithSentry(origGetInitialProps: GetInitialProps): GetInitialProps {\n  return new Proxy(origGetInitialProps, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<GetInitialProps>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const [context] = args;\n      const { req, res } = context;\n\n      const errorWrappedGetInitialProps = withErrorInstrumentation(wrappingTarget);\n      // Generally we can assume that `req` and `res` are always defined on the server:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-initial-props#context-object\n      // This does not seem to be the case in dev mode. Because we have no clean way of associating the the data fetcher\n      // span with each other when there are no req or res objects, we simply do not trace them at all here.\n      if (req && res) {\n        const tracedGetInitialProps = withTracedServerSideDataFetcher(errorWrappedGetInitialProps, req, res, {\n          dataFetcherRouteName: context.pathname,\n          requestedRouteName: context.pathname,\n          dataFetchingMethodName: 'getInitialProps',\n        });\n\n        const {\n          data: initialProps,\n          baggage,\n          sentryTrace,\n        }: {\n          data?: unknown;\n          baggage?: string;\n          sentryTrace?: string;\n        } = (await tracedGetInitialProps.apply(thisArg, args)) ?? {}; // Next.js allows undefined to be returned from a getInitialPropsFunction.\n\n        if (typeof initialProps === 'object' && initialProps !== null) {\n          // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n          if (sentryTrace) {\n            (initialProps as Record<string, unknown>)._sentryTraceData = sentryTrace;\n          }\n\n          // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n          if (baggage) {\n            (initialProps as Record<string, unknown>)._sentryBaggage = baggage;\n          }\n        }\n\n        return initialProps;\n      } else {\n        return errorWrappedGetInitialProps.apply(thisArg, args);\n      }\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "withTracedServerSideDataFetcher"], "mappings": ";;;;;AAMA;;;;;;CAMA,GACO,SAAS,6BAA6B,CAAC,mBAAmB,EAAoC;IACnG,OAAO,IAAI,KAAK,CAAC,mBAAmB,EAAE;QACpC,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAkC;YAC3E,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,CAAC,OAAO,CAAA,GAAI,IAAI;YACtB,MAAM,EAAE,GAAG,EAAE,GAAI,EAAA,GAAI,OAAO;YAE5B,MAAM,2BAA4B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAClF,iFAAA;YACA,uFAAA;YACA,kHAAA;YACA,sGAAA;YACM,IAAI,GAAI,IAAG,GAAG,EAAE;gBACd,MAAM,qBAAsB,GAAEC,aAAAA,+BAA+B,CAAC,2BAA2B,EAAE,GAAG,EAAE,GAAG,EAAE;oBACnG,oBAAoB,EAAE,OAAO,CAAC,QAAQ;oBACtC,kBAAkB,EAAE,OAAO,CAAC,QAAQ;oBACpC,sBAAsB,EAAE,iBAAiB;gBACnD,CAAS,CAAC;gBAEF,MAAM,EACJ,IAAI,EAAE,YAAY,EAClB,OAAO,EACP,WAAW,EACrB,GAIY,AAAC,MAAM,qBAAqB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAK,CAAA,CAAE,CAAA,CAAA,0EAAA;gBAE5D,IAAI,OAAO,YAAa,KAAI,YAAY,YAAA,KAAiB,IAAI,EAAE;oBACvE,wFAAA;oBACU,IAAI,WAAW,EAAE;wBACd,YAAa,CAA4B,gBAAA,GAAmB,WAAW;oBACpF;oBAEA,wFAAA;oBACU,IAAI,OAAO,EAAE;wBACV,YAAa,CAA4B,cAAA,GAAiB,OAAO;oBAC9E;gBACA;gBAEQ,OAAO,YAAY;YAC3B,OAAa;gBACL,OAAO,2BAA2B,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAC/D;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "file": "wrapAppGetInitialPropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapAppGetInitialPropsWithSentry.ts"], "sourcesContent": ["import type App from 'next/app';\nimport { isBuild } from '../utils/isBuild';\nimport { withErrorInstrumentation, withTracedServerSideDataFetcher } from '../utils/wrapperUtils';\n\ntype AppGetInitialProps = (typeof App)['getInitialProps'];\n\n/**\n * Create a wrapped version of the user's exported `getInitialProps` function in\n * a custom app (\"_app.js\").\n *\n * @param origAppGetInitialProps The user's `getInitialProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapAppGetInitialPropsWithSentry(origAppGetInitialProps: AppGetInitialProps): AppGetInitialProps {\n  return new Proxy(origAppGetInitialProps, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<AppGetInitialProps>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const [context] = args;\n      const { req, res } = context.ctx;\n\n      const errorWrappedAppGetInitialProps = withErrorInstrumentation(wrappingTarget);\n\n      // Generally we can assume that `req` and `res` are always defined on the server:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-initial-props#context-object\n      // This does not seem to be the case in dev mode. Because we have no clean way of associating the the data fetcher\n      // span with each other when there are no req or res objects, we simply do not trace them at all here.\n      if (req && res) {\n        const tracedGetInitialProps = withTracedServerSideDataFetcher(errorWrappedAppGetInitialProps, req, res, {\n          dataFetcherRouteName: '/_app',\n          requestedRouteName: context.ctx.pathname,\n          dataFetchingMethodName: 'getInitialProps',\n        });\n\n        const {\n          data: appGetInitialProps,\n          sentryTrace,\n          baggage,\n        }: {\n          data?: unknown;\n          sentryTrace?: string;\n          baggage?: string;\n        } = await tracedGetInitialProps.apply(thisArg, args);\n\n        if (typeof appGetInitialProps === 'object' && appGetInitialProps !== null) {\n          // Per definition, `pageProps` is not optional, however an increased amount of users doesn't seem to call\n          // `App.getInitialProps(appContext)` in their custom `_app` pages which is required as per\n          // https://nextjs.org/docs/advanced-features/custom-app - resulting in missing `pageProps`.\n          // For this reason, we just handle the case where `pageProps` doesn't exist explicitly.\n          if (!(appGetInitialProps as Record<string, unknown>).pageProps) {\n            (appGetInitialProps as Record<string, unknown>).pageProps = {};\n          }\n\n          // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n          if (sentryTrace) {\n            (appGetInitialProps as { pageProps: Record<string, unknown> }).pageProps._sentryTraceData = sentryTrace;\n          }\n\n          // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n          if (baggage) {\n            (appGetInitialProps as { pageProps: Record<string, unknown> }).pageProps._sentryBaggage = baggage;\n          }\n        }\n\n        return appGetInitialProps;\n      } else {\n        return errorWrappedAppGetInitialProps.apply(thisArg, args);\n      }\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "withTracedServerSideDataFetcher"], "mappings": ";;;;;AAMA;;;;;;;CAOA,GACO,SAAS,gCAAgC,CAAC,sBAAsB,EAA0C;IAC/G,OAAO,IAAI,KAAK,CAAC,sBAAsB,EAAE;QACvC,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAqC;YAC9E,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,CAAC,OAAO,CAAA,GAAI,IAAI;YACtB,MAAM,EAAE,GAAG,EAAE,GAAA,EAAM,GAAE,OAAO,CAAC,GAAG;YAEhC,MAAM,8BAA+B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAErF,iFAAA;YACA,uFAAA;YACA,kHAAA;YACA,sGAAA;YACM,IAAI,GAAI,IAAG,GAAG,EAAE;gBACd,MAAM,qBAAsB,GAAEC,aAAAA,+BAA+B,CAAC,8BAA8B,EAAE,GAAG,EAAE,GAAG,EAAE;oBACtG,oBAAoB,EAAE,OAAO;oBAC7B,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;oBACxC,sBAAsB,EAAE,iBAAiB;gBACnD,CAAS,CAAC;gBAEF,MAAM,EACJ,IAAI,EAAE,kBAAkB,EACxB,WAAW,EACX,OAAO,EACjB,GAIY,MAAM,qBAAqB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBAEpD,IAAI,OAAO,kBAAmB,KAAI,YAAY,kBAAA,KAAuB,IAAI,EAAE;oBACnF,yGAAA;oBACA,0FAAA;oBACA,2FAAA;oBACA,uFAAA;oBACU,IAAI,CAAC,AAAC,mBAA+C,SAAS,EAAE;wBAC7D,kBAAmB,CAA4B,SAAU,GAAE,CAAA,CAAE;oBAC1E;oBAEA,wFAAA;oBACU,IAAI,WAAW,EAAE;wBACd,mBAA8D,SAAS,CAAC,gBAAA,GAAmB,WAAW;oBACnH;oBAEA,wFAAA;oBACU,IAAI,OAAO,EAAE;wBACV,mBAA8D,SAAS,CAAC,cAAA,GAAiB,OAAO;oBAC7G;gBACA;gBAEQ,OAAO,kBAAkB;YACjC,OAAa;gBACL,OAAO,8BAA8B,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClE;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "file": "wrapDocumentGetInitialPropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapDocumentGetInitialPropsWithSentry.ts"], "sourcesContent": ["import type Document from 'next/document';\nimport { isBuild } from '../utils/isBuild';\nimport { withErrorInstrumentation, withTracedServerSideDataFetcher } from '../utils/wrapperUtils';\n\ntype DocumentGetInitialProps = typeof Document.getInitialProps;\n\n/**\n * Create a wrapped version of the user's exported `getInitialProps` function in\n * a custom document (\"_document.js\").\n *\n * @param origDocumentGetInitialProps The user's `getInitialProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapDocumentGetInitialPropsWithSentry(\n  origDocumentGetInitialProps: DocumentGetInitialProps,\n): DocumentGetInitialProps {\n  return new Proxy(origDocumentGetInitialProps, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<DocumentGetInitialProps>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const [context] = args;\n      const { req, res } = context;\n\n      const errorWrappedGetInitialProps = withErrorInstrumentation(wrappingTarget);\n      // Generally we can assume that `req` and `res` are always defined on the server:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-initial-props#context-object\n      // This does not seem to be the case in dev mode. Because we have no clean way of associating the the data fetcher\n      // span with each other when there are no req or res objects, we simply do not trace them at all here.\n      if (req && res) {\n        const tracedGetInitialProps = withTracedServerSideDataFetcher(errorWrappedGetInitialProps, req, res, {\n          dataFetcherRouteName: '/_document',\n          requestedRouteName: context.pathname,\n          dataFetchingMethodName: 'getInitialProps',\n        });\n\n        const { data } = await tracedGetInitialProps.apply(thisArg, args);\n        return data;\n      } else {\n        return errorWrappedGetInitialProps.apply(thisArg, args);\n      }\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "withTracedServerSideDataFetcher"], "mappings": ";;;;;AAMA;;;;;;;CAOA,GACO,SAAS,qCAAqC,CACnD,2BAA2B;IAE3B,OAAO,IAAI,KAAK,CAAC,2BAA2B,EAAE;QAC5C,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAA0C;YACnF,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,CAAC,OAAO,CAAA,GAAI,IAAI;YACtB,MAAM,EAAE,GAAG,EAAE,GAAI,EAAA,GAAI,OAAO;YAE5B,MAAM,2BAA4B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAClF,iFAAA;YACA,uFAAA;YACA,kHAAA;YACA,sGAAA;YACM,IAAI,GAAI,IAAG,GAAG,EAAE;gBACd,MAAM,qBAAsB,GAAEC,aAAAA,+BAA+B,CAAC,2BAA2B,EAAE,GAAG,EAAE,GAAG,EAAE;oBACnG,oBAAoB,EAAE,YAAY;oBAClC,kBAAkB,EAAE,OAAO,CAAC,QAAQ;oBACpC,sBAAsB,EAAE,iBAAiB;gBACnD,CAAS,CAAC;gBAEF,MAAM,EAAE,IAAK,EAAA,GAAI,MAAM,qBAAqB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBACjE,OAAO,IAAI;YACnB,OAAa;gBACL,OAAO,2BAA2B,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAC/D;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "file": "wrapErrorGetInitialPropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapErrorGetInitialPropsWithSentry.ts"], "sourcesContent": ["import type { NextPageContext } from 'next';\nimport type { ErrorProps } from 'next/error';\nimport { isBuild } from '../utils/isBuild';\nimport { withErrorInstrumentation, withTracedServerSideDataFetcher } from '../utils/wrapperUtils';\n\ntype ErrorGetInitialProps = (context: NextPageContext) => Promise<ErrorProps>;\n\n/**\n * Create a wrapped version of the user's exported `getInitialProps` function in\n * a custom error page (\"_error.js\").\n *\n * @param origErrorGetInitialProps The user's `getInitialProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapErrorGetInitialPropsWithSentry(\n  origErrorGetInitialProps: ErrorGetInitialProps,\n): ErrorGetInitialProps {\n  return new Proxy(origErrorGetInitialProps, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<ErrorGetInitialProps>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const [context] = args;\n      const { req, res } = context;\n\n      const errorWrappedGetInitialProps = withErrorInstrumentation(wrappingTarget);\n      // Generally we can assume that `req` and `res` are always defined on the server:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-initial-props#context-object\n      // This does not seem to be the case in dev mode. Because we have no clean way of associating the the data fetcher\n      // span with each other when there are no req or res objects, we simply do not trace them at all here.\n      if (req && res) {\n        const tracedGetInitialProps = withTracedServerSideDataFetcher(errorWrappedGetInitialProps, req, res, {\n          dataFetcherRouteName: '/_error',\n          requestedRouteName: context.pathname,\n          dataFetchingMethodName: 'getInitialProps',\n        });\n\n        const {\n          data: errorGetInitialProps,\n          baggage,\n          sentryTrace,\n        }: {\n          data?: unknown;\n          baggage?: string;\n          sentryTrace?: string;\n        } = await tracedGetInitialProps.apply(thisArg, args);\n\n        if (typeof errorGetInitialProps === 'object' && errorGetInitialProps !== null) {\n          if (sentryTrace) {\n            // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n            (errorGetInitialProps as Record<string, unknown>)._sentryTraceData = sentryTrace;\n          }\n\n          // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n          if (baggage) {\n            (errorGetInitialProps as Record<string, unknown>)._sentryBaggage = baggage;\n          }\n        }\n\n        return errorGetInitialProps;\n      } else {\n        return errorWrappedGetInitialProps.apply(thisArg, args);\n      }\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "withTracedServerSideDataFetcher"], "mappings": ";;;;;AAOA;;;;;;;CAOA,GACO,SAAS,kCAAkC,CAChD,wBAAwB;IAExB,OAAO,IAAI,KAAK,CAAC,wBAAwB,EAAE;QACzC,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAuC;YAChF,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,CAAC,OAAO,CAAA,GAAI,IAAI;YACtB,MAAM,EAAE,GAAG,EAAE,GAAI,EAAA,GAAI,OAAO;YAE5B,MAAM,2BAA4B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAClF,iFAAA;YACA,uFAAA;YACA,kHAAA;YACA,sGAAA;YACM,IAAI,GAAI,IAAG,GAAG,EAAE;gBACd,MAAM,qBAAsB,GAAEC,aAAAA,+BAA+B,CAAC,2BAA2B,EAAE,GAAG,EAAE,GAAG,EAAE;oBACnG,oBAAoB,EAAE,SAAS;oBAC/B,kBAAkB,EAAE,OAAO,CAAC,QAAQ;oBACpC,sBAAsB,EAAE,iBAAiB;gBACnD,CAAS,CAAC;gBAEF,MAAM,EACJ,IAAI,EAAE,oBAAoB,EAC1B,OAAO,EACP,WAAW,EACrB,GAIY,MAAM,qBAAqB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBAEpD,IAAI,OAAO,oBAAqB,KAAI,YAAY,oBAAA,KAAyB,IAAI,EAAE;oBAC7E,IAAI,WAAW,EAAE;wBAC3B,wFAAA;wBACa,oBAAqB,CAA4B,gBAAA,GAAmB,WAAW;oBAC5F;oBAEA,wFAAA;oBACU,IAAI,OAAO,EAAE;wBACV,oBAAqB,CAA4B,cAAA,GAAiB,OAAO;oBACtF;gBACA;gBAEQ,OAAO,oBAAoB;YACnC,OAAa;gBACL,OAAO,2BAA2B,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAC/D;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "file": "wrapGetServerSidePropsWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapGetServerSidePropsWithSentry.ts"], "sourcesContent": ["import type { GetServerSideProps } from 'next';\nimport { isBuild } from '../utils/isBuild';\nimport { withErrorInstrumentation, withTracedServerSideDataFetcher } from '../utils/wrapperUtils';\n\n/**\n * Create a wrapped version of the user's exported `getServerSideProps` function\n *\n * @param origGetServerSideProps The user's `getServerSideProps` function\n * @param parameterizedRoute The page's parameterized route\n * @returns A wrapped version of the function\n */\nexport function wrapGetServerSidePropsWithSentry(\n  origGetServerSideProps: GetServerSideProps,\n  parameterizedRoute: string,\n): GetServerSideProps {\n  return new Proxy(origGetServerSideProps, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<GetServerSideProps>) => {\n      if (isBuild()) {\n        return wrappingTarget.apply(thisArg, args);\n      }\n\n      const [context] = args;\n      const { req, res } = context;\n\n      const errorWrappedGetServerSideProps = withErrorInstrumentation(wrappingTarget);\n      const tracedGetServerSideProps = withTracedServerSideDataFetcher(errorWrappedGetServerSideProps, req, res, {\n        dataFetcherRouteName: parameterizedRoute,\n        requestedRouteName: parameterizedRoute,\n        dataFetchingMethodName: 'getServerSideProps',\n      });\n\n      const {\n        data: serverSideProps,\n        baggage,\n        sentryTrace,\n      }: {\n        data?: unknown;\n        baggage?: string;\n        sentryTrace?: string;\n      } = await (tracedGetServerSideProps.apply(thisArg, args) as ReturnType<typeof tracedGetServerSideProps>);\n\n      if (typeof serverSideProps === 'object' && serverSideProps !== null && 'props' in serverSideProps) {\n        // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n        if (sentryTrace) {\n          (serverSideProps.props as Record<string, unknown>)._sentryTraceData = sentryTrace;\n        }\n\n        // The Next.js serializer throws on undefined values so we need to guard for it (#12102)\n        if (baggage) {\n          (serverSideProps.props as Record<string, unknown>)._sentryBaggage = baggage;\n        }\n      }\n\n      return serverSideProps;\n    },\n  });\n}\n"], "names": ["isBuild", "withErrorInstrumentation", "withTracedServerSideDataFetcher"], "mappings": ";;;;;AAIA;;;;;;CAMA,GACO,SAAS,gCAAgC,CAC9C,sBAAsB,EACtB,kBAAkB;IAElB,OAAO,IAAI,KAAK,CAAC,sBAAsB,EAAE;QACvC,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAqC;YAC9E,IAAIA,QAAAA,OAAO,EAAE,EAAE;gBACb,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClD;YAEM,MAAM,CAAC,OAAO,CAAA,GAAI,IAAI;YACtB,MAAM,EAAE,GAAG,EAAE,GAAI,EAAA,GAAI,OAAO;YAE5B,MAAM,8BAA+B,GAAEC,aAAAA,wBAAwB,CAAC,cAAc,CAAC;YAC/E,MAAM,wBAAyB,GAAEC,aAAAA,+BAA+B,CAAC,8BAA8B,EAAE,GAAG,EAAE,GAAG,EAAE;gBACzG,oBAAoB,EAAE,kBAAkB;gBACxC,kBAAkB,EAAE,kBAAkB;gBACtC,sBAAsB,EAAE,oBAAoB;YACpD,CAAO,CAAC;YAEF,MAAM,EACJ,IAAI,EAAE,eAAe,EACrB,OAAO,EACP,WAAW,EACnB,GAIU,MAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAA,EAAiD;YAExG,IAAI,OAAO,eAAA,KAAoB,QAAS,IAAG,eAAgB,KAAI,IAAK,IAAG,OAAQ,IAAG,eAAe,EAAE;gBACzG,wFAAA;gBACQ,IAAI,WAAW,EAAE;oBACd,eAAe,CAAC,KAAA,CAAkC,gBAAA,GAAmB,WAAW;gBAC3F;gBAEA,wFAAA;gBACQ,IAAI,OAAO,EAAE;oBACV,eAAe,CAAC,KAAA,CAAkC,cAAA,GAAiB,OAAO;gBACrF;YACA;YAEM,OAAO,eAAe;QAC5B,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "file": "nextNavigationErrorUtils.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/nextNavigationErrorUtils.ts"], "sourcesContent": ["import { isError } from '@sentry/core';\n\n/**\n * Determines whether input is a Next.js not-found error.\n * https://beta.nextjs.org/docs/api-reference/notfound#notfound\n */\nexport function isNotFoundNavigationError(subject: unknown): boolean {\n  return (\n    isError(subject) &&\n    ['NEXT_NOT_FOUND', 'NEXT_HTTP_ERROR_FALLBACK;404'].includes(\n      (subject as Error & { digest?: unknown }).digest as string,\n    )\n  );\n}\n\n/**\n * Determines whether input is a Next.js redirect error.\n * https://beta.nextjs.org/docs/api-reference/redirect#redirect\n */\nexport function isRedirectNavigationError(subject: unknown): boolean {\n  return (\n    isError(subject) &&\n    typeof (subject as Error & { digest?: unknown }).digest === 'string' &&\n    (subject as Error & { digest: string }).digest.startsWith('NEXT_REDIRECT;') // a redirect digest looks like \"NEXT_REDIRECT;[redirect path]\"\n  );\n}\n"], "names": ["isError"], "mappings": ";;;;AAEA;;;CAGA,GACO,SAAS,yBAAyB,CAAC,OAAO,EAAoB;IACnE,OACEA,KAAAA,OAAO,CAAC,OAAO,CAAE,IACjB;QAAC,gBAAgB;QAAE,8BAA8B;KAAC,CAAC,QAAQ,CACzD,AAAC,OAAA,CAAyC,MAAO;AAGvD;AAEA;;;CAGA,GACO,SAAS,yBAAyB,CAAC,OAAO,EAAoB;IACnE,OACEA,KAAAA,OAAO,CAAC,OAAO,CAAE,IACjB,OAAO,AAAC,OAAA,CAAyC,MAAA,KAAW,QAAS,IACrE,AAAC,QAAuC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAA,CAAA,+DAAA;;AAE9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1851, "column": 0}, "map": {"version": 3, "file": "tracingUtils.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/utils/tracingUtils.ts"], "sourcesContent": ["import type { PropagationContext } from '@sentry/core';\nimport { getActiveSpan, getRootSpan, GLOBAL_OBJ, logger, Scope, spanToJSON, startNewTrace } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION } from '../span-attributes-with-logic-attached';\n\nconst commonPropagationContextMap = new WeakMap<object, PropagationContext>();\n\n/**\n * Takes a shared (garbage collectable) object between resources, e.g. a headers object shared between Next.js server components and returns a common propagation context.\n *\n * @param commonObject The shared object.\n * @param propagationContext The propagation context that should be shared between all the resources if no propagation context was registered yet.\n * @returns the shared propagation context.\n */\nexport function commonObjectToPropagationContext(\n  commonObject: unknown,\n  propagationContext: PropagationContext,\n): PropagationContext {\n  if (typeof commonObject === 'object' && commonObject) {\n    const memoPropagationContext = commonPropagationContextMap.get(commonObject);\n    if (memoPropagationContext) {\n      return memoPropagationContext;\n    } else {\n      commonPropagationContextMap.set(commonObject, propagationContext);\n      return propagationContext;\n    }\n  } else {\n    return propagationContext;\n  }\n}\n\nconst commonIsolationScopeMap = new WeakMap<object, Scope>();\n\n/**\n * Takes a shared (garbage collectable) object between resources, e.g. a headers object shared between Next.js server components and returns a common propagation context.\n *\n * @param commonObject The shared object.\n * @param isolationScope The isolationScope that should be shared between all the resources if no isolation scope was created yet.\n * @returns the shared isolation scope.\n */\nexport function commonObjectToIsolationScope(commonObject: unknown): Scope {\n  if (typeof commonObject === 'object' && commonObject) {\n    const memoIsolationScope = commonIsolationScopeMap.get(commonObject);\n    if (memoIsolationScope) {\n      return memoIsolationScope;\n    } else {\n      const newIsolationScope = new Scope();\n      commonIsolationScopeMap.set(commonObject, newIsolationScope);\n      return newIsolationScope;\n    }\n  } else {\n    return new Scope();\n  }\n}\n\ninterface AsyncLocalStorage<T> {\n  getStore(): T | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  run<R, TArgs extends any[]>(store: T, callback: (...args: TArgs) => R, ...args: TArgs): R;\n}\n\nlet nextjsEscapedAsyncStorage: AsyncLocalStorage<true>;\n\n/**\n * Will mark the execution context of the callback as \"escaped\" from Next.js internal tracing by unsetting the active\n * span and propagation context. When an execution passes through this function multiple times, it is a noop after the\n * first time.\n */\nexport function escapeNextjsTracing<T>(cb: () => T): T {\n  const MaybeGlobalAsyncLocalStorage = (GLOBAL_OBJ as { AsyncLocalStorage?: new () => AsyncLocalStorage<true> })\n    .AsyncLocalStorage;\n\n  if (!MaybeGlobalAsyncLocalStorage) {\n    DEBUG_BUILD &&\n      logger.warn(\n        \"Tried to register AsyncLocalStorage async context strategy in a runtime that doesn't support AsyncLocalStorage.\",\n      );\n    return cb();\n  }\n\n  if (!nextjsEscapedAsyncStorage) {\n    nextjsEscapedAsyncStorage = new MaybeGlobalAsyncLocalStorage();\n  }\n\n  if (nextjsEscapedAsyncStorage.getStore()) {\n    return cb();\n  } else {\n    return startNewTrace(() => {\n      return nextjsEscapedAsyncStorage.run(true, () => {\n        return cb();\n      });\n    });\n  }\n}\n\n/**\n * Ideally this function never lands in the develop branch.\n *\n * Drops the entire span tree this function was called in, if it was a span tree created by Next.js.\n */\nexport function dropNextjsRootContext(): void {\n  const nextJsOwnedSpan = getActiveSpan();\n  if (nextJsOwnedSpan) {\n    const rootSpan = getRootSpan(nextJsOwnedSpan);\n    const rootSpanAttributes = spanToJSON(rootSpan).data;\n    if (rootSpanAttributes?.['next.span_type']) {\n      getRootSpan(nextJsOwnedSpan)?.setAttribute(TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION, true);\n    }\n  }\n}\n"], "names": ["<PERSON><PERSON>", "GLOBAL_OBJ", "DEBUG_BUILD", "logger", "startNewTrace", "getActiveSpan", "getRootSpan", "spanToJSON", "TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION"], "mappings": ";;;;;;AAKA,MAAM,2BAA4B,GAAE,IAAI,OAAO,EAA8B;AAE7E;;;;;;CAMA,GACO,SAAS,gCAAgC,CAC9C,YAAY,EACZ,kBAAkB;IAElB,IAAI,OAAO,YAAA,KAAiB,QAAS,IAAG,YAAY,EAAE;QACpD,MAAM,yBAAyB,2BAA2B,CAAC,GAAG,CAAC,YAAY,CAAC;QAC5E,IAAI,sBAAsB,EAAE;YAC1B,OAAO,sBAAsB;QACnC,OAAW;YACL,2BAA2B,CAAC,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC;YACjE,OAAO,kBAAkB;QAC/B;IACA,OAAS;QACL,OAAO,kBAAkB;IAC7B;AACA;AAEA,MAAM,uBAAwB,GAAE,IAAI,OAAO,EAAiB;AAE5D;;;;;;CAMA,GACO,SAAS,4BAA4B,CAAC,YAAY,EAAkB;IACzE,IAAI,OAAO,YAAA,KAAiB,QAAS,IAAG,YAAY,EAAE;QACpD,MAAM,qBAAqB,uBAAuB,CAAC,GAAG,CAAC,YAAY,CAAC;QACpE,IAAI,kBAAkB,EAAE;YACtB,OAAO,kBAAkB;QAC/B,OAAW;YACL,MAAM,iBAAkB,GAAE,IAAIA,KAAAA,KAAK,EAAE;YACrC,uBAAuB,CAAC,GAAG,CAAC,YAAY,EAAE,iBAAiB,CAAC;YAC5D,OAAO,iBAAiB;QAC9B;IACA,OAAS;QACL,OAAO,IAAIA,KAAAA,KAAK,EAAE;IACtB;AACA;AAQA,IAAI,yBAAyB;AAE7B;;;;CAIA,GACO,SAAS,mBAAmB,CAAI,EAAE,EAAc;IACrD,MAAM,4BAAA,GAA+B,AAACC,KAAAA,UAAW,CAC9C,iBAAiB;IAEpB,IAAI,CAAC,4BAA4B,EAAE;QACjCC,WAAAA,WAAY,IACVC,KAAAA,MAAM,CAAC,IAAI,CACT,iHAAiH;QAErH,OAAO,EAAE,EAAE;IACf;IAEE,IAAI,CAAC,yBAAyB,EAAE;QAC9B,yBAA0B,GAAE,IAAI,4BAA4B,EAAE;IAClE;IAEE,IAAI,yBAAyB,CAAC,QAAQ,EAAE,EAAE;QACxC,OAAO,EAAE,EAAE;IACf,OAAS;QACL,OAAOC,KAAAA,aAAa,CAAC,MAAM;YACzB,OAAO,yBAAyB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM;gBAC/C,OAAO,EAAE,EAAE;YACnB,CAAO,CAAC;QACR,CAAK,CAAC;IACN;AACA;AAEA;;;;CAIA,GACO,SAAS,qBAAqB,GAAS;IAC5C,MAAM,eAAA,GAAkBC,KAAAA,aAAa,EAAE;IACvC,IAAI,eAAe,EAAE;QACnB,MAAM,QAAS,GAAEC,KAAAA,WAAW,CAAC,eAAe,CAAC;QAC7C,MAAM,qBAAqBC,KAAAA,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI;QACpD,IAAI,kBAAkB,EAAA,CAAG,gBAAgB,CAAC,EAAE;YAC1CD,KAAAA,WAAW,CAAC,eAAe,CAAC,EAAE,YAAY,CAACE,gCAAAA,wCAAwC,EAAE,IAAI,CAAC;QAChG;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "file": "wrapServerComponentWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/wrapServerComponentWithSentry.ts"], "sourcesContent": ["import type { RequestEventData } from '@sentry/core';\nimport {\n  captureException,\n  getActiveSpan,\n  getCapturedScopesOnSpan,\n  getRootSpan,\n  handleCallbackErrors,\n  propagationContextFromHeaders,\n  Scope,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setCapturedScopesOnSpan,\n  SPAN_STATUS_ERROR,\n  SPAN_STATUS_OK,\n  startSpanManual,\n  vercelWaitUntil,\n  winterCGHeadersToDict,\n  withIsolationScope,\n  withScope,\n} from '@sentry/core';\nimport { isNotFoundNavigationError, isRedirectNavigationError } from '../common/nextNavigationErrorUtils';\nimport type { ServerComponentContext } from '../common/types';\nimport { TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL } from './span-attributes-with-logic-attached';\nimport { flushSafelyWithTimeout } from './utils/responseEnd';\nimport { commonObjectToIsolationScope, commonObjectToPropagationContext } from './utils/tracingUtils';\n\n/**\n * Wraps an `app` directory server component with Sentry error instrumentation.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrapServerComponentWithSentry<F extends (...args: any[]) => any>(\n  appDirComponent: F,\n  context: ServerComponentContext,\n): F {\n  const { componentRoute, componentType } = context;\n  // Even though users may define server components as async functions, for the client bundles\n  // Next.js will turn them into synchronous functions and it will transform any `await`s into instances of the `use`\n  // hook. 🤯\n  return new Proxy(appDirComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      const requestTraceId = getActiveSpan()?.spanContext().traceId;\n      const isolationScope = commonObjectToIsolationScope(context.headers);\n\n      const activeSpan = getActiveSpan();\n      if (activeSpan) {\n        const rootSpan = getRootSpan(activeSpan);\n        const { scope } = getCapturedScopesOnSpan(rootSpan);\n        setCapturedScopesOnSpan(rootSpan, scope ?? new Scope(), isolationScope);\n      }\n\n      const headersDict = context.headers ? winterCGHeadersToDict(context.headers) : undefined;\n\n      isolationScope.setSDKProcessingMetadata({\n        normalizedRequest: {\n          headers: headersDict,\n        } satisfies RequestEventData,\n      });\n\n      return withIsolationScope(isolationScope, () => {\n        return withScope(scope => {\n          scope.setTransactionName(`${componentType} Server Component (${componentRoute})`);\n\n          if (process.env.NEXT_RUNTIME === 'edge') {\n            const propagationContext = commonObjectToPropagationContext(\n              context.headers,\n              propagationContextFromHeaders(headersDict?.['sentry-trace'], headersDict?.['baggage']),\n            );\n\n            if (requestTraceId) {\n              propagationContext.traceId = requestTraceId;\n            }\n\n            scope.setPropagationContext(propagationContext);\n          }\n\n          const activeSpan = getActiveSpan();\n          if (activeSpan) {\n            const rootSpan = getRootSpan(activeSpan);\n            const sentryTrace = headersDict?.['sentry-trace'];\n            if (sentryTrace) {\n              rootSpan.setAttribute(TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL, sentryTrace);\n            }\n          }\n\n          return startSpanManual(\n            {\n              op: 'function.nextjs',\n              name: `${componentType} Server Component (${componentRoute})`,\n              attributes: {\n                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'component',\n                [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.nextjs',\n                'sentry.nextjs.ssr.function.type': componentType,\n                'sentry.nextjs.ssr.function.route': componentRoute,\n              },\n            },\n            span => {\n              return handleCallbackErrors(\n                () => originalFunction.apply(thisArg, args),\n                error => {\n                  // When you read this code you might think: \"Wait a minute, shouldn't we set the status on the root span too?\"\n                  // The answer is: \"No.\" - The status of the root span is determined by whatever status code Next.js decides to put on the response.\n                  if (isNotFoundNavigationError(error)) {\n                    // We don't want to report \"not-found\"s\n                    span.setStatus({ code: SPAN_STATUS_ERROR, message: 'not_found' });\n                  } else if (isRedirectNavigationError(error)) {\n                    // We don't want to report redirects\n                    span.setStatus({ code: SPAN_STATUS_OK });\n                  } else {\n                    span.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n                    captureException(error, {\n                      mechanism: {\n                        handled: false,\n                      },\n                    });\n                  }\n                },\n                () => {\n                  span.end();\n                  vercelWaitUntil(flushSafelyWithTimeout());\n                },\n              );\n            },\n          );\n        });\n      });\n    },\n  });\n}\n"], "names": ["getActiveSpan", "commonObjectToIsolationScope", "getRootSpan", "getCapturedScopesOnSpan", "setCapturedScopesOnSpan", "<PERSON><PERSON>", "winterCGHeadersToDict", "withIsolationScope", "withScope", "commonObjectToPropagationContext", "propagationContextFromHeaders", "TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL", "startSpanManual", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "handleCallbackErrors", "isNotFoundNavigationError", "SPAN_STATUS_ERROR", "isRedirectNavigationError", "SPAN_STATUS_OK", "captureException", "vercelWaitUntil", "flushSafelyWithTimeout"], "mappings": ";;;;;;;;AA0BA;;CAEA,GACA,8DAAA;AACO,SAAS,6BAA6B,CAC3C,eAAe,EACf,OAAO;IAEP,MAAM,EAAE,cAAc,EAAE,aAAc,EAAA,GAAI,OAAO;IACnD,4FAAA;IACA,mHAAA;IACA,WAAA;IACE,OAAO,IAAI,KAAK,CAAC,eAAe,EAAE;QAChC,KAAK,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,KAAK;YAC1C,MAAM,cAAe,GAAEA,KAAAA,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO;YAC7D,MAAM,iBAAiBC,aAAAA,4BAA4B,CAAC,OAAO,CAAC,OAAO,CAAC;YAEpE,MAAM,UAAA,GAAaD,KAAAA,aAAa,EAAE;YAClC,IAAI,UAAU,EAAE;gBACd,MAAM,QAAS,GAAEE,KAAAA,WAAW,CAAC,UAAU,CAAC;gBACxC,MAAM,EAAE,KAAM,EAAA,GAAIC,KAAAA,uBAAuB,CAAC,QAAQ,CAAC;gBACnDC,KAAAA,uBAAuB,CAAC,QAAQ,EAAE,KAAM,IAAG,IAAIC,KAAAA,KAAK,EAAE,EAAE,cAAc,CAAC;YAC/E;YAEM,MAAM,WAAA,GAAc,OAAO,CAAC,OAAQ,GAAEC,KAAAA,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAA,GAAI,SAAS;YAExF,cAAc,CAAC,wBAAwB,CAAC;gBACtC,iBAAiB,EAAE;oBACjB,OAAO,EAAE,WAAW;gBAC9B,CAAU;YACV,CAAO,CAAC;YAEF,OAAOC,KAAAA,kBAAkB,CAAC,cAAc,EAAE,MAAM;gBAC9C,OAAOC,KAAAA,SAAS,EAAC,KAAA,IAAS;oBACxB,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAA,aAAA,CAAA,mBAAA,EAAA,cAAA,CAAA,CAAA,CAAA,CAAA;oBAEA,IAAA,OAAA,CAAA,GAAA,CAAA,YAAA,KAAA,MAAA;;oBAWA;oBAEA,MAAA,UAAA,GAAAR,KAAAA,aAAA,EAAA;oBACA,IAAA,UAAA,EAAA;wBACA,MAAA,QAAA,GAAAE,KAAAA,WAAA,CAAA,UAAA,CAAA;wBACA,MAAA,WAAA,GAAA,WAAA,EAAA,CAAA,cAAA,CAAA;wBACA,IAAA,WAAA,EAAA;4BACA,QAAA,CAAA,YAAA,CAAAS,gCAAAA,sCAAA,EAAA,WAAA,CAAA;wBACA;oBACA;oBAEA,OAAAC,KAAAA,eAAA,CACA;wBACA,EAAA,EAAA,iBAAA;wBACA,IAAA,EAAA,CAAA,EAAA,aAAA,CAAA,mBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;wBACA,UAAA,EAAA;4BACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,WAAA;4BACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,sBAAA;4BACA,iCAAA,EAAA,aAAA;4BACA,kCAAA,EAAA,cAAA;wBACA,CAAA;oBACA,CAAA,GACA,IAAA,IAAA;wBACA,OAAAC,KAAAA,oBAAA,CACA,IAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GACA,KAAA,IAAA;4BACA,8GAAA;4BACA,mIAAA;4BACA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA;gCACA,uCAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAC,KAAAA,iBAAA;oCAAA,OAAA,EAAA,WAAA;gCAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA;gCACA,oCAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAC,KAAAA,cAAA;gCAAA,CAAA,CAAA;4BACA,CAAA,MAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAF,KAAAA,iBAAA;oCAAA,OAAA,EAAA,gBAAA;gCAAA,CAAA,CAAA;gCACAG,KAAAA,gBAAA,CAAA,KAAA,EAAA;oCACA,SAAA,EAAA;wCACA,OAAA,EAAA,KAAA;oCACA,CAAA;gCACA,CAAA,CAAA;4BACA;wBACA,CAAA,EACA,MAAA;4BACA,IAAA,CAAA,GAAA,EAAA;4BACAC,KAAAA,eAAA,CAAAC,YAAAA,sBAAA,EAAA,CAAA;wBACA,CAAA;oBAEA,CAAA;gBAEA,CAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2042, "column": 0}, "map": {"version": 3, "file": "wrapRouteHandlerWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/wrapRouteHandlerWithSentry.ts"], "sourcesContent": ["import type { RequestEventData } from '@sentry/core';\nimport {\n  captureException,\n  getActiveSpan,\n  getCapturedScopesOnSpan,\n  getIsolationScope,\n  getRootSpan,\n  handleCallbackErrors,\n  propagationContextFromHeaders,\n  Scope,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setCapturedScopesOnSpan,\n  setHttpStatus,\n  winterCGHeadersToDict,\n  withIsolationScope,\n  withScope,\n} from '@sentry/core';\nimport { isNotFoundNavigationError, isRedirectNavigationError } from './nextNavigationErrorUtils';\nimport type { RouteHandlerContext } from './types';\nimport { commonObjectToIsolationScope } from './utils/tracingUtils';\n\n/**\n * Wraps a Next.js App Router Route handler with Sentry error and performance instrumentation.\n *\n * NOTICE: This wrapper is for App Router API routes. If you are looking to wrap Pages Router API routes use `wrapApiHandlerWithSentry` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrapRouteHandlerWithSentry<F extends (...args: any[]) => any>(\n  routeHandler: F,\n  context: RouteHandlerContext,\n): (...args: Parameters<F>) => ReturnType<F> extends Promise<unknown> ? ReturnType<F> : Promise<ReturnType<F>> {\n  const { method, parameterizedRoute, headers } = context;\n\n  return new Proxy(routeHandler, {\n    apply: async (originalFunction, thisArg, args) => {\n      const activeSpan = getActiveSpan();\n      const rootSpan = activeSpan ? getRootSpan(activeSpan) : undefined;\n\n      let edgeRuntimeIsolationScopeOverride: Scope | undefined;\n      if (rootSpan && process.env.NEXT_RUNTIME === 'edge') {\n        const isolationScope = commonObjectToIsolationScope(headers);\n        const { scope } = getCapturedScopesOnSpan(rootSpan);\n        setCapturedScopesOnSpan(rootSpan, scope ?? new Scope(), isolationScope);\n\n        edgeRuntimeIsolationScopeOverride = isolationScope;\n\n        rootSpan.updateName(`${method} ${parameterizedRoute}`);\n        rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'route');\n        rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'http.server');\n      }\n\n      return withIsolationScope(\n        process.env.NEXT_RUNTIME === 'edge' ? edgeRuntimeIsolationScopeOverride : getIsolationScope(),\n        () => {\n          return withScope(async scope => {\n            scope.setTransactionName(`${method} ${parameterizedRoute}`);\n\n            if (process.env.NEXT_RUNTIME === 'edge') {\n              const completeHeadersDict: Record<string, string> = headers ? winterCGHeadersToDict(headers) : {};\n              const incomingPropagationContext = propagationContextFromHeaders(\n                completeHeadersDict['sentry-trace'],\n                completeHeadersDict['baggage'],\n              );\n              scope.setPropagationContext(incomingPropagationContext);\n              scope.setSDKProcessingMetadata({\n                normalizedRequest: {\n                  method,\n                  headers: completeHeadersDict,\n                } satisfies RequestEventData,\n              });\n            }\n\n            const response: Response = await handleCallbackErrors(\n              () => originalFunction.apply(thisArg, args),\n              error => {\n                // Next.js throws errors when calling `redirect()`. We don't wanna report these.\n                if (isRedirectNavigationError(error)) {\n                  // Don't do anything\n                } else if (isNotFoundNavigationError(error)) {\n                  if (activeSpan) {\n                    setHttpStatus(activeSpan, 404);\n                  }\n                  if (rootSpan) {\n                    setHttpStatus(rootSpan, 404);\n                  }\n                } else {\n                  captureException(error, {\n                    mechanism: {\n                      handled: false,\n                    },\n                  });\n                }\n              },\n            );\n\n            try {\n              if (response.status) {\n                if (activeSpan) {\n                  setHttpStatus(activeSpan, response.status);\n                }\n                if (rootSpan) {\n                  setHttpStatus(rootSpan, response.status);\n                }\n              }\n            } catch {\n              // best effort - response may be undefined?\n            }\n\n            return response;\n          });\n        },\n      );\n    },\n  });\n}\n"], "names": ["getActiveSpan", "getRootSpan", "commonObjectToIsolationScope", "getCapturedScopesOnSpan", "setCapturedScopesOnSpan", "<PERSON><PERSON>", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "withIsolationScope", "getIsolationScope", "withScope", "winterCGHeadersToDict", "propagationContextFromHeaders", "handleCallbackErrors", "isRedirectNavigationError", "isNotFoundNavigationError", "setHttpStatus", "captureException"], "mappings": ";;;;;;AAsBA;;;;CAIA,GACA,8DAAA;AACO,SAAS,0BAA0B,CACxC,YAAY,EACZ,OAAO;IAEP,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAA,EAAU,GAAE,OAAO;IAEvD,OAAO,IAAI,KAAK,CAAC,YAAY,EAAE;QAC7B,KAAK,EAAE,OAAO,gBAAgB,EAAE,OAAO,EAAE,IAAI,KAAK;YAChD,MAAM,UAAA,GAAaA,KAAAA,aAAa,EAAE;YAClC,MAAM,QAAS,GAAE,UAAW,GAAEC,KAAAA,WAAW,CAAC,UAAU,CAAE,GAAE,SAAS;YAEjE,IAAI,iCAAiC;YACrC,IAAI,QAAA,IAAY,OAAO,CAAC,GAAG,CAAC,WAAyB,CAAzB,KAAiB,MAAM;;YAU5B;YAEA,OAAAO,KAAAA,kBAAA,CACA,OAAA,CAAA,GAAA,CAAA,YAAA,KAAA,MAAA,GAAA,iCAAA,MAAAC,KAAAA,iBAAA,EAAA,EACA,MAAA;gBACA,OAAAC,KAAAA,SAAA,CAAA,OAAA,KAAA,IAAA;oBACA,KAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;oBAEA,IAAA,OAAA,CAAA,GAAA,CAAA,YAAA,KAAA,MAAA;;oBAaA;oBAEA,MAAA,QAAA,GAAA,MAAAG,KAAAA,oBAAA,CACA,IAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GACA,KAAA,IAAA;wBACA,gFAAA;wBACA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA,CAEA;6BAAA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA;4BACA,IAAA,UAAA,EAAA;gCACAC,KAAAA,aAAA,CAAA,UAAA,EAAA,GAAA,CAAA;4BACA;4BACA,IAAA,QAAA,EAAA;gCACAA,KAAAA,aAAA,CAAA,QAAA,EAAA,GAAA,CAAA;4BACA;wBACA,CAAA,MAAA;4BACAC,KAAAA,gBAAA,CAAA,KAAA,EAAA;gCACA,SAAA,EAAA;oCACA,OAAA,EAAA,KAAA;gCACA,CAAA;4BACA,CAAA,CAAA;wBACA;oBACA,CAAA;oBAGA,IAAA;wBACA,IAAA,QAAA,CAAA,MAAA,EAAA;4BACA,IAAA,UAAA,EAAA;gCACAD,KAAAA,aAAA,CAAA,UAAA,EAAA,QAAA,CAAA,MAAA,CAAA;4BACA;4BACA,IAAA,QAAA,EAAA;gCACAA,KAAAA,aAAA,CAAA,QAAA,EAAA,QAAA,CAAA,MAAA,CAAA;4BACA;wBACA;oBACA,CAAA,CAAA,OAAA;oBACA,2CAAA;oBACA;oBAEA,OAAA,QAAA;gBACA,CAAA,CAAA;YACA,CAAA;QAEA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2111, "column": 0}, "map": {"version": 3, "file": "wrapApiHandlerWithSentryVercelCrons.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapApiHandlerWithSentryVercelCrons.ts"], "sourcesContent": ["import { captureCheckIn } from '@sentry/core';\nimport type { NextApiRequest } from 'next';\nimport type { VercelCronsConfig } from '../types';\n\ntype EdgeRequest = {\n  nextUrl: URL;\n  headers: Headers;\n};\n\n/**\n * Wraps a function with Sentry crons instrumentation by automatically sending check-ins for the given Vercel crons config.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrapApiHandlerWithSentryVercelCrons<F extends (...args: any[]) => any>(\n  handler: F,\n  vercelCronsConfig: VercelCronsConfig,\n): F {\n  return new Proxy(handler, {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    apply: (originalFunction, thisArg, args: any[]) => {\n      if (!args?.[0]) {\n        return originalFunction.apply(thisArg, args);\n      }\n\n      const [req] = args as [NextApiRequest | EdgeRequest];\n\n      let maybePromiseResult;\n      const cronsKey = 'nextUrl' in req ? req.nextUrl.pathname : req.url;\n      const userAgentHeader = 'nextUrl' in req ? req.headers.get('user-agent') : req.headers['user-agent'];\n\n      if (\n        !vercelCronsConfig || // do nothing if vercel crons config is missing\n        !userAgentHeader?.includes('vercel-cron') // do nothing if endpoint is not called from vercel crons\n      ) {\n        return originalFunction.apply(thisArg, args);\n      }\n\n      const vercelCron = vercelCronsConfig.find(vercelCron => vercelCron.path === cronsKey);\n\n      if (!vercelCron?.path || !vercelCron.schedule) {\n        return originalFunction.apply(thisArg, args);\n      }\n\n      const monitorSlug = vercelCron.path;\n\n      const checkInId = captureCheckIn(\n        {\n          monitorSlug,\n          status: 'in_progress',\n        },\n        {\n          maxRuntime: 60 * 12, // (minutes) so 12 hours - just a very high arbitrary number since we don't know the actual duration of the users cron job\n          schedule: {\n            type: 'crontab',\n            value: vercelCron.schedule,\n          },\n        },\n      );\n\n      const startTime = Date.now() / 1000;\n\n      const handleErrorCase = (): void => {\n        captureCheckIn({\n          checkInId,\n          monitorSlug,\n          status: 'error',\n          duration: Date.now() / 1000 - startTime,\n        });\n      };\n\n      try {\n        maybePromiseResult = originalFunction.apply(thisArg, args);\n      } catch (e) {\n        handleErrorCase();\n        throw e;\n      }\n\n      if (typeof maybePromiseResult === 'object' && maybePromiseResult !== null && 'then' in maybePromiseResult) {\n        Promise.resolve(maybePromiseResult).then(\n          () => {\n            captureCheckIn({\n              checkInId,\n              monitorSlug,\n              status: 'ok',\n              duration: Date.now() / 1000 - startTime,\n            });\n          },\n          () => {\n            handleErrorCase();\n          },\n        );\n\n        // It is very important that we return the original promise here, because Next.js attaches various properties\n        // to that promise and will throw if they are not on the returned value.\n        return maybePromiseResult;\n      } else {\n        captureCheckIn({\n          checkInId,\n          monitorSlug,\n          status: 'ok',\n          duration: Date.now() / 1000 - startTime,\n        });\n        return maybePromiseResult;\n      }\n    },\n  });\n}\n"], "names": ["captureCheckIn"], "mappings": ";;;;AASA;;CAEA,GACA,8DAAA;AACO,SAAS,mCAAmC,CACjD,OAAO,EACP,iBAAiB;IAEjB,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE;QAC5B,8DAAA;QACI,KAAK,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,KAAY;YACjD,IAAI,CAAC,IAAI,EAAA,CAAG,CAAC,CAAC,EAAE;gBACd,OAAO,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YACpD;YAEM,MAAM,CAAC,GAAG,CAAA,GAAI,IAAK;YAEnB,IAAI,kBAAkB;YACtB,MAAM,QAAA,GAAW,SAAA,IAAa,GAAI,GAAE,GAAG,CAAC,OAAO,CAAC,QAAA,GAAW,GAAG,CAAC,GAAG;YAClE,MAAM,kBAAkB,SAAA,IAAa,GAAI,GAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAE,GAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YAEpG,IACE,CAAC,iBAAkB,IAAA,+CAAA;YACnB,CAAC,eAAe,EAAE,QAAQ,CAAC,aAAa,CAAA,CAAA,yDAAA;cACxC;gBACA,OAAO,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YACpD;YAEM,MAAM,UAAA,GAAa,iBAAiB,CAAC,IAAI,EAAC,UAAW,GAAG,UAAU,CAAC,IAAK,KAAI,QAAQ,CAAC;YAErF,IAAI,CAAC,UAAU,EAAE,IAAK,IAAG,CAAC,UAAU,CAAC,QAAQ,EAAE;gBAC7C,OAAO,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YACpD;YAEM,MAAM,WAAA,GAAc,UAAU,CAAC,IAAI;YAEnC,MAAM,SAAU,GAAEA,KAAAA,cAAc,CAC9B;gBACE,WAAW;gBACX,MAAM,EAAE,aAAa;YAC/B,CAAS,EACD;gBACE,UAAU,EAAE,EAAG,GAAE,EAAE;gBACnB,QAAQ,EAAE;oBACR,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,UAAU,CAAC,QAAQ;gBACtC,CAAW;YACX,CAAS;YAGH,MAAM,YAAY,IAAI,CAAC,GAAG,EAAG,GAAE,IAAI;YAEnC,MAAM,eAAA,GAAkB,MAAY;gBAClCA,KAAAA,cAAc,CAAC;oBACb,SAAS;oBACT,WAAW;oBACX,MAAM,EAAE,OAAO;oBACf,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAG,GAAE,IAAK,GAAE,SAAS;gBACjD,CAAS,CAAC;YACV,CAAO;YAED,IAAI;gBACF,kBAAA,GAAqB,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAClE,CAAQ,CAAA,OAAO,CAAC,EAAE;gBACV,eAAe,EAAE;gBACjB,MAAM,CAAC;YACf;YAEM,IAAI,OAAO,kBAAA,KAAuB,QAAS,IAAG,kBAAmB,KAAI,IAAK,IAAG,MAAO,IAAG,kBAAkB,EAAE;gBACzG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CACtC,MAAM;oBACJA,KAAAA,cAAc,CAAC;wBACb,SAAS;wBACT,WAAW;wBACX,MAAM,EAAE,IAAI;wBACZ,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAG,GAAE,IAAK,GAAE,SAAS;oBACrD,CAAa,CAAC;gBACd,CAAW,EACD,MAAM;oBACJ,eAAe,EAAE;gBAC7B,CAAW;gBAGX,6GAAA;gBACA,wEAAA;gBACQ,OAAO,kBAAkB;YACjC,OAAa;gBACLA,KAAAA,cAAc,CAAC;oBACb,SAAS;oBACT,WAAW;oBACX,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAG,GAAE,IAAK,GAAE,SAAS;gBACjD,CAAS,CAAC;gBACF,OAAO,kBAAkB;YACjC;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "file": "wrapMiddlewareWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/wrapMiddlewareWithSentry.ts"], "sourcesContent": ["import type { TransactionSource } from '@sentry/core';\nimport {\n  captureException,\n  getActiveSpan,\n  getCurrentScope,\n  getRootSpan,\n  handleCallbackErrors,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setCapturedScopesOnSpan,\n  startSpan,\n  vercelWaitUntil,\n  winterCGRequestToRequestData,\n  withIsolationScope,\n} from '@sentry/core';\nimport type { EdgeRouteHandler } from '../edge/types';\nimport { flushSafelyWithTimeout } from './utils/responseEnd';\n\n/**\n * Wraps Next.js middleware with Sentry error and performance instrumentation.\n *\n * @param middleware The middleware handler.\n * @returns a wrapped middleware handler.\n */\nexport function wrapMiddlewareWithSentry<H extends EdgeRouteHandler>(\n  middleware: H,\n): (...params: Parameters<H>) => Promise<ReturnType<H>> {\n  return new Proxy(middleware, {\n    apply: async (wrappingTarget, thisArg, args: Parameters<H>) => {\n      // TODO: We still should add central isolation scope creation for when our build-time instrumentation does not work anymore with turbopack.\n      return withIsolationScope(isolationScope => {\n        const req: unknown = args[0];\n        const currentScope = getCurrentScope();\n\n        let spanName: string;\n        let spanSource: TransactionSource;\n\n        if (req instanceof Request) {\n          isolationScope.setSDKProcessingMetadata({\n            normalizedRequest: winterCGRequestToRequestData(req),\n          });\n          spanName = `middleware ${req.method} ${new URL(req.url).pathname}`;\n          spanSource = 'url';\n        } else {\n          spanName = 'middleware';\n          spanSource = 'component';\n        }\n\n        currentScope.setTransactionName(spanName);\n\n        const activeSpan = getActiveSpan();\n\n        if (activeSpan) {\n          // If there is an active span, it likely means that the automatic Next.js OTEL instrumentation worked and we can\n          // rely on that for parameterization.\n          spanName = 'middleware';\n          spanSource = 'component';\n\n          const rootSpan = getRootSpan(activeSpan);\n          if (rootSpan) {\n            setCapturedScopesOnSpan(rootSpan, currentScope, isolationScope);\n          }\n        }\n\n        return startSpan(\n          {\n            name: spanName,\n            op: 'http.server.middleware',\n            attributes: {\n              [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: spanSource,\n              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.nextjs.wrapMiddlewareWithSentry',\n            },\n          },\n          () => {\n            return handleCallbackErrors(\n              () => wrappingTarget.apply(thisArg, args),\n              error => {\n                captureException(error, {\n                  mechanism: {\n                    type: 'instrument',\n                    handled: false,\n                  },\n                });\n              },\n              () => {\n                vercelWaitUntil(flushSafelyWithTimeout());\n              },\n            );\n          },\n        );\n      });\n    },\n  });\n}\n"], "names": ["withIsolationScope", "getCurrentScope", "winterCGRequestToRequestData", "getActiveSpan", "getRootSpan", "setCapturedScopesOnSpan", "startSpan", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "handleCallbackErrors", "captureException", "vercelWaitUntil", "flushSafelyWithTimeout"], "mappings": ";;;;;AAkBA;;;;;CAKA,GACO,SAAS,wBAAwB,CACtC,UAAU;IAEV,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE;QAC3B,KAAK,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,IAAI,KAAoB;YACnE,2IAAA;YACM,OAAOA,KAAAA,kBAAkB,EAAC,cAAA,IAAkB;gBAC1C,MAAM,GAAG,GAAY,IAAI,CAAC,CAAC,CAAC;gBAC5B,MAAM,YAAA,GAAeC,KAAAA,eAAe,EAAE;gBAEtC,IAAI,QAAQ;gBACZ,IAAI,UAAU;gBAEd,IAAI,GAAI,YAAW,OAAO,EAAE;oBAC1B,cAAc,CAAC,wBAAwB,CAAC;wBACtC,iBAAiB,EAAEC,KAAAA,4BAA4B,CAAC,GAAG,CAAC;oBAChE,CAAW,CAAC;oBACF,QAAA,GAAW,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;oBACA,UAAA,GAAA,KAAA;gBACA,CAAA,MAAA;oBACA,QAAA,GAAA,YAAA;oBACA,UAAA,GAAA,WAAA;gBACA;gBAEA,YAAA,CAAA,kBAAA,CAAA,QAAA,CAAA;gBAEA,MAAA,UAAA,GAAAC,KAAAA,aAAA,EAAA;gBAEA,IAAA,UAAA,EAAA;oBACA,gHAAA;oBACA,qCAAA;oBACA,QAAA,GAAA,YAAA;oBACA,UAAA,GAAA,WAAA;oBAEA,MAAA,QAAA,GAAAC,KAAAA,WAAA,CAAA,UAAA,CAAA;oBACA,IAAA,QAAA,EAAA;wBACAC,KAAAA,uBAAA,CAAA,QAAA,EAAA,YAAA,EAAA,cAAA,CAAA;oBACA;gBACA;gBAEA,OAAAC,KAAAA,SAAA,CACA;oBACA,IAAA,EAAA,QAAA;oBACA,EAAA,EAAA,wBAAA;oBACA,UAAA,EAAA;wBACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,UAAA;wBACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,+CAAA;oBACA,CAAA;gBACA,CAAA,EACA,MAAA;oBACA,OAAAC,KAAAA,oBAAA,CACA,IAAA,cAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GACA,KAAA,IAAA;wBACAC,KAAAA,gBAAA,CAAA,KAAA,EAAA;4BACA,SAAA,EAAA;gCACA,IAAA,EAAA,YAAA;gCACA,OAAA,EAAA,KAAA;4BACA,CAAA;wBACA,CAAA,CAAA;oBACA,CAAA,EACA,MAAA;wBACAC,KAAAA,eAAA,CAAAC,YAAAA,sBAAA,EAAA,CAAA;oBACA,CAAA;gBAEA,CAAA;YAEA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2266, "column": 0}, "map": {"version": 3, "file": "wrapPageComponentWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapPageComponentWithSentry.ts"], "sourcesContent": ["import { captureException, extractTraceparentData, getCurrentScope, withIsolationScope } from '@sentry/core';\n\ninterface FunctionComponent {\n  (...args: unknown[]): unknown;\n}\n\ninterface ClassComponent {\n  new (...args: unknown[]): {\n    props?: unknown;\n    render(...args: unknown[]): unknown;\n  };\n}\n\nfunction isReactClassComponent(target: unknown): target is ClassComponent {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return typeof target === 'function' && target?.prototype?.isReactComponent;\n}\n\n/**\n * Wraps a page component with Sentry error instrumentation.\n */\nexport function wrapPageComponentWithSentry(pageComponent: FunctionComponent | ClassComponent): unknown {\n  if (isReactClassComponent(pageComponent)) {\n    return class SentryWrappedPageComponent extends pageComponent {\n      public render(...args: unknown[]): unknown {\n        return withIsolationScope(() => {\n          const scope = getCurrentScope();\n          // We extract the sentry trace data that is put in the component props by datafetcher wrappers\n          const sentryTraceData =\n            typeof this.props === 'object' &&\n            this.props !== null &&\n            '_sentryTraceData' in this.props &&\n            typeof this.props._sentryTraceData === 'string'\n              ? this.props._sentryTraceData\n              : undefined;\n\n          if (sentryTraceData) {\n            const traceparentData = extractTraceparentData(sentryTraceData);\n            scope.setContext('trace', {\n              span_id: traceparentData?.parentSpanId,\n              trace_id: traceparentData?.traceId,\n            });\n          }\n\n          try {\n            return super.render(...args);\n          } catch (e) {\n            captureException(e, {\n              mechanism: {\n                handled: false,\n              },\n            });\n            throw e;\n          }\n        });\n      }\n    };\n  } else if (typeof pageComponent === 'function') {\n    return new Proxy(pageComponent, {\n      apply(target, thisArg, argArray: [{ _sentryTraceData?: string } | undefined]) {\n        return withIsolationScope(() => {\n          const scope = getCurrentScope();\n          // We extract the sentry trace data that is put in the component props by datafetcher wrappers\n          const sentryTraceData = argArray?.[0]?._sentryTraceData;\n\n          if (sentryTraceData) {\n            const traceparentData = extractTraceparentData(sentryTraceData);\n            scope.setContext('trace', {\n              span_id: traceparentData?.parentSpanId,\n              trace_id: traceparentData?.traceId,\n            });\n          }\n\n          try {\n            return target.apply(thisArg, argArray);\n          } catch (e) {\n            captureException(e, {\n              mechanism: {\n                handled: false,\n              },\n            });\n            throw e;\n          }\n        });\n      },\n    });\n  } else {\n    return pageComponent;\n  }\n}\n"], "names": ["withIsolationScope", "getCurrentScope", "extractTraceparentData", "captureException"], "mappings": ";;;;AAaA,SAAS,qBAAqB,CAAC,MAAM,EAAqC;IAC1E,sEAAA;IACE,OAAO,OAAO,MAAA,KAAW,UAAA,IAAc,MAAM,EAAE,SAAS,EAAE,gBAAgB;AAC5E;AAEA;;CAEA,GACO,SAAS,2BAA2B,CAAC,aAAa,EAA+C;IACtG,IAAI,qBAAqB,CAAC,aAAa,CAAC,EAAE;QACxC,OAAO,MAAM,0BAA2B,SAAQ,aAAc,CAAA;YACrD,MAAM,CAAC,GAAG,IAAI,EAAsB;gBACzC,OAAOA,KAAAA,kBAAkB,CAAC,MAAM;oBAC9B,MAAM,KAAA,GAAQC,KAAAA,eAAe,EAAE;oBACzC,8FAAA;oBACU,MAAM,eAAgB,GACpB,OAAO,IAAI,CAAC,KAAA,KAAU,QAAS,IAC/B,IAAI,CAAC,KAAM,KAAI,IAAK,IACpB,kBAAmB,IAAG,IAAI,CAAC,KAAM,IACjC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAA,KAAqB,WACnC,IAAI,CAAC,KAAK,CAAC,gBAAA,GACX,SAAS;oBAEf,IAAI,eAAe,EAAE;wBACnB,MAAM,eAAgB,GAAEC,KAAAA,sBAAsB,CAAC,eAAe,CAAC;wBAC/D,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;4BACxB,OAAO,EAAE,eAAe,EAAE,YAAY;4BACtC,QAAQ,EAAE,eAAe,EAAE,OAAO;wBAChD,CAAa,CAAC;oBACd;oBAEU,IAAI;wBACF,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;oBACxC,CAAY,CAAA,OAAO,CAAC,EAAE;wBACVC,KAAAA,gBAAgB,CAAC,CAAC,EAAE;4BAClB,SAAS,EAAE;gCACT,OAAO,EAAE,KAAK;4BAC9B,CAAe;wBACf,CAAa,CAAC;wBACF,MAAM,CAAC;oBACnB;gBACA,CAAS,CAAC;YACV;QACA,CAAK;IACL,CAAE,MAAO,IAAI,OAAO,aAAc,KAAI,UAAU,EAAE;QAC9C,OAAO,IAAI,KAAK,CAAC,aAAa,EAAE;YAC9B,KAAK,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAA+C;gBAC5E,OAAOH,KAAAA,kBAAkB,CAAC,MAAM;oBAC9B,MAAM,KAAA,GAAQC,KAAAA,eAAe,EAAE;oBACzC,8FAAA;oBACU,MAAM,kBAAkB,QAAQ,EAAA,CAAG,CAAC,CAAC,EAAE,gBAAgB;oBAEvD,IAAI,eAAe,EAAE;wBACnB,MAAM,eAAgB,GAAEC,KAAAA,sBAAsB,CAAC,eAAe,CAAC;wBAC/D,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;4BACxB,OAAO,EAAE,eAAe,EAAE,YAAY;4BACtC,QAAQ,EAAE,eAAe,EAAE,OAAO;wBAChD,CAAa,CAAC;oBACd;oBAEU,IAAI;wBACF,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;oBAClD,CAAY,CAAA,OAAO,CAAC,EAAE;wBACVC,KAAAA,gBAAgB,CAAC,CAAC,EAAE;4BAClB,SAAS,EAAE;gCACT,OAAO,EAAE,KAAK;4BAC9B,CAAe;wBACf,CAAa,CAAC;wBACF,MAAM,CAAC;oBACnB;gBACA,CAAS,CAAC;YACV,CAAO;QACP,CAAK,CAAC;IACN,OAAS;QACL,OAAO,aAAa;IACxB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2341, "column": 0}, "map": {"version": 3, "file": "wrapGenerationFunctionWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/wrapGenerationFunctionWithSentry.ts"], "sourcesContent": ["import type { RequestEventD<PERSON>, WebFetchHeaders } from '@sentry/core';\nimport {\n  captureException,\n  getActiveSpan,\n  getCapturedScopesOnSpan,\n  getClient,\n  getRootSpan,\n  handleCallbackErrors,\n  propagationContextFromHeaders,\n  Scope,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setCapturedScopesOnSpan,\n  SPAN_STATUS_ERROR,\n  SPAN_STATUS_OK,\n  startSpanManual,\n  winterCGHeadersToDict,\n  withIsolationScope,\n  withScope,\n} from '@sentry/core';\nimport type { GenerationFunctionContext } from '../common/types';\nimport { isNotFoundNavigationError, isRedirectNavigationError } from './nextNavigationErrorUtils';\nimport { TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL } from './span-attributes-with-logic-attached';\nimport { commonObjectToIsolationScope, commonObjectToPropagationContext } from './utils/tracingUtils';\n\n/**\n * Wraps a generation function (e.g. generateMetadata) with Sentry error and performance instrumentation.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrapGenerationFunctionWithSentry<F extends (...args: any[]) => any>(\n  generationFunction: F,\n  context: GenerationFunctionContext,\n): F {\n  const { requestAsyncStorage, componentRoute, componentType, generationFunctionIdentifier } = context;\n  return new Proxy(generationFunction, {\n    apply: (originalFunction, thisArg, args) => {\n      const requestTraceId = getActiveSpan()?.spanContext().traceId;\n      let headers: WebFetchHeaders | undefined = undefined;\n      // We try-catch here just in case anything goes wrong with the async storage here goes wrong since it is Next.js internal API\n      try {\n        headers = requestAsyncStorage?.getStore()?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      const isolationScope = commonObjectToIsolationScope(headers);\n\n      const activeSpan = getActiveSpan();\n      if (activeSpan) {\n        const rootSpan = getRootSpan(activeSpan);\n        const { scope } = getCapturedScopesOnSpan(rootSpan);\n        setCapturedScopesOnSpan(rootSpan, scope ?? new Scope(), isolationScope);\n      }\n\n      let data: Record<string, unknown> | undefined = undefined;\n      if (getClient()?.getOptions().sendDefaultPii) {\n        const props: unknown = args[0];\n        const params = props && typeof props === 'object' && 'params' in props ? props.params : undefined;\n        const searchParams =\n          props && typeof props === 'object' && 'searchParams' in props ? props.searchParams : undefined;\n        data = { params, searchParams };\n      }\n\n      const headersDict = headers ? winterCGHeadersToDict(headers) : undefined;\n\n      return withIsolationScope(isolationScope, () => {\n        return withScope(scope => {\n          scope.setTransactionName(`${componentType}.${generationFunctionIdentifier} (${componentRoute})`);\n\n          isolationScope.setSDKProcessingMetadata({\n            normalizedRequest: {\n              headers: headersDict,\n            } satisfies RequestEventData,\n          });\n\n          const activeSpan = getActiveSpan();\n          if (activeSpan) {\n            const rootSpan = getRootSpan(activeSpan);\n            const sentryTrace = headersDict?.['sentry-trace'];\n            if (sentryTrace) {\n              rootSpan.setAttribute(TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL, sentryTrace);\n            }\n          }\n\n          const propagationContext = commonObjectToPropagationContext(\n            headers,\n            propagationContextFromHeaders(headersDict?.['sentry-trace'], headersDict?.['baggage']),\n          );\n\n          if (requestTraceId) {\n            propagationContext.traceId = requestTraceId;\n          }\n\n          scope.setPropagationContext(propagationContext);\n\n          scope.setExtra('route_data', data);\n\n          return startSpanManual(\n            {\n              op: 'function.nextjs',\n              name: `${componentType}.${generationFunctionIdentifier} (${componentRoute})`,\n              attributes: {\n                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n                [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.nextjs',\n                'sentry.nextjs.ssr.function.type': generationFunctionIdentifier,\n                'sentry.nextjs.ssr.function.route': componentRoute,\n              },\n            },\n            span => {\n              return handleCallbackErrors(\n                () => originalFunction.apply(thisArg, args),\n                err => {\n                  // When you read this code you might think: \"Wait a minute, shouldn't we set the status on the root span too?\"\n                  // The answer is: \"No.\" - The status of the root span is determined by whatever status code Next.js decides to put on the response.\n                  if (isNotFoundNavigationError(err)) {\n                    // We don't want to report \"not-found\"s\n                    span.setStatus({ code: SPAN_STATUS_ERROR, message: 'not_found' });\n                    getRootSpan(span).setStatus({ code: SPAN_STATUS_ERROR, message: 'not_found' });\n                  } else if (isRedirectNavigationError(err)) {\n                    // We don't want to report redirects\n                    span.setStatus({ code: SPAN_STATUS_OK });\n                  } else {\n                    span.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n                    getRootSpan(span).setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n                    captureException(err, {\n                      mechanism: {\n                        handled: false,\n                      },\n                    });\n                  }\n                },\n                () => {\n                  span.end();\n                },\n              );\n            },\n          );\n        });\n      });\n    },\n  });\n}\n"], "names": ["getActiveSpan", "commonObjectToIsolationScope", "getRootSpan", "getCapturedScopesOnSpan", "setCapturedScopesOnSpan", "<PERSON><PERSON>", "getClient", "winterCGHeadersToDict", "withIsolationScope", "withScope", "TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL", "commonObjectToPropagationContext", "propagationContextFromHeaders", "startSpanManual", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "handleCallbackErrors", "isNotFoundNavigationError", "SPAN_STATUS_ERROR", "isRedirectNavigationError", "SPAN_STATUS_OK", "captureException"], "mappings": ";;;;;;;AAyBA;;CAEA,GACA,8DAAA;AACO,SAAS,gCAAgC,CAC9C,kBAAkB,EAClB,OAAO;IAEP,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,aAAa,EAAE,4BAAA,EAA+B,GAAE,OAAO;IACpG,OAAO,IAAI,KAAK,CAAC,kBAAkB,EAAE;QACnC,KAAK,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,KAAK;YAC1C,MAAM,cAAe,GAAEA,KAAAA,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO;YAC7D,IAAI,OAAO,GAAgC,SAAS;YAC1D,6HAAA;YACM,IAAI;gBACF,OAAA,GAAU,mBAAmB,EAAE,QAAQ,EAAE,EAAE,OAAO;YAC1D,CAAQ,CAAA,OAAO,CAAC,EAAE;YAClB,UAAA,GACA;YAEM,MAAM,cAAe,GAAEC,aAAAA,4BAA4B,CAAC,OAAO,CAAC;YAE5D,MAAM,UAAA,GAAaD,KAAAA,aAAa,EAAE;YAClC,IAAI,UAAU,EAAE;gBACd,MAAM,QAAS,GAAEE,KAAAA,WAAW,CAAC,UAAU,CAAC;gBACxC,MAAM,EAAE,KAAM,EAAA,GAAIC,KAAAA,uBAAuB,CAAC,QAAQ,CAAC;gBACnDC,KAAAA,uBAAuB,CAAC,QAAQ,EAAE,KAAM,IAAG,IAAIC,KAAAA,KAAK,EAAE,EAAE,cAAc,CAAC;YAC/E;YAEM,IAAI,IAAI,GAAwC,SAAS;YACzD,IAAIC,KAAAA,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,cAAc,EAAE;gBAC5C,MAAM,KAAK,GAAY,IAAI,CAAC,CAAC,CAAC;gBAC9B,MAAM,MAAO,GAAE,SAAS,OAAO,UAAU,QAAA,IAAY,QAAA,IAAY,KAAM,GAAE,KAAK,CAAC,MAAA,GAAS,SAAS;gBACjG,MAAM,YAAa,GACjB,KAAM,IAAG,OAAO,KAAA,KAAU,QAAS,IAAG,cAAe,IAAG,QAAQ,KAAK,CAAC,YAAA,GAAe,SAAS;gBAChG,OAAO;oBAAE,MAAM;oBAAE;gBAAA,CAAc;YACvC;YAEM,MAAM,WAAY,GAAE,OAAQ,GAAEC,KAAAA,qBAAqB,CAAC,OAAO,CAAE,GAAE,SAAS;YAExE,OAAOC,KAAAA,kBAAkB,CAAC,cAAc,EAAE,MAAM;gBAC9C,OAAOC,KAAAA,SAAS,EAAC,KAAA,IAAS;oBACxB,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAA,aAAA,CAAA,CAAA,EAAA,4BAAA,CAAA,EAAA,EAAA,cAAA,CAAA,CAAA,CAAA,CAAA;oBAEA,cAAA,CAAA,wBAAA,CAAA;wBACA,iBAAA,EAAA;4BACA,OAAA,EAAA,WAAA;wBACA,CAAA;oBACA,CAAA,CAAA;oBAEA,MAAA,UAAA,GAAAT,KAAAA,aAAA,EAAA;oBACA,IAAA,UAAA,EAAA;wBACA,MAAA,QAAA,GAAAE,KAAAA,WAAA,CAAA,UAAA,CAAA;wBACA,MAAA,WAAA,GAAA,WAAA,EAAA,CAAA,cAAA,CAAA;wBACA,IAAA,WAAA,EAAA;4BACA,QAAA,CAAA,YAAA,CAAAQ,gCAAAA,sCAAA,EAAA,WAAA,CAAA;wBACA;oBACA;oBAEA,MAAA,kBAAA,GAAAC,aAAAA,gCAAA,CACA,OAAA,EACAC,KAAAA,6BAAA,CAAA,WAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;oBAGA,IAAA,cAAA,EAAA;wBACA,kBAAA,CAAA,OAAA,GAAA,cAAA;oBACA;oBAEA,KAAA,CAAA,qBAAA,CAAA,kBAAA,CAAA;oBAEA,KAAA,CAAA,QAAA,CAAA,YAAA,EAAA,IAAA,CAAA;oBAEA,OAAAC,KAAAA,eAAA,CACA;wBACA,EAAA,EAAA,iBAAA;wBACA,IAAA,EAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,4BAAA,CAAA,EAAA,EAAA,cAAA,CAAA,CAAA,CAAA;wBACA,UAAA,EAAA;4BACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,OAAA;4BACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,sBAAA;4BACA,iCAAA,EAAA,4BAAA;4BACA,kCAAA,EAAA,cAAA;wBACA,CAAA;oBACA,CAAA,GACA,IAAA,IAAA;wBACA,OAAAC,KAAAA,oBAAA,CACA,IAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GACA,GAAA,IAAA;4BACA,8GAAA;4BACA,mIAAA;4BACA,IAAAC,yBAAAA,yBAAA,CAAA,GAAA,CAAA,EAAA;gCACA,uCAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAC,KAAAA,iBAAA;oCAAA,OAAA,EAAA,WAAA;gCAAA,CAAA,CAAA;gCACAhB,KAAAA,WAAA,CAAA,IAAA,CAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAgB,KAAAA,iBAAA;oCAAA,OAAA,EAAA,WAAA;gCAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IAAAC,yBAAAA,yBAAA,CAAA,GAAA,CAAA,EAAA;gCACA,oCAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAC,KAAAA,cAAA;gCAAA,CAAA,CAAA;4BACA,CAAA,MAAA;gCACA,IAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAF,KAAAA,iBAAA;oCAAA,OAAA,EAAA,gBAAA;gCAAA,CAAA,CAAA;gCACAhB,KAAAA,WAAA,CAAA,IAAA,CAAA,CAAA,SAAA,CAAA;oCAAA,IAAA,EAAAgB,KAAAA,iBAAA;oCAAA,OAAA,EAAA,gBAAA;gCAAA,CAAA,CAAA;gCACAG,KAAAA,gBAAA,CAAA,GAAA,EAAA;oCACA,SAAA,EAAA;wCACA,OAAA,EAAA,KAAA;oCACA,CAAA;gCACA,CAAA,CAAA;4BACA;wBACA,CAAA,EACA,MAAA;4BACA,IAAA,CAAA,GAAA,EAAA;wBACA,CAAA;oBAEA,CAAA;gBAEA,CAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "file": "withServerActionInstrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/withServerActionInstrumentation.ts"], "sourcesContent": ["import type { RequestEventData } from '@sentry/core';\nimport {\n  captureException,\n  continueTrace,\n  getActiveSpan,\n  getClient,\n  getIsolationScope,\n  handleCallbackErrors,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  SPAN_STATUS_ERROR,\n  startSpan,\n  vercelWaitUntil,\n  withIsolationScope,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from './debug-build';\nimport { isNotFoundNavigationError, isRedirectNavigationError } from './nextNavigationErrorUtils';\nimport { flushSafelyWithTimeout } from './utils/responseEnd';\n\ninterface Options {\n  formData?: FormData;\n\n  /**\n   * Headers as returned from `headers()`.\n   *\n   * Currently accepts both a plain `Headers` object and `Promise<ReadonlyHeaders>` to be compatible with async APIs introduced in Next.js 15: https://github.com/vercel/next.js/pull/68812\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  headers?: Headers | Promise<any>;\n\n  /**\n   * Whether the server action response should be included in any events captured within the server action.\n   */\n  recordResponse?: boolean;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withServerActionInstrumentation<A extends (...args: any[]) => any>(\n  serverActionName: string,\n  callback: A,\n): Promise<ReturnType<A>>;\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withServerActionInstrumentation<A extends (...args: any[]) => any>(\n  serverActionName: string,\n  options: Options,\n  callback: A,\n): Promise<ReturnType<A>>;\n\n/**\n * Wraps a Next.js Server Action implementation with Sentry Error and Performance instrumentation.\n */\nexport function withServerActionInstrumentation<A extends (...args: unknown[]) => unknown>(\n  ...args: [string, Options, A] | [string, A]\n): Promise<ReturnType<A>> {\n  if (typeof args[1] === 'function') {\n    const [serverActionName, callback] = args;\n    return withServerActionInstrumentationImplementation(serverActionName, {}, callback);\n  } else {\n    const [serverActionName, options, callback] = args;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return withServerActionInstrumentationImplementation(serverActionName, options, callback!);\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function withServerActionInstrumentationImplementation<A extends (...args: any[]) => any>(\n  serverActionName: string,\n  options: Options,\n  callback: A,\n): Promise<ReturnType<A>> {\n  return withIsolationScope(async isolationScope => {\n    const sendDefaultPii = getClient()?.getOptions().sendDefaultPii;\n\n    let sentryTraceHeader;\n    let baggageHeader;\n    const fullHeadersObject: Record<string, string> = {};\n    try {\n      const awaitedHeaders: Headers = await options.headers;\n      sentryTraceHeader = awaitedHeaders?.get('sentry-trace') ?? undefined;\n      baggageHeader = awaitedHeaders?.get('baggage');\n      awaitedHeaders?.forEach((value, key) => {\n        fullHeadersObject[key] = value;\n      });\n    } catch (e) {\n      DEBUG_BUILD &&\n        logger.warn(\n          \"Sentry wasn't able to extract the tracing headers for a server action. Will not trace this request.\",\n        );\n    }\n\n    isolationScope.setTransactionName(`serverAction/${serverActionName}`);\n    isolationScope.setSDKProcessingMetadata({\n      normalizedRequest: {\n        headers: fullHeadersObject,\n      } satisfies RequestEventData,\n    });\n\n    // Normally, there is an active span here (from Next.js OTEL) and we just use that as parent\n    // Else, we manually continueTrace from the incoming headers\n    const continueTraceIfNoActiveSpan = getActiveSpan()\n      ? <T>(_opts: unknown, callback: () => T) => callback()\n      : continueTrace;\n\n    return continueTraceIfNoActiveSpan(\n      {\n        sentryTrace: sentryTraceHeader,\n        baggage: baggageHeader,\n      },\n      async () => {\n        try {\n          return await startSpan(\n            {\n              op: 'function.server_action',\n              name: `serverAction/${serverActionName}`,\n              forceTransaction: true,\n              attributes: {\n                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n              },\n            },\n            async span => {\n              const result = await handleCallbackErrors(callback, error => {\n                if (isNotFoundNavigationError(error)) {\n                  // We don't want to report \"not-found\"s\n                  span.setStatus({ code: SPAN_STATUS_ERROR, message: 'not_found' });\n                } else if (isRedirectNavigationError(error)) {\n                  // Don't do anything for redirects\n                } else {\n                  span.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n                  captureException(error, {\n                    mechanism: {\n                      handled: false,\n                    },\n                  });\n                }\n              });\n\n              if (options.recordResponse !== undefined ? options.recordResponse : sendDefaultPii) {\n                getIsolationScope().setExtra('server_action_result', result);\n              }\n\n              if (options.formData) {\n                options.formData.forEach((value, key) => {\n                  getIsolationScope().setExtra(\n                    `server_action_form_data.${key}`,\n                    typeof value === 'string' ? value : '[non-string value]',\n                  );\n                });\n              }\n\n              return result;\n            },\n          );\n        } finally {\n          vercelWaitUntil(flushSafelyWithTimeout());\n        }\n      },\n    );\n  });\n}\n"], "names": ["withIsolationScope", "getClient", "DEBUG_BUILD", "logger", "getActiveSpan", "continueTrace", "startSpan", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "handleCallbackErrors", "isNotFoundNavigationError", "SPAN_STATUS_ERROR", "isRedirectNavigationError", "captureException", "getIsolationScope", "vercelWaitUntil", "flushSafelyWithTimeout"], "mappings": ";;;;;;;AAiDA;;CAEA,GACO,SAAS,+BAA+B,CAC7C,GAAG,IAAA;IAEH,IAAI,OAAO,IAAI,CAAC,CAAC,CAAA,KAAM,UAAU,EAAE;QACjC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAA,GAAI,IAAI;QACzC,OAAO,6CAA6C,CAAC,gBAAgB,EAAE,CAAA,CAAE,EAAE,QAAQ,CAAC;IACxF,OAAS;QACL,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,CAAE,GAAE,IAAI;QACtD,oEAAA;QACI,OAAO,6CAA6C,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,CAAE;IAC9F;AACA;AAEA,8DAAA;AACA,eAAe,6CAA6C,CAC1D,gBAAgB,EAChB,OAAO,EACP,QAAQ;IAER,OAAOA,KAAAA,kBAAkB,CAAC,OAAM,kBAAkB;QAChD,MAAM,cAAe,GAAEC,KAAAA,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,cAAc;QAE/D,IAAI,iBAAiB;QACrB,IAAI,aAAa;QACjB,MAAM,iBAAiB,GAA2B,CAAA,CAAE;QACpD,IAAI;YACF,MAAM,cAAc,GAAY,MAAM,OAAO,CAAC,OAAO;YACrD,iBAAA,GAAoB,cAAc,EAAE,GAAG,CAAC,cAAc,CAAE,IAAG,SAAS;YACpE,gBAAgB,cAAc,EAAE,GAAG,CAAC,SAAS,CAAC;YAC9C,cAAc,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;gBACtC,iBAAiB,CAAC,GAAG,CAAA,GAAI,KAAK;YACtC,CAAO,CAAC;QACR,CAAM,CAAA,OAAO,CAAC,EAAE;YACVC,WAAAA,WAAY,IACVC,KAAAA,MAAM,CAAC,IAAI,CACT,qGAAqG;QAE/G;QAEI,cAAc,CAAC,kBAAkB,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAA,CAAA;QACA,cAAA,CAAA,wBAAA,CAAA;YACA,iBAAA,EAAA;gBACA,OAAA,EAAA,iBAAA;YACA,CAAA;QACA,CAAA,CAAA;QAEA,4FAAA;QACA,4DAAA;QACA,MAAA,2BAAA,GAAAC,KAAAA,aAAA,KACA,CAAA,KAAA,EAAA,QAAA,GAAA,QAAA,KACAC,KAAAA,aAAA;QAEA,OAAA,2BAAA,CACA;YACA,WAAA,EAAA,iBAAA;YACA,OAAA,EAAA,aAAA;QACA,CAAA,EACA,YAAA;YACA,IAAA;gBACA,OAAA,MAAAC,KAAAA,SAAA,CACA;oBACA,EAAA,EAAA,wBAAA;oBACA,IAAA,EAAA,CAAA,aAAA,EAAA,gBAAA,CAAA,CAAA;oBACA,gBAAA,EAAA,IAAA;oBACA,UAAA,EAAA;wBACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,OAAA;oBACA,CAAA;gBACA,CAAA,EACA,OAAA,IAAA,IAAA;oBACA,MAAA,MAAA,GAAA,MAAAC,KAAAA,oBAAA,CAAA,QAAA,GAAA,KAAA,IAAA;wBACA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA;4BACA,uCAAA;4BACA,IAAA,CAAA,SAAA,CAAA;gCAAA,IAAA,EAAAC,KAAAA,iBAAA;gCAAA,OAAA,EAAA,WAAA;4BAAA,CAAA,CAAA;wBACA,CAAA,MAAA,IAAAC,yBAAAA,yBAAA,CAAA,KAAA,CAAA,EAAA;wBACA,kCAAA;wBACA,CAAA,MAAA;4BACA,IAAA,CAAA,SAAA,CAAA;gCAAA,IAAA,EAAAD,KAAAA,iBAAA;gCAAA,OAAA,EAAA,gBAAA;4BAAA,CAAA,CAAA;4BACAE,KAAAA,gBAAA,CAAA,KAAA,EAAA;gCACA,SAAA,EAAA;oCACA,OAAA,EAAA,KAAA;gCACA,CAAA;4BACA,CAAA,CAAA;wBACA;oBACA,CAAA,CAAA;oBAEA,IAAA,OAAA,CAAA,cAAA,KAAA,SAAA,GAAA,OAAA,CAAA,cAAA,GAAA,cAAA,EAAA;wBACAC,KAAAA,iBAAA,EAAA,CAAA,QAAA,CAAA,sBAAA,EAAA,MAAA,CAAA;oBACA;oBAEA,IAAA,OAAA,CAAA,QAAA,EAAA;wBACA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA,KAAA,EAAA,GAAA,KAAA;4BACAA,KAAAA,iBAAA,EAAA,CAAA,QAAA,CACA,CAAA,wBAAA,EAAA,GAAA,CAAA,CAAA,EACA,OAAA,KAAA,KAAA,QAAA,GAAA,KAAA,GAAA,oBAAA;wBAEA,CAAA,CAAA;oBACA;oBAEA,OAAA,MAAA;gBACA,CAAA;YAEA,CAAA,QAAA;gBACAC,KAAAA,eAAA,CAAAC,YAAAA,sBAAA,EAAA,CAAA;YACA;QACA,CAAA;IAEA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2561, "column": 0}, "map": {"version": 3, "file": "captureRequestError.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/captureRequestError.ts"], "sourcesContent": ["import type { RequestEventData } from '@sentry/core';\nimport { captureException, headersToDict, vercelWaitUntil, withScope } from '@sentry/core';\nimport { flushSafelyWithTimeout } from './utils/responseEnd';\n\ntype RequestInfo = {\n  path: string;\n  method: string;\n  headers: Record<string, string | string[] | undefined>;\n};\n\ntype ErrorContext = {\n  routerKind: string; // 'Pages Router' | 'App Router'\n  routePath: string;\n  routeType: string; // 'render' | 'route' | 'middleware'\n};\n\n/**\n * Reports errors passed to the the Next.js `onRequestError` instrumentation hook.\n */\nexport function captureRequestError(error: unknown, request: RequestInfo, errorContext: ErrorContext): void {\n  withScope(scope => {\n    scope.setSDKProcessingMetadata({\n      normalizedRequest: {\n        headers: headersToDict(request.headers),\n        method: request.method,\n      } satisfies RequestEventData,\n    });\n\n    scope.setContext('nextjs', {\n      request_path: request.path,\n      router_kind: errorContext.routerKind,\n      router_path: errorContext.routePath,\n      route_type: errorContext.routeType,\n    });\n\n    scope.setTransactionName(errorContext.routePath);\n\n    captureException(error, {\n      mechanism: {\n        handled: false,\n      },\n    });\n\n    vercelWaitUntil(flushSafelyWithTimeout());\n  });\n}\n"], "names": ["withScope", "headersToDict", "captureException", "vercelWaitUntil", "flushSafelyWithTimeout"], "mappings": ";;;;;AAgBA;;CAEA,GACO,SAAS,mBAAmB,CAAC,KAAK,EAAW,OAAO,EAAe,YAAY,EAAsB;IAC1GA,KAAAA,SAAS,EAAC,KAAA,IAAS;QACjB,KAAK,CAAC,wBAAwB,CAAC;YAC7B,iBAAiB,EAAE;gBACjB,OAAO,EAAEC,KAAAA,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvC,MAAM,EAAE,OAAO,CAAC,MAAM;YAC9B,CAAQ;QACR,CAAK,CAAC;QAEF,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE;YACzB,YAAY,EAAE,OAAO,CAAC,IAAI;YAC1B,WAAW,EAAE,YAAY,CAAC,UAAU;YACpC,WAAW,EAAE,YAAY,CAAC,SAAS;YACnC,UAAU,EAAE,YAAY,CAAC,SAAS;QACxC,CAAK,CAAC;QAEF,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC;QAEhDC,KAAAA,gBAAgB,CAAC,KAAK,EAAE;YACtB,SAAS,EAAE;gBACT,OAAO,EAAE,KAAK;YACtB,CAAO;QACP,CAAK,CAAC;QAEFC,KAAAA,eAAe,CAACC,YAAAA,sBAAsB,EAAE,CAAC;IAC7C,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "file": "wrapApiHandlerWithSentry.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/common/pages-router-instrumentation/wrapApiHandlerWithSentry.ts"], "sourcesContent": ["import {\n  captureException,\n  continueTrace,\n  getActiveSpan,\n  httpRequestToRequestData,\n  isString,\n  logger,\n  objectify,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setHttpStatus,\n  startSpanManual,\n  vercelWaitUntil,\n  withIsolationScope,\n} from '@sentry/core';\nimport type { NextApiRequest } from 'next';\nimport type { AugmentedNextApiResponse, NextApiHandler } from '../types';\nimport { flushSafelyWithTimeout } from '../utils/responseEnd';\nimport { dropNextjsRootContext, escapeNextjsTracing } from '../utils/tracingUtils';\n\nexport type AugmentedNextApiRequest = NextApiRequest & {\n  __withSentry_applied__?: boolean;\n};\n\n/**\n * Wrap the given API route handler with error nad performance monitoring.\n *\n * @param apiHandler The handler exported from the user's API page route file, which may or may not already be\n * wrapped with `withSentry`\n * @param parameterizedRoute The page's parameterized route.\n * @returns The wrapped handler which will always return a Promise.\n */\nexport function wrapApiHandlerWithSentry(apiHandler: NextApiHandler, parameterizedRoute: string): NextApiHandler {\n  return new Proxy(apiHandler, {\n    apply: (\n      wrappingTarget,\n      thisArg,\n      args: [AugmentedNextApiRequest | undefined, AugmentedNextApiResponse | undefined],\n    ) => {\n      dropNextjsRootContext();\n      return escapeNextjsTracing(() => {\n        const [req, res] = args;\n\n        if (!req) {\n          logger.debug(\n            `Wrapped API handler on route \"${parameterizedRoute}\" was not passed a request object. Will not instrument.`,\n          );\n          return wrappingTarget.apply(thisArg, args);\n        } else if (!res) {\n          logger.debug(\n            `Wrapped API handler on route \"${parameterizedRoute}\" was not passed a response object. Will not instrument.`,\n          );\n          return wrappingTarget.apply(thisArg, args);\n        }\n\n        // Prevent double wrapping of the same request.\n        if (req.__withSentry_applied__) {\n          return wrappingTarget.apply(thisArg, args);\n        }\n        req.__withSentry_applied__ = true;\n\n        return withIsolationScope(isolationScope => {\n          // Normally, there is an active span here (from Next.js OTEL) and we just use that as parent\n          // Else, we manually continueTrace from the incoming headers\n          const continueTraceIfNoActiveSpan = getActiveSpan()\n            ? <T>(_opts: unknown, callback: () => T) => callback()\n            : continueTrace;\n\n          return continueTraceIfNoActiveSpan(\n            {\n              sentryTrace:\n                req.headers && isString(req.headers['sentry-trace']) ? req.headers['sentry-trace'] : undefined,\n              baggage: req.headers?.baggage,\n            },\n            () => {\n              const reqMethod = `${(req.method || 'GET').toUpperCase()} `;\n              const normalizedRequest = httpRequestToRequestData(req);\n\n              isolationScope.setSDKProcessingMetadata({ normalizedRequest });\n              isolationScope.setTransactionName(`${reqMethod}${parameterizedRoute}`);\n\n              return startSpanManual(\n                {\n                  name: `${reqMethod}${parameterizedRoute}`,\n                  op: 'http.server',\n                  forceTransaction: true,\n                  attributes: {\n                    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n                    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.nextjs',\n                  },\n                },\n                async span => {\n                  // eslint-disable-next-line @typescript-eslint/unbound-method\n                  res.end = new Proxy(res.end, {\n                    apply(target, thisArg, argArray) {\n                      setHttpStatus(span, res.statusCode);\n                      span.end();\n                      vercelWaitUntil(flushSafelyWithTimeout());\n                      return target.apply(thisArg, argArray);\n                    },\n                  });\n                  try {\n                    return await wrappingTarget.apply(thisArg, args);\n                  } catch (e) {\n                    // In case we have a primitive, wrap it in the equivalent wrapper class (string -> String, etc.) so that we can\n                    // store a seen flag on it. (Because of the one-way-on-Vercel-one-way-off-of-Vercel approach we've been forced\n                    // to take, it can happen that the same thrown object gets caught in two different ways, and flagging it is a\n                    // way to prevent it from actually being reported twice.)\n                    const objectifiedErr = objectify(e);\n\n                    captureException(objectifiedErr, {\n                      mechanism: {\n                        type: 'instrument',\n                        handled: false,\n                        data: {\n                          wrapped_handler: wrappingTarget.name,\n                          function: 'withSentry',\n                        },\n                      },\n                    });\n\n                    setHttpStatus(span, 500);\n                    span.end();\n\n                    // we need to await the flush here to ensure that the error is captured\n                    // as the runtime freezes as soon as the error is thrown below\n                    await flushSafelyWithTimeout();\n\n                    // We rethrow here so that nextjs can do with the error whatever it would normally do. (Sometimes \"whatever it\n                    // would normally do\" is to allow the error to bubble up to the global handlers - another reason we need to mark\n                    // the error as already having been captured.)\n                    throw objectifiedErr;\n                  }\n                },\n              );\n            },\n          );\n        });\n      });\n    },\n  });\n}\n"], "names": ["dropNextjsRootContext", "escapeNextjsTracing", "logger", "withIsolationScope", "getActiveSpan", "continueTrace", "isString", "httpRequestToRequestData", "startSpanManual", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "setHttpStatus", "vercelWaitUntil", "flushSafelyWithTimeout", "objectify", "captureException"], "mappings": ";;;;;;AAwBA;;;;;;;CAOA,GACO,SAAS,wBAAwB,CAAC,UAAU,EAAkB,kBAAkB,EAA0B;IAC/G,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE;QAC3B,KAAK,EAAE,CACL,cAAc,EACd,OAAO,EACP,IAAI;YAEJA,aAAAA,qBAAqB,EAAE;YACvB,OAAOC,aAAAA,mBAAmB,CAAC,MAAM;gBAC/B,MAAM,CAAC,GAAG,EAAE,GAAG,CAAA,GAAI,IAAI;gBAEvB,IAAI,CAAC,GAAG,EAAE;oBACRC,KAAAA,MAAM,CAAC,KAAK,CACV,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,uDAAuD,CAAC;oBAE9G,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBACpD,OAAe,IAAI,CAAC,GAAG,EAAE;oBACfA,KAAAA,MAAM,CAAC,KAAK,CACV,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,wDAAwD,CAAC;oBAE/G,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBACpD;gBAEA,+CAAA;gBACQ,IAAI,GAAG,CAAC,sBAAsB,EAAE;oBAC9B,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBACpD;gBACQ,GAAG,CAAC,sBAAuB,GAAE,IAAI;gBAEjC,OAAOC,KAAAA,kBAAkB,EAAC,cAAA,IAAkB;oBACpD,4FAAA;oBACA,4DAAA;oBACU,MAAM,2BAAA,GAA8BC,KAAAA,aAAa,KAC7C,CAAI,KAAK,EAAW,QAAQ,GAAc,QAAQ,KAClDC,KAAAA,aAAa;oBAEjB,OAAO,2BAA2B,CAChC;wBACE,WAAW,EACT,GAAG,CAAC,OAAQ,IAAGC,KAAAA,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA,GAAI,GAAG,CAAC,OAAO,CAAC,cAAc,CAAE,GAAE,SAAS;wBAChG,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO;oBAC3C,CAAa,EACD,MAAM;wBACJ,MAAM,SAAA,GAAY,CAAC,EAAA,CAAA,GAAA,CAAA,MAAA,IAAA,KAAA,EAAA,WAAA,EAAA,CAAA,CAAA,CAAA;wBACA,MAAA,iBAAA,GAAAC,KAAAA,wBAAA,CAAA,GAAA,CAAA;wBAEA,cAAA,CAAA,wBAAA,CAAA;4BAAA,iBAAA;wBAAA,CAAA,CAAA;wBACA,cAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,SAAA,CAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;wBAEA,OAAAC,KAAAA,eAAA,CACA;4BACA,IAAA,EAAA,CAAA,EAAA,SAAA,CAAA,EAAA,kBAAA,CAAA,CAAA;4BACA,EAAA,EAAA,aAAA;4BACA,gBAAA,EAAA,IAAA;4BACA,UAAA,EAAA;gCACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,OAAA;gCACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,kBAAA;4BACA,CAAA;wBACA,CAAA,EACA,OAAA,IAAA,IAAA;4BACA,6DAAA;4BACA,GAAA,CAAA,GAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;gCACA,KAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA;oCACAC,KAAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA,UAAA,CAAA;oCACA,IAAA,CAAA,GAAA,EAAA;oCACAC,KAAAA,eAAA,CAAAC,YAAAA,sBAAA,EAAA,CAAA;oCACA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA;gCACA,CAAA;4BACA,CAAA,CAAA;4BACA,IAAA;gCACA,OAAA,MAAA,cAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA;4BACA,CAAA,CAAA,OAAA,CAAA,EAAA;gCACA,+GAAA;gCACA,8GAAA;gCACA,6GAAA;gCACA,yDAAA;gCACA,MAAA,cAAA,GAAAC,KAAAA,SAAA,CAAA,CAAA,CAAA;gCAEAC,KAAAA,gBAAA,CAAA,cAAA,EAAA;oCACA,SAAA,EAAA;wCACA,IAAA,EAAA,YAAA;wCACA,OAAA,EAAA,KAAA;wCACA,IAAA,EAAA;4CACA,eAAA,EAAA,cAAA,CAAA,IAAA;4CACA,QAAA,EAAA,YAAA;wCACA,CAAA;oCACA,CAAA;gCACA,CAAA,CAAA;gCAEAJ,KAAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;gCACA,IAAA,CAAA,GAAA,EAAA;gCAEA,uEAAA;gCACA,8DAAA;gCACA,MAAAE,YAAAA,sBAAA,EAAA;gCAEA,8GAAA;gCACA,gHAAA;gCACA,8CAAA;gCACA,MAAA,cAAA;4BACA;wBACA,CAAA;oBAEA,CAAA;gBAEA,CAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bnextjs%409.22.0_%40open_5a3cd89dd78442528bf347837a15d2b9/node_modules/%40sentry/nextjs/src/server/index.ts"], "sourcesContent": ["import { context } from '@opentelemetry/api';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_ROUTE,\n  ATTR_URL_QUERY,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_TARGET,\n} from '@opentelemetry/semantic-conventions';\nimport type { EventProcessor } from '@sentry/core';\nimport {\n  applySdkMetadata,\n  extractTraceparentData,\n  getCapturedScopesOnSpan,\n  getClient,\n  getCurrentScope,\n  getGlobalScope,\n  getIsolationScope,\n  getRootSpan,\n  GLOBAL_OBJ,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  setCapturedScopesOnSpan,\n  spanToJSON,\n  stripUrlQueryAndFragment,\n} from '@sentry/core';\nimport type { NodeClient, NodeOptions } from '@sentry/node';\nimport { getDefaultIntegrations, httpIntegration, init as nodeInit } from '@sentry/node';\nimport { getScopesFromContext } from '@sentry/opentelemetry';\nimport { DEBUG_BUILD } from '../common/debug-build';\nimport { devErrorSymbolicationEventProcessor } from '../common/devErrorSymbolicationEventProcessor';\nimport { getVercelEnv } from '../common/getVercelEnv';\nimport {\n  TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL,\n  TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL,\n  TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION,\n} from '../common/span-attributes-with-logic-attached';\nimport { isBuild } from '../common/utils/isBuild';\nimport { distDirRewriteFramesIntegration } from './distDirRewriteFramesIntegration';\n\nexport * from '@sentry/node';\n\nexport { captureUnderscoreErrorException } from '../common/pages-router-instrumentation/_error';\n\nconst globalWithInjectedValues = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  _sentryRewriteFramesDistDir?: string;\n  _sentryRewritesTunnelPath?: string;\n  _sentryRelease?: string;\n};\n\n/**\n * A passthrough error boundary for the server that doesn't depend on any react. Error boundaries don't catch SSR errors\n * so they should simply be a passthrough.\n */\nexport const ErrorBoundary = (props: React.PropsWithChildren<unknown>): React.ReactNode => {\n  if (!props.children) {\n    return null;\n  }\n\n  if (typeof props.children === 'function') {\n    return (props.children as () => React.ReactNode)();\n  }\n\n  // since Next.js >= 10 requires React ^16.6.0 we are allowed to return children like this here\n  return props.children as React.ReactNode;\n};\n\n/**\n * A passthrough redux enhancer for the server that doesn't depend on anything from the `@sentry/react` package.\n */\nexport function createReduxEnhancer() {\n  return (createStore: unknown) => createStore;\n}\n\n/**\n * A passthrough error boundary wrapper for the server that doesn't depend on any react. Error boundaries don't catch\n * SSR errors so they should simply be a passthrough.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withErrorBoundary<P extends Record<string, any>>(\n  WrappedComponent: React.ComponentType<P>,\n): React.FC<P> {\n  return WrappedComponent as React.FC<P>;\n}\n\n/**\n * Just a passthrough since we're on the server and showing the report dialog on the server doesn't make any sense.\n */\nexport function showReportDialog(): void {\n  return;\n}\n\n/** Inits the Sentry NextJS SDK on node. */\nexport function init(options: NodeOptions): NodeClient | undefined {\n  if (isBuild()) {\n    return;\n  }\n\n  const customDefaultIntegrations = getDefaultIntegrations(options)\n    .filter(integration => integration.name !== 'Http')\n    .concat(\n      // We are using the HTTP integration without instrumenting incoming HTTP requests because Next.js does that by itself.\n      httpIntegration({\n        disableIncomingRequestSpans: true,\n      }),\n    );\n\n  // Turn off Next.js' own fetch instrumentation\n  // https://github.com/lforst/nextjs-fork/blob/****************************************/packages/next/src/server/lib/patch-fetch.ts#L245\n  process.env.NEXT_OTEL_FETCH_DISABLED = '1';\n\n  // This value is injected at build time, based on the output directory specified in the build config. Though a default\n  // is set there, we set it here as well, just in case something has gone wrong with the injection.\n  const distDirName = process.env._sentryRewriteFramesDistDir || globalWithInjectedValues._sentryRewriteFramesDistDir;\n  if (distDirName) {\n    customDefaultIntegrations.push(distDirRewriteFramesIntegration({ distDirName }));\n  }\n\n  const opts: NodeOptions = {\n    environment: process.env.SENTRY_ENVIRONMENT || getVercelEnv(false) || process.env.NODE_ENV,\n    release: process.env._sentryRelease || globalWithInjectedValues._sentryRelease,\n    defaultIntegrations: customDefaultIntegrations,\n    ...options,\n  };\n\n  if (DEBUG_BUILD && opts.debug) {\n    logger.enable();\n  }\n\n  DEBUG_BUILD && logger.log('Initializing SDK...');\n\n  if (sdkAlreadyInitialized()) {\n    DEBUG_BUILD && logger.log('SDK already initialized');\n    return;\n  }\n\n  applySdkMetadata(opts, 'nextjs', ['nextjs', 'node']);\n\n  const client = nodeInit(opts);\n  client?.on('beforeSampling', ({ spanAttributes }, samplingDecision) => {\n    // There are situations where the Next.js Node.js server forwards requests for the Edge Runtime server (e.g. in\n    // middleware) and this causes spans for Sentry ingest requests to be created. These are not exempt from our tracing\n    // because we didn't get the chance to do `suppressTracing`, since this happens outside of userland.\n    // We need to drop these spans.\n    if (\n      // eslint-disable-next-line deprecation/deprecation\n      (typeof spanAttributes[SEMATTRS_HTTP_TARGET] === 'string' &&\n        // eslint-disable-next-line deprecation/deprecation\n        spanAttributes[SEMATTRS_HTTP_TARGET].includes('sentry_key') &&\n        // eslint-disable-next-line deprecation/deprecation\n        spanAttributes[SEMATTRS_HTTP_TARGET].includes('sentry_client')) ||\n      (typeof spanAttributes[ATTR_URL_QUERY] === 'string' &&\n        spanAttributes[ATTR_URL_QUERY].includes('sentry_key') &&\n        spanAttributes[ATTR_URL_QUERY].includes('sentry_client'))\n    ) {\n      samplingDecision.decision = false;\n    }\n  });\n\n  client?.on('spanStart', span => {\n    const spanAttributes = spanToJSON(span).data;\n\n    // What we do in this glorious piece of code, is hoist any information about parameterized routes from spans emitted\n    // by Next.js via the `next.route` attribute, up to the transaction by setting the http.route attribute.\n    if (typeof spanAttributes?.['next.route'] === 'string') {\n      const rootSpan = getRootSpan(span);\n      const rootSpanAttributes = spanToJSON(rootSpan).data;\n\n      // Only hoist the http.route attribute if the transaction doesn't already have it\n      if (\n        // eslint-disable-next-line deprecation/deprecation\n        (rootSpanAttributes?.[ATTR_HTTP_REQUEST_METHOD] || rootSpanAttributes?.[SEMATTRS_HTTP_METHOD]) &&\n        !rootSpanAttributes?.[ATTR_HTTP_ROUTE]\n      ) {\n        const route = spanAttributes['next.route'].replace(/\\/route$/, '');\n        rootSpan.updateName(route);\n        rootSpan.setAttribute(ATTR_HTTP_ROUTE, route);\n        // Preserving the original attribute despite internally not depending on it\n        rootSpan.setAttribute('next.route', route);\n      }\n    }\n\n    // We want to skip span data inference for any spans generated by Next.js. Reason being that Next.js emits spans\n    // with patterns (e.g. http.server spans) that will produce confusing data.\n    if (spanAttributes?.['next.span_type'] !== undefined) {\n      span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto');\n    }\n\n    // We want to fork the isolation scope for incoming requests\n    if (spanAttributes?.['next.span_type'] === 'BaseServer.handleRequest' && span === getRootSpan(span)) {\n      const scopes = getCapturedScopesOnSpan(span);\n\n      const isolationScope = (scopes.isolationScope || getIsolationScope()).clone();\n      const scope = scopes.scope || getCurrentScope();\n\n      const currentScopesPointer = getScopesFromContext(context.active());\n      if (currentScopesPointer) {\n        currentScopesPointer.isolationScope = isolationScope;\n      }\n\n      setCapturedScopesOnSpan(span, scope, isolationScope);\n    }\n  });\n\n  getGlobalScope().addEventProcessor(\n    Object.assign(\n      (event => {\n        if (event.type === 'transaction') {\n          // Filter out transactions for static assets\n          // This regex matches the default path to the static assets (`_next/static`) and could potentially filter out too many transactions.\n          // We match `/_next/static/` anywhere in the transaction name because its location may change with the basePath setting.\n          if (event.transaction?.match(/^GET (\\/.*)?\\/_next\\/static\\//)) {\n            return null;\n          }\n\n          // Filter out transactions for requests to the tunnel route\n          if (\n            (globalWithInjectedValues._sentryRewritesTunnelPath &&\n              event.transaction === `POST ${globalWithInjectedValues._sentryRewritesTunnelPath}`) ||\n            (process.env._sentryRewritesTunnelPath &&\n              event.transaction === `POST ${process.env._sentryRewritesTunnelPath}`)\n          ) {\n            return null;\n          }\n\n          // Filter out requests to resolve source maps for stack frames in dev mode\n          if (event.transaction?.match(/\\/__nextjs_original-stack-frame/)) {\n            return null;\n          }\n\n          // Filter out /404 transactions which seem to be created excessively\n          if (\n            // Pages router\n            event.transaction === '/404' ||\n            // App router (could be \"GET /404\", \"POST /404\", ...)\n            event.transaction?.match(/^(GET|HEAD|POST|PUT|DELETE|CONNECT|OPTIONS|TRACE|PATCH) \\/(404|_not-found)$/)\n          ) {\n            return null;\n          }\n\n          // Filter transactions that we explicitly want to drop.\n          if (event.contexts?.trace?.data?.[TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION]) {\n            return null;\n          }\n\n          // Next.js 13 sometimes names the root transactions like this containing useless tracing.\n          if (event.transaction === 'NextServer.getRequestHandler') {\n            return null;\n          }\n\n          // Next.js 13 is not correctly picking up tracing data for trace propagation so we use a back-fill strategy\n          if (typeof event.contexts?.trace?.data?.[TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL] === 'string') {\n            const traceparentData = extractTraceparentData(\n              event.contexts.trace.data[TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL],\n            );\n\n            if (traceparentData?.parentSampled === false) {\n              return null;\n            }\n          }\n\n          return event;\n        } else {\n          return event;\n        }\n      }) satisfies EventProcessor,\n      { id: 'NextLowQualityTransactionsFilter' },\n    ),\n  );\n\n  getGlobalScope().addEventProcessor(\n    Object.assign(\n      ((event, hint) => {\n        if (event.type !== undefined) {\n          return event;\n        }\n\n        const originalException = hint.originalException;\n\n        const isPostponeError =\n          typeof originalException === 'object' &&\n          originalException !== null &&\n          '$$typeof' in originalException &&\n          originalException.$$typeof === Symbol.for('react.postpone');\n\n        if (isPostponeError) {\n          // Postpone errors are used for partial-pre-rendering (PPR)\n          return null;\n        }\n\n        // We don't want to capture suspense errors as they are simply used by React/Next.js for control flow\n        const exceptionMessage = event.exception?.values?.[0]?.value;\n        if (\n          exceptionMessage?.includes('Suspense Exception: This is not a real error!') ||\n          exceptionMessage?.includes('Suspense Exception: This is not a real error, and should not leak')\n        ) {\n          return null;\n        }\n\n        return event;\n      }) satisfies EventProcessor,\n      { id: 'DropReactControlFlowErrors' },\n    ),\n  );\n\n  // Use the preprocessEvent hook instead of an event processor, so that the users event processors receive the most\n  // up-to-date value, but also so that the logic that detects changes to the transaction names to set the source to\n  // \"custom\", doesn't trigger.\n  client?.on('preprocessEvent', event => {\n    // Enhance route handler transactions\n    if (\n      event.type === 'transaction' &&\n      event.contexts?.trace?.data?.['next.span_type'] === 'BaseServer.handleRequest'\n    ) {\n      event.contexts.trace.data[SEMANTIC_ATTRIBUTE_SENTRY_OP] = 'http.server';\n      event.contexts.trace.op = 'http.server';\n\n      if (event.transaction) {\n        event.transaction = stripUrlQueryAndFragment(event.transaction);\n      }\n\n      // eslint-disable-next-line deprecation/deprecation\n      const method = event.contexts.trace.data[SEMATTRS_HTTP_METHOD];\n      // eslint-disable-next-line deprecation/deprecation\n      const target = event.contexts?.trace?.data?.[SEMATTRS_HTTP_TARGET];\n      const route = event.contexts.trace.data[ATTR_HTTP_ROUTE] || event.contexts.trace.data['next.route'];\n\n      if (typeof method === 'string' && typeof route === 'string') {\n        const cleanRoute = route.replace(/\\/route$/, '');\n        event.transaction = `${method} ${cleanRoute}`;\n        event.contexts.trace.data[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'route';\n        // Preserve next.route in case it did not get hoisted\n        event.contexts.trace.data['next.route'] = cleanRoute;\n      }\n\n      // backfill transaction name for pages that would otherwise contain unparameterized routes\n      if (event.contexts.trace.data[TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL] && event.transaction !== 'GET /_app') {\n        event.transaction = `${method} ${event.contexts.trace.data[TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL]}`;\n      }\n\n      // Next.js overrides transaction names for page loads that throw an error\n      // but we want to keep the original target name\n      if (event.transaction === 'GET /_error' && target) {\n        event.transaction = `${method ? `${method} ` : ''}${target}`;\n      }\n    }\n\n    // Next.js 13 is not correctly picking up tracing data for trace propagation so we use a back-fill strategy\n    if (\n      event.type === 'transaction' &&\n      typeof event.contexts?.trace?.data?.[TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL] === 'string'\n    ) {\n      const traceparentData = extractTraceparentData(event.contexts.trace.data[TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL]);\n\n      if (traceparentData?.traceId) {\n        event.contexts.trace.trace_id = traceparentData.traceId;\n      }\n\n      if (traceparentData?.parentSpanId) {\n        event.contexts.trace.parent_span_id = traceparentData.parentSpanId;\n      }\n    }\n  });\n\n  if (process.env.NODE_ENV === 'development') {\n    getGlobalScope().addEventProcessor(devErrorSymbolicationEventProcessor);\n  }\n\n  try {\n    // @ts-expect-error `process.turbopack` is a magic string that will be replaced by Next.js\n    if (process.turbopack) {\n      getGlobalScope().setTag('turbopack', true);\n    }\n  } catch {\n    // Noop\n    // The statement above can throw because process is not defined on the client\n  }\n\n  DEBUG_BUILD && logger.log('SDK successfully initialized');\n\n  return client;\n}\n\nfunction sdkAlreadyInitialized(): boolean {\n  return !!getClient();\n}\n\nexport * from '../common';\n\nexport { wrapApiHandlerWithSentry } from '../common/pages-router-instrumentation/wrapApiHandlerWithSentry';\n"], "names": ["GLOBAL_OBJ", "isBuild", "getDefaultIntegrations", "httpIntegration", "distDirRewriteFramesIntegration", "getVercelEnv", "DEBUG_BUILD", "logger", "applySdkMetadata", "nodeInit", "SEMATTRS_HTTP_TARGET", "ATTR_URL_QUERY", "spanToJSON", "getRootSpan", "ATTR_HTTP_REQUEST_METHOD", "SEMATTRS_HTTP_METHOD", "ATTR_HTTP_ROUTE", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "getCapturedScopesOnSpan", "getIsolationScope", "getCurrentScope", "getScopesFromContext", "context", "setCapturedScopesOnSpan", "getGlobalScope", "TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION", "TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL", "extractTraceparentData", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "stripUrlQueryAndFragment", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL", "devErrorSymbolicationEventProcessor", "getClient"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAM,wBAAA,GAA2BA,KAAAA,UAAAA;AAMjC;;;CAGA,GACa,MAAA,aAAA,GAAgB,CAAC,KAAK,KAAwD;IACzF,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;QACnB,OAAO,IAAI;IACf;IAEE,IAAI,OAAO,KAAK,CAAC,QAAS,KAAI,UAAU,EAAE;QACxC,OAAQ,AAAD,KAAM,CAAC,QAAS,IAA2B;IACtD;IAEA,8FAAA;IACE,OAAO,KAAK,CAAC,QAAS;AACxB;AAEA;;CAEA,GACO,SAAS,mBAAmB,GAAG;IACpC,OAAO,CAAC,WAAW,GAAc,WAAW;AAC9C;AAEA;;;CAGA,GACA,8DAAA;AACO,SAAS,iBAAiB,CAC/B,gBAAgB;IAEhB,OAAO,gBAAiB;AAC1B;AAEA;;CAEA,GACO,SAAS,gBAAgB,GAAS;IACvC;AACF;AAEA,yCAAA,GACO,SAAS,IAAI,CAAC,OAAO,EAAuC;IACjE,IAAIC,QAAAA,OAAO,EAAE,EAAE;QACb;IACJ;IAEE,MAAM,yBAAA,GAA4BC,KAAAA,sBAAsB,CAAC,OAAO,EAC7D,MAAM,EAAC,WAAA,GAAe,WAAW,CAAC,IAAK,KAAI,MAAM,EACjD,MAAM,CACX,sHAAA;IACMC,KAAAA,eAAe,CAAC;QACd,2BAA2B,EAAE,IAAI;IACzC,CAAO,CAAC;IAGR,8CAAA;IACA,uIAAA;IACE,OAAO,CAAC,GAAG,CAAC,wBAAA,GAA2B,GAAG;IAE5C,sHAAA;IACA,kGAAA;IACE,MAAM,WAAY,GAAE,OAAO,CAAC,GAAG,CAAC,2BAA4B,IAAG,wBAAwB,CAAC,2BAA2B;IACnH,IAAI,WAAW,EAAE;QACf,yBAAyB,CAAC,IAAI,CAACC,gCAAAA,+BAA+B,CAAC;YAAE,WAAA;QAAA,CAAa,CAAC,CAAC;IACpF;IAEE,MAAM,IAAI,GAAgB;QACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAmB,IAAGC,aAAAA,YAAY,CAAC,KAAK,CAAE,IAAG,OAAO,CAAC,GAAG,CAAC,QAAQ;QAC1F,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,cAAe,IAAG,wBAAwB,CAAC,cAAc;QAC9E,mBAAmB,EAAE,yBAAyB;QAC9C,GAAG,OAAO;IACd,CAAG;IAED,IAAIC,WAAAA,WAAA,IAAe,IAAI,CAAC,KAAK,EAAE;QAC7BC,KAAAA,MAAM,CAAC,MAAM,EAAE;IACnB;IAEED,WAAAA,WAAAA,IAAeC,KAAAA,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC;IAEhD,IAAI,qBAAqB,EAAE,EAAE;QAC3BD,WAAAA,WAAAA,IAAeC,KAAAA,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC;QACpD;IACJ;IAEEC,KAAAA,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE;QAAC,QAAQ;QAAE,MAAM;KAAC,CAAC;IAEpD,MAAM,MAAO,GAAEC,KAAAA,IAAQ,CAAC,IAAI,CAAC;IAC7B,MAAM,EAAE,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAE,cAAe,EAAC,EAAE,gBAAgB,KAAK;QACzE,+GAAA;QACA,oHAAA;QACA,oGAAA;QACA,+BAAA;QACI,IACJ,mDAAA;QACO,OAAO,cAAc,CAACC,oBAAAA,oBAAoB,CAAA,KAAM,QAAS,IAChE,mDAAA;QACQ,cAAc,CAACA,oBAAAA,oBAAoB,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAE,IACpE,mDAAA;QACQ,cAAc,CAACA,oBAAAA,oBAAoB,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,IAC/D,OAAO,cAAc,CAACC,oBAAAA,cAAc,CAAA,KAAM,QAAS,IAClD,cAAc,CAACA,oBAAAA,cAAc,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAE,IACtD,cAAc,CAACA,oBAAAA,cAAc,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,EAC1D;YACA,gBAAgB,CAAC,QAAS,GAAE,KAAK;QACvC;IACA,CAAG,CAAC;IAEF,MAAM,EAAE,EAAE,CAAC,WAAW,GAAE,QAAQ;QAC9B,MAAM,iBAAiBC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;QAEhD,oHAAA;QACA,wGAAA;QACI,IAAI,OAAO,cAAc,EAAA,CAAG,YAAY,CAAA,KAAM,QAAQ,EAAE;YACtD,MAAM,QAAS,GAAEC,KAAAA,WAAW,CAAC,IAAI,CAAC;YAClC,MAAM,qBAAqBD,KAAAA,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI;YAE1D,iFAAA;YACM,IACN,mDAAA;YACQ,CAAC,kBAAkB,EAAA,CAAGE,oBAAAA,wBAAwB,CAAA,IAAK,kBAAkB,EAAA,CAAGC,oBAAAA,oBAAoB,CAAC,KAC7F,CAAC,kBAAkB,EAAA,CAAGC,oBAAAA,eAAe,CAAA,EACrC;gBACA,MAAM,KAAA,GAAQ,cAAc,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;gBAClE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC1B,QAAQ,CAAC,YAAY,CAACA,oBAAAA,eAAe,EAAE,KAAK,CAAC;gBACrD,2EAAA;gBACQ,QAAQ,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;YAClD;QACA;QAEA,gHAAA;QACA,2EAAA;QACI,IAAI,cAAc,EAAA,CAAG,gBAAgB,CAAA,KAAM,SAAS,EAAE;YACpD,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,MAAM,CAAC;QACjE;QAEA,4DAAA;QACI,IAAI,cAAc,EAAA,CAAG,gBAAgB,CAAE,KAAI,0BAA2B,IAAG,SAASJ,KAAAA,WAAW,CAAC,IAAI,CAAC,EAAE;YACnG,MAAM,MAAO,GAAEK,KAAAA,uBAAuB,CAAC,IAAI,CAAC;YAE5C,MAAM,cAAA,GAAiB,CAAC,MAAM,CAAC,cAAA,IAAkBC,KAAAA,iBAAiB,EAAE,EAAE,KAAK,EAAE;YAC7E,MAAM,QAAQ,MAAM,CAAC,KAAM,IAAGC,KAAAA,eAAe,EAAE;YAE/C,MAAM,oBAAqB,GAAEC,cAAAA,oBAAoB,CAACC,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAC;YACnE,IAAI,oBAAoB,EAAE;gBACxB,oBAAoB,CAAC,cAAe,GAAE,cAAc;YAC5D;YAEMC,KAAAA,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC;QAC1D;IACA,CAAG,CAAC;IAEFC,KAAAA,cAAc,EAAE,CAAC,iBAAiB,CAChC,MAAM,CAAC,MAAM,EACV,SAAS;QACR,IAAI,KAAK,CAAC,IAAK,KAAI,aAAa,EAAE;YAC1C,4CAAA;YACA,oIAAA;YACA,wHAAA;YACU,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,+BAA+B,CAAC,EAAE;gBAC7D,OAAO,IAAI;YACvB;YAEA,2DAAA;YACU,IACE,AAAC,wBAAwB,CAAC,yBAA0B,IAClD,KAAK,CAAC,WAAA,KAAgB,CAAC,KAAK,EAAE,wBAAwB,CAAC,yBAAyB,CAAC,CAAA,IACA,OAAA,CAAA,GAAA,CAAA,yBAAA,IACA,KAAA,CAAA,WAAA,KAAA,CAAA,KAAA,EAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,CAAA,CAAA,EACA;gBACA,OAAA,IAAA;YACA;YAEA,0EAAA;YACA,IAAA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA,iCAAA,CAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,oEAAA;YACA,IACA,eAAA;YACA,KAAA,CAAA,WAAA,KAAA,MAAA,IACA,qDAAA;YACA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA,6EAAA,GACA;gBACA,OAAA,IAAA;YACA;YAEA,uDAAA;YACA,IAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAAC,gCAAAA,wCAAA,CAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,yFAAA;YACA,IAAA,KAAA,CAAA,WAAA,KAAA,8BAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,2GAAA;YACA,IAAA,OAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAAC,gCAAAA,sCAAA,CAAA,KAAA,QAAA,EAAA;gBACA,MAAA,eAAA,GAAAC,KAAAA,sBAAA,CACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAD,gCAAAA,sCAAA,CAAA;gBAGA,IAAA,eAAA,EAAA,aAAA,KAAA,KAAA,EAAA;oBACA,OAAA,IAAA;gBACA;YACA;YAEA,OAAA,KAAA;QACA,CAAA,MAAA;YACA,OAAA,KAAA;QACA;IACA,CAAA,EACA;QAAA,EAAA,EAAA,kCAAA;IAAA,CAAA;IAIAF,KAAAA,cAAA,EAAA,CAAA,iBAAA,CACA,MAAA,CAAA,MAAA,CACA,CAAA,KAAA,EAAA,IAAA,KAAA;QACA,IAAA,KAAA,CAAA,IAAA,KAAA,SAAA,EAAA;YACA,OAAA,KAAA;QACA;QAEA,MAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA;QAEA,MAAA,eAAA,GACA,OAAA,iBAAA,KAAA,QAAA,IACA,iBAAA,KAAA,IAAA,IACA,UAAA,IAAA,iBAAA,IACA,iBAAA,CAAA,QAAA,KAAA,MAAA,CAAA,GAAA,CAAA,gBAAA,CAAA;QAEA,IAAA,eAAA,EAAA;YACA,2DAAA;YACA,OAAA,IAAA;QACA;QAEA,qGAAA;QACA,MAAA,gBAAA,GAAA,KAAA,CAAA,SAAA,EAAA,MAAA,EAAA,CAAA,CAAA,CAAA,EAAA,KAAA;QACA,IACA,gBAAA,EAAA,QAAA,CAAA,+CAAA,CAAA,IACA,gBAAA,EAAA,QAAA,CAAA,mEAAA,GACA;YACA,OAAA,IAAA;QACA;QAEA,OAAA,KAAA;IACA,CAAA,EACA;QAAA,EAAA,EAAA,4BAAA;IAAA,CAAA;IAIA,kHAAA;IACA,kHAAA;IACA,6BAAA;IACA,MAAA,EAAA,EAAA,CAAA,iBAAA,GAAA,KAAA,IAAA;QACA,qCAAA;QACA,IACA,KAAA,CAAA,IAAA,KAAA,aAAA,IACA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,gBAAA,CAAA,KAAA,4BACA;YACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAI,KAAAA,4BAAA,CAAA,GAAA,aAAA;YACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA,GAAA,aAAA;YAEA,IAAA,KAAA,CAAA,WAAA,EAAA;gBACA,KAAA,CAAA,WAAA,GAAAC,KAAAA,wBAAA,CAAA,KAAA,CAAA,WAAA,CAAA;YACA;YAEA,mDAAA;YACA,MAAA,MAAA,GAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAd,oBAAAA,oBAAA,CAAA;YACA,mDAAA;YACA,MAAA,MAAA,GAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAAL,oBAAAA,oBAAA,CAAA;YACA,MAAA,KAAA,GAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAM,oBAAAA,eAAA,CAAA,IAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA;YAEA,IAAA,OAAA,MAAA,KAAA,QAAA,IAAA,OAAA,KAAA,KAAA,QAAA,EAAA;gBACA,MAAA,UAAA,GAAA,KAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA,CAAA;gBACA,KAAA,CAAA,WAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA;gBACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAc,KAAAA,gCAAA,CAAA,GAAA,OAAA;gBACA,qDAAA;gBACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,UAAA;YACA;YAEA,0FAAA;YACA,IAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAC,gCAAAA,sCAAA,CAAA,IAAA,KAAA,CAAA,WAAA,KAAA,WAAA,EAAA;gBACA,KAAA,CAAA,WAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAA,gCAAAA,sCAAA,CAAA,CAAA,CAAA;YACA;YAEA,yEAAA;YACA,+CAAA;YACA,IAAA,KAAA,CAAA,WAAA,KAAA,aAAA,IAAA,MAAA,EAAA;gBACA,KAAA,CAAA,WAAA,GAAA,CAAA,EAAA,MAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,EAAA,MAAA,CAAA,CAAA;YACA;QACA;QAEA,2GAAA;QACA,IACA,KAAA,CAAA,IAAA,KAAA,aAAA,IACA,OAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAAL,gCAAAA,sCAAA,CAAA,KAAA,UACA;YACA,MAAA,eAAA,GAAAC,KAAAA,sBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAAD,gCAAAA,sCAAA,CAAA,CAAA;YAEA,IAAA,eAAA,EAAA,OAAA,EAAA;gBACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA,GAAA,eAAA,CAAA,OAAA;YACA;YAEA,IAAA,eAAA,EAAA,YAAA,EAAA;gBACA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,cAAA,GAAA,eAAA,CAAA,YAAA;YACA;QACA;IACA,CAAA,CAAA;IAEA,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,WAAA,EAAA;QACAF,KAAAA,cAAA,EAAA,CAAA,iBAAA,CAAAQ,oCAAAA,mCAAA,CAAA;IACA;IAEA,IAAA;QACA,0FAAA;QACA,IAAA,OAAA,CAAA,SAAA,mBAAA;YACAR,KAAAA,cAAA,EAAA,CAAA,MAAA,CAAA,WAAA,EAAA,IAAA,CAAA;QACA;IACA,CAAA,CAAA,OAAA;IACA,OAAA;IACA,6EAAA;IACA;IAEAlB,WAAAA,WAAA,IAAAC,KAAAA,MAAA,CAAA,GAAA,CAAA,8BAAA,CAAA;IAEA,OAAA,MAAA;AACA;AAEA,SAAA,qBAAA,GAAA;IACA,OAAA,CAAA,CAAA0B,KAAAA,SAAA,EAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3004, "column": 0}, "map": {"version": 3, "file": "index.server.js", "sources": [], "names": [], "mappings": "", "debugId": null}}]}