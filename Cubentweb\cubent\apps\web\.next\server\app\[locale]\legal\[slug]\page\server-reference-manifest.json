{"node": {"0096e53795e6a564b18f93bb8fcafed8c92ce69fd0": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}, "6097e1140601cb7e26bf714e6feb992c08cd55994e": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}, "60abc6f9e6e8750102e6c6b14f96f2958fc7055d29": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}, "60ad23fee6b5eb1517ffe82db266ee5173f6c96370": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}, "40e41d29e77b3a7ab132de11415d6e33ba9fc9409b": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}, "7ff83766f16d7a517e525f5e3addcd3b4216951139": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "action-browser"}}, "7f96a2fd407ce9e86c335c8a960562dbe5505817d3": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "action-browser"}}, "402b8e03b1f0b8a7712e317018747fcc5f91f9c504": {"workers": {"app/[locale]/legal/[slug]/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/[locale]/legal/[slug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/apps/web/app/[locale]/legal/[slug]/page.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/legal/[slug]/page": "rsc"}}}, "edge": {}}