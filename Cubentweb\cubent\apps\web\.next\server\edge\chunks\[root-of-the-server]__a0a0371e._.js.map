{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,uRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,gPAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,gPAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,gPAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,gPAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,gPAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,QAAQ,GAAG,CAAC,eAAe;YAC5C,YAAY,QAAQ,GAAG,CAAC,UAAU;YAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;YAC1C,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;QAC5D;IACF"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/observability/instrumentation.ts"], "sourcesContent": ["import { init } from '@sentry/nextjs';\nimport { keys } from './keys';\n\nconst opts = {\n  dsn: keys().NEXT_PUBLIC_SENTRY_DSN,\n};\n\nexport const initializeSentry = () => {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    init(opts);\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    init(opts);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,OAAO;IACX,KAAK,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,IAAI,sBAAsB;AACpC;AAEO,MAAM,mBAAmB;IAC9B,uCAA2C;;IAE3C;IAEA,wCAAyC;QACvC,CAAA,GAAA,0SAAA,CAAA,OAAI,AAAD,EAAE;IACP;AACF"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/instrumentation.ts"], "sourcesContent": ["import { initializeSentry } from '@repo/observability/instrumentation';\n\nexport const register = initializeSentry();\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD"}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "AAAA,KAAK,QAAQ,KAAK,CAAC;AACnB,MAAM;AACN,QAAQ,KAAK,CAAC,KAAO;AACrB,KAAK,QAAQ,CAAC,6BAA6B,GAAG,IAAI,MAAM,SAAS;IAC7D,KAAI,OAAO,EAAE,IAAI;QACb,IAAI,SAAS,QAAQ;YACjB,OAAO,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,KAAK;QAC3C;QACA,IAAI,SAAS,CAAC,GAAG,OAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK;QAClE,OAAO,IAAI,GAAG,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,CAAC,MAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;QACvE,OAAO;IACX;AACJ"}}]}