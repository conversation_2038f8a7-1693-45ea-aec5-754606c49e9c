{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bFuaNjTIHxCGZnZfnp9MT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "c88b870df3f1b2f27f01c21a895337a0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e47c4b495ff6598ed53b4fe8a2d4300c261ad80a7507a79b65ca1529bfeada3d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7d01802c702a824991ab25e0f8bdc3655a054e5bc1ba51375df0cd575db4d64e"}}}, "functions": {}, "sortedMiddleware": ["/"]}