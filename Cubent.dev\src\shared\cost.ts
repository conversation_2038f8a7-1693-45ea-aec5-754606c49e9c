import type { ModelInfo } from "@cubent/types"

// Cubent Units mapping based on https://cubentdev.mintlify.app/models-and-pricing
// This maps model IDs to their Cubent unit costs for non-BYAK configurations
export const CUBENT_UNITS_MAPPING: Record<string, number> = {
	// Anthropic Claude Models
	"claude-3-7-sonnet-20250219": 1.1,
	"claude-3-7-sonnet-thinking-20250219": 1.35,
	"claude-3-5-sonnet-20241022": 0.95,
	"claude-3-5-haiku-20241022": 0.55,
	"claude-3-haiku-20240307": 0.45,

	// OpenAI Models
	"gpt-4o": 1.1,
	"gpt-4.5-preview": 1.2,
	"gpt-4o-mini": 0.65,
	"o3-mini": 1.0,
	"o3-mini-high": 1.1,
	"o3-mini-low": 0.75,

	// DeepSeek Models
	"deepseek-chat": 0.35,
	"deepseek-reasoner": 0.7,

	// Google Gemini Models
	"gemini-2.5-flash": 0.3,
	"gemini-2.5-flash-thinking": 0.4,
	"gemini-2.5-pro": 0.85,
	"gemini-2.0-flash": 0.45,
	"gemini-2.0-pro": 0.7,
	"gemini-1.5-flash": 0.4,
	"gemini-1.5-pro": 0.65,

	// xAI Grok Models
	"grok-3": 1.1,
	"grok-3-mini": 0.3,
	"grok-2-vision": 0.7,
}

// Get Cubent units for a model (returns 0 for BYAK models or unknown models)
export function getCubentUnitsForModel(modelId: string): number {
	// Normalize model ID to match our mapping
	const normalizedModelId = normalizeModelId(modelId)
	return CUBENT_UNITS_MAPPING[normalizedModelId] || 0
}

// Normalize model IDs to match our mapping (handles provider prefixes and variations)
function normalizeModelId(modelId: string): string {
	// Remove common provider prefixes
	let normalized = modelId
		.replace(/^anthropic\//, "")
		.replace(/^openai\//, "")
		.replace(/^google\//, "")
		.replace(/^deepseek\//, "")
		.replace(/^xai\//, "")
		.toLowerCase()

	// Remove version suffixes (like -001, -002, -beta, -exp, etc.)
	normalized = normalized
		.replace(/-\d{3}$/, "") // Remove -001, -002, etc.
		.replace(/-beta$/, "")
		.replace(/-exp$/, "")
		.replace(/-experimental$/, "")
		.replace(/-preview$/, "")
		.replace(/-latest$/, "")

	// Handle common model ID variations
	const mappings: Record<string, string> = {
		// Claude variations
		"claude-sonnet-4-20250514": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet-thinking": "claude-3-7-sonnet-thinking-20250219",
		"claude-3.5-sonnet": "claude-3-5-sonnet-20241022",
		"claude-3.5-haiku": "claude-3-5-haiku-20241022",
		"claude-3-haiku": "claude-3-haiku-20240307",

		// OpenAI variations
		"gpt-4o-2024-11-20": "gpt-4o",
		"gpt-4o-mini-2024-07-18": "gpt-4o-mini",
		"o3-mini-high-reasoning": "o3-mini-high",
		"o3-mini-low-reasoning": "o3-mini-low",

		// Gemini variations
		"gemini-2.5-flash-002": "gemini-2.5-flash",
		"gemini-2.5-flash-thinking-exp": "gemini-2.5-flash-thinking",
		"gemini-2.5-pro-002": "gemini-2.5-pro",
		"gemini-2.0-flash-exp": "gemini-2.0-flash",

		// DeepSeek variations
		"deepseek-ai/deepseek-chat": "deepseek-chat",
		"deepseek-ai/deepseek-reasoner": "deepseek-reasoner",

		// Grok variations
		"grok-3-beta": "grok-3",
		"grok-3-mini-beta": "grok-3-mini",
		"grok-2-vision-beta": "grok-2-vision",
	}

	return mappings[normalized] || normalized
}

function calculateApiCostInternal(
	modelInfo: ModelInfo,
	inputTokens: number,
	outputTokens: number,
	cacheCreationInputTokens: number,
	cacheReadInputTokens: number,
): number {
	const cacheWritesCost = ((modelInfo.cacheWritesPrice || 0) / 1_000_000) * cacheCreationInputTokens
	const cacheReadsCost = ((modelInfo.cacheReadsPrice || 0) / 1_000_000) * cacheReadInputTokens
	const baseInputCost = ((modelInfo.inputPrice || 0) / 1_000_000) * inputTokens
	const outputCost = ((modelInfo.outputPrice || 0) / 1_000_000) * outputTokens
	const totalCost = cacheWritesCost + cacheReadsCost + baseInputCost + outputCost
	return totalCost
}

// For Anthropic compliant usage, the input tokens count does NOT include the cached tokens
export function calculateApiCostAnthropic(
	modelInfo: ModelInfo,
	inputTokens: number,
	outputTokens: number,
	cacheCreationInputTokens?: number,
	cacheReadInputTokens?: number,
): number {
	const cacheCreationInputTokensNum = cacheCreationInputTokens || 0
	const cacheReadInputTokensNum = cacheReadInputTokens || 0
	return calculateApiCostInternal(
		modelInfo,
		inputTokens,
		outputTokens,
		cacheCreationInputTokensNum,
		cacheReadInputTokensNum,
	)
}

// For OpenAI compliant usage, the input tokens count INCLUDES the cached tokens
export function calculateApiCostOpenAI(
	modelInfo: ModelInfo,
	inputTokens: number,
	outputTokens: number,
	cacheCreationInputTokens?: number,
	cacheReadInputTokens?: number,
): number {
	const cacheCreationInputTokensNum = cacheCreationInputTokens || 0
	const cacheReadInputTokensNum = cacheReadInputTokens || 0
	const nonCachedInputTokens = Math.max(0, inputTokens - cacheCreationInputTokensNum - cacheReadInputTokensNum)
	return calculateApiCostInternal(
		modelInfo,
		nonCachedInputTokens,
		outputTokens,
		cacheCreationInputTokensNum,
		cacheReadInputTokensNum,
	)
}

export const parseApiPrice = (price: any) => (price ? parseFloat(price) * 1_000_000 : undefined)
