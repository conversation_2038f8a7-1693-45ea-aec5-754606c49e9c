import { create } from "zustand"
import { subscribeWithSelector } from "zustand/middleware"

import type { CloudUserInfo } from "@cubent/types"

export interface AuthState {
	// Authentication status
	isAuthenticated: boolean
	isAuthenticating: boolean
	hasActiveSession: boolean

	// User information
	userInfo: CloudUserInfo | null

	// Device OAuth specific
	deviceId: string | null
	authState: string | null

	// Error handling
	authError: string | null

	// Actions
	setAuthenticated: (authenticated: boolean) => void
	setAuthenticating: (authenticating: boolean) => void
	setActiveSession: (active: boolean) => void
	setUserInfo: (userInfo: CloudUserInfo | null) => void
	setDeviceAuth: (deviceId: string, state: string) => void
	setAuthError: (error: string | null) => void
	clearAuth: () => void
	reset: () => void
}

const initialState = {
	isAuthenticated: false,
	isAuthenticating: false,
	hasActiveSession: false,
	userInfo: null,
	deviceId: null,
	authState: null,
	authError: null,
}

export const useAuthStore = create<AuthState>()(
	subscribeWithSelector((set, get) => ({
		...initialState,

		setAuthenticated: (authenticated: boolean) => {
			set({ isAuthenticated: authenticated })
			if (!authenticated) {
				set({ hasActiveSession: false, userInfo: null })
			}
		},

		setAuthenticating: (authenticating: boolean) => {
			set({ isAuthenticating: authenticating })
			if (authenticating) {
				set({ authError: null })
			}
		},

		setActiveSession: (active: boolean) => {
			set({ hasActiveSession: active })
			if (active) {
				set({ isAuthenticated: true, isAuthenticating: false })
			}
		},

		setUserInfo: (userInfo: CloudUserInfo | null) => {
			set({ userInfo })
		},

		setDeviceAuth: (deviceId: string, state: string) => {
			set({
				deviceId,
				authState: state,
				isAuthenticating: true,
				authError: null,
			})
		},

		setAuthError: (error: string | null) => {
			set({
				authError: error,
				isAuthenticating: false,
			})
		},

		clearAuth: () => {
			set({
				isAuthenticated: false,
				hasActiveSession: false,
				userInfo: null,
				deviceId: null,
				authState: null,
				authError: null,
			})
		},

		reset: () => {
			set(initialState)
		},
	})),
)

// Selectors for common use cases
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useIsAuthenticating = () => useAuthStore((state) => state.isAuthenticating)
export const useUserInfo = () => useAuthStore((state) => state.userInfo)
export const useAuthError = () => useAuthStore((state) => state.authError)
export const useHasActiveSession = () => useAuthStore((state) => state.hasActiveSession)

// Actions
export const useAuthActions = () =>
	useAuthStore((state) => ({
		setAuthenticated: state.setAuthenticated,
		setAuthenticating: state.setAuthenticating,
		setActiveSession: state.setActiveSession,
		setUserInfo: state.setUserInfo,
		setDeviceAuth: state.setDeviceAuth,
		setAuthError: state.setAuthError,
		clearAuth: state.clearAuth,
		reset: state.reset,
	}))
