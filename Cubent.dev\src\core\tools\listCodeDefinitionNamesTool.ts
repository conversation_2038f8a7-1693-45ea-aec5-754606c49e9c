import path from "path"
import fs from "fs/promises"

import { <PERSON><PERSON><PERSON><PERSON>, AskA<PERSON>roval, <PERSON>leError, PushToolResult, RemoveClosingTag } from "../../shared/tools"
import { Task } from "../task/Task"
import { ClineSayTool } from "../../shared/ExtensionMessage"
import { getReadablePath } from "../../utils/path"
import { parseSourceCodeForDefinitionsTopLevel, parseSourceCodeDefinitionsForFile } from "../../services/tree-sitter"
import { RecordSource } from "../context-tracking/FileContextTrackerTypes"

// Rate limiting for source definition tool to prevent excessive token consumption
const RATE_LIMITS = {
	MAX_USES_PER_TASK: 10, // Max uses per task
	COOLDOWN_SECONDS: 30, // Cooldown between uses
}

// Track usage per task
const taskUsageTracker = new Map<string, { count: number; lastUsed: number }>()

export async function listCodeDefinitionNamesTool(
	cline: Task,
	block: ToolUse,
	askApproval: Ask<PERSON><PERSON><PERSON>al,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	const relPath: string | undefined = block.params.path

	const sharedMessageProps: ClineSayTool = {
		tool: "listCodeDefinitionNames",
		path: getReadablePath(cline.cwd, removeClosingTag("path", relPath)),
	}

	try {
		if (block.partial) {
			const partialMessage = JSON.stringify({ ...sharedMessageProps, content: "" } satisfies ClineSayTool)
			await cline.ask("tool", partialMessage, block.partial).catch(() => {})
			return
		} else {
			if (!relPath) {
				cline.consecutiveMistakeCount++
				cline.recordToolError("list_code_definition_names")
				pushToolResult(await cline.sayAndCreateMissingParamError("list_code_definition_names", "path"))
				return
			}

			cline.consecutiveMistakeCount = 0

			// Check rate limits to prevent excessive token consumption
			const taskId = cline.taskId
			const now = Date.now()
			const usage = taskUsageTracker.get(taskId) || { count: 0, lastUsed: 0 }

			// Check max uses per task
			if (usage.count >= RATE_LIMITS.MAX_USES_PER_TASK) {
				pushToolResult(
					`Rate limit exceeded: Maximum ${RATE_LIMITS.MAX_USES_PER_TASK} source definition requests per task. This prevents excessive token consumption. Use VS Code's built-in "Go to Definition" (F12) for navigation instead.`,
				)
				return
			}

			// Check cooldown
			const timeSinceLastUse = (now - usage.lastUsed) / 1000
			if (usage.lastUsed > 0 && timeSinceLastUse < RATE_LIMITS.COOLDOWN_SECONDS) {
				const remainingCooldown = Math.ceil(RATE_LIMITS.COOLDOWN_SECONDS - timeSinceLastUse)
				pushToolResult(
					`Rate limit: Please wait ${remainingCooldown} seconds before using source definitions again. This prevents excessive token consumption.`,
				)
				return
			}

			// Update usage tracking
			taskUsageTracker.set(taskId, { count: usage.count + 1, lastUsed: now })

			const absolutePath = path.resolve(cline.cwd, relPath)
			let result: string

			try {
				const stats = await fs.stat(absolutePath)

				if (stats.isFile()) {
					const fileResult = await parseSourceCodeDefinitionsForFile(absolutePath, cline.rooIgnoreController)
					result = fileResult ?? "No source code definitions found in cline file."
				} else if (stats.isDirectory()) {
					result = await parseSourceCodeForDefinitionsTopLevel(absolutePath, cline.rooIgnoreController)
				} else {
					result = "The specified path is neither a file nor a directory."
				}
			} catch {
				result = `${absolutePath}: does not exist or cannot be accessed.`
			}

			const completeMessage = JSON.stringify({ ...sharedMessageProps, content: result } satisfies ClineSayTool)
			const didApprove = await askApproval("tool", completeMessage)

			if (!didApprove) {
				return
			}

			if (relPath) {
				await cline.fileContextTracker.trackFileContext(relPath, "read_tool" as RecordSource)
			}

			// Add usage info to result
			const currentUsage = taskUsageTracker.get(taskId)!
			const remainingUses = RATE_LIMITS.MAX_USES_PER_TASK - currentUsage.count
			const usageInfo = `\n\n[Source Definition Usage: ${currentUsage.count}/${RATE_LIMITS.MAX_USES_PER_TASK} uses in this task. ${remainingUses} remaining.]`

			pushToolResult(result + usageInfo)

			// Cleanup old task data (keep only last 10 tasks)
			if (taskUsageTracker.size > 10) {
				const oldestTasks = Array.from(taskUsageTracker.entries())
					.sort((a, b) => a[1].lastUsed - b[1].lastUsed)
					.slice(0, taskUsageTracker.size - 10)
				oldestTasks.forEach(([taskId]) => taskUsageTracker.delete(taskId))
			}

			return
		}
	} catch (error) {
		await handleError("parsing source code definitions", error)
		return
	}
}
