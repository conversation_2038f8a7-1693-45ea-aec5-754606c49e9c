import { SECRET_STATE_KEYS, ProviderSettings } from "@cubent/types"

export function checkExistKey(config: ProviderSettings | undefined) {
	// Check if user has a valid API configuration
	// This determines whether to show the welcome screen or main interface
	if (!config) {
		return false
	}

	// Check if the configuration has required fields based on provider
	switch (config.apiProvider) {
		case "openrouter":
			return !!config.openRouterApiKey
		case "glama":
			return !!config.glamaApiKey
		case "unbound":
			return !!config.unboundApiKey
		case "requesty":
			return !!config.requestyApiKey
		case "litellm":
			return !!config.litellmApiKey
		case "anthropic":
			return !!config.anthropicApiKey || !!config.apiKey // Support both new and legacy key
		case "openai":
			return !!config.openAiApiKey
		case "gemini":
			return !!config.geminiApiKey
		case "mistral":
			return !!config.mistralApiKey
		case "bedrock":
			return !!config.awsAccessKey && !!config.awsSecretKey && !!config.awsRegion
		case "openai-native":
			return !!config.openAiNativeApiKey
		case "vscode-lm":
			return !!config.vsCodeLmModelSelector
		case "deepseek":
			return !!config.deepSeekApiKey
		case "xai":
			return !!config.xaiApiKey
		case "groq":
			return !!config.groqApiKey
		case "chutes":
			return !!config.chutesApiKey
		case "vertex":
			return !!config.vertexProjectId && (!!config.vertexKeyFile || !!config.vertexJsonCredentials)
		case "ollama":
			return !!config.ollamaModelId
		case "lmstudio":
			return !!config.lmStudioModelId
		case "human-relay":
		case "fake-ai":
			return true // These don't require API keys
		default:
			return false
	}
}
