import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Security | Cubent',
  description: 'Security practices and policies for Cubent AI coding assistant',
};

export default function SecurityPolicy() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-16 max-w-4xl">
        <h1 className="text-4xl font-bold mb-8">Security</h1>
        <p className="text-gray-400 mb-8">Last updated: January 2, 2025</p>
        
        <div className="prose prose-invert max-w-none">
          <p className="text-lg mb-6">
            Keeping your source code and developer environment secure is our top priority. 
            This page outlines how we approach security for Cubent.
          </p>

          <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 mb-8">
            <p className="text-yellow-200">
              <strong>Security Contact:</strong> For security-related questions or to report vulnerabilities, 
              please contact us at <a href="mailto:<EMAIL>" className="text-yellow-400 hover:text-yellow-300"><EMAIL></a>
            </p>
          </div>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Infrastructure Security</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Cloud Infrastructure</h3>
          <p className="mb-4">
            Our infrastructure is built on industry-leading cloud platforms with robust security measures:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li><strong>Primary Hosting:</strong> AWS with servers primarily in the US</li>
            <li><strong>Content Delivery:</strong> Cloudflare for enhanced performance and security</li>
            <li><strong>Database Security:</strong> Encrypted databases with access controls</li>
            <li><strong>Network Security:</strong> VPC isolation and network-level controls</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">AI Model Providers</h3>
          <p className="mb-4">
            We partner with leading AI providers to deliver our services. All providers have 
            zero data retention agreements to ensure your code is not stored or used for training:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>OpenAI - Zero data retention agreement</li>
            <li>Anthropic - Zero data retention agreement</li>
            <li>Google Cloud Vertex API - Zero data retention agreement</li>
            <li>Custom models hosted on secure infrastructure</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">Access Controls</h3>
          <p className="mb-4">
            We implement strict access controls across our infrastructure:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Least-privilege access for all team members</li>
            <li>Multi-factor authentication required</li>
            <li>Regular access reviews and audits</li>
            <li>Secure secrets management</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Client Security</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Application Security</h3>
          <p className="mb-4">
            Cubent is built with security best practices in mind:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Regular security updates and patches</li>
            <li>Secure communication protocols (HTTPS/TLS)</li>
            <li>Input validation and sanitization</li>
            <li>Protection against common web vulnerabilities</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">API Endpoints</h3>
          <p className="mb-4">
            Our application communicates with the following secure domains:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li><code>api.cubent.dev</code> - Main API requests</li>
            <li><code>cdn.cubent.dev</code> - Static content delivery</li>
            <li><code>auth.cubent.dev</code> - Authentication services</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">AI Requests and Data Handling</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Request Processing</h3>
          <p className="mb-4">
            To provide AI-powered coding assistance, Cubent processes various types of requests:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Chat conversations and code questions</li>
            <li>Code completion and suggestions</li>
            <li>Code analysis and optimization recommendations</li>
            <li>Documentation and explanation requests</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">Data Flow Security</h3>
          <p className="mb-4">
            All AI requests follow a secure data flow:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Encrypted transmission from client to our servers</li>
            <li>Secure processing on our AWS infrastructure</li>
            <li>Encrypted communication with AI model providers</li>
            <li>Immediate deletion of temporary processing data</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Privacy Mode Guarantee</h2>
          
          <div className="bg-green-900/20 border border-green-600/30 rounded-lg p-4 mb-6">
            <h3 className="text-xl font-medium mb-3 text-green-200">Enhanced Privacy Protection</h3>
            <p className="text-green-100 mb-4">
              Privacy Mode provides the highest level of data protection for your code and conversations.
            </p>
          </div>

          <h3 className="text-xl font-medium mt-6 mb-3">Privacy Mode Features</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Code data is never stored by AI model providers</li>
            <li>No data used for model training purposes</li>
            <li>Temporary processing only for immediate responses</li>
            <li>Separate infrastructure for privacy mode users</li>
            <li>Enhanced logging restrictions and data isolation</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">Implementation</h3>
          <p className="mb-4">
            Our privacy mode implementation includes:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Parallel infrastructure for privacy and non-privacy requests</li>
            <li>Header-based request routing with redundant checks</li>
            <li>Default privacy mode assumption for safety</li>
            <li>Team-level privacy mode enforcement</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Code Indexing Security</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Secure Indexing Process</h3>
          <p className="mb-4">
            When codebase indexing is enabled, we implement several security measures:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Respect for .gitignore and .cubentignore files</li>
            <li>File path obfuscation using client-side encryption</li>
            <li>Merkle tree-based change detection</li>
            <li>Encrypted storage of embeddings and metadata</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">Privacy Mode Indexing</h3>
          <p className="mb-4">
            For privacy mode users, additional protections apply:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>No plaintext code stored on servers</li>
            <li>Local processing of search results</li>
            <li>Encrypted embeddings only</li>
            <li>Automatic data deletion upon account termination</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Compliance and Certifications</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Security Standards</h3>
          <p className="mb-4">
            We are committed to maintaining high security standards and are working towards:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>SOC 2 Type II certification (in progress)</li>
            <li>Regular third-party security assessments</li>
            <li>Penetration testing by reputable security firms</li>
            <li>Compliance with industry best practices</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Account Security</h2>
          
          <h3 className="text-xl font-medium mt-6 mb-3">Account Protection</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Strong password requirements</li>
            <li>Multi-factor authentication support</li>
            <li>Session management and timeout controls</li>
            <li>Account activity monitoring</li>
          </ul>

          <h3 className="text-xl font-medium mt-6 mb-3">Data Deletion</h3>
          <p className="mb-4">
            You can delete your account and all associated data at any time. We guarantee 
            complete data removal within 30 days of deletion request.
          </p>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Vulnerability Disclosure</h2>
          
          <p className="mb-4">
            We take security vulnerabilities seriously and encourage responsible disclosure:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Report vulnerabilities to <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300"><EMAIL></a></li>
            <li>We commit to acknowledging reports within 5 business days</li>
            <li>Critical issues will be addressed immediately</li>
            <li>Security advisories published for resolved issues</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8 mb-4">Contact Information</h2>
          
          <p className="mb-4">
            For security-related questions or concerns, please contact us:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Security Team: <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300"><EMAIL></a></li>
            <li>General Inquiries: <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300"><EMAIL></a></li>
          </ul>
        </div>
      </div>
    </div>
  );
}
