'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { UserButton } from '@repo/auth/components/user-button';
import Link from 'next/link';
import Image from 'next/image';

export const AppHeader = () => {
  return (
    <header className="sticky top-0 left-0 z-50 w-full bg-background/95 backdrop-blur-sm border-b border-border/40 supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-14 items-center justify-between px-4">
        {/* Left side - Logo */}
        <Link href="/dashboard" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
          <div className="w-7 h-7 bg-gradient-to-br from-orange-500 to-orange-600 rounded-md flex items-center justify-center">
            <span className="text-white font-bold text-sm">C</span>
          </div>
          <span className="font-semibold text-sm">Cubent</span>
        </Link>

        {/* Right side - User menu */}
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" asChild>
            <Link href="https://cubent.dev" target="_blank" rel="noopener noreferrer">
              Website
            </Link>
          </Button>
          <UserButton />
        </div>
      </div>
    </header>
  );
};
