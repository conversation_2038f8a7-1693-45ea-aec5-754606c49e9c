{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/image.tsx"], "sourcesContent": ["export { BaseHubImage as Image } from '../.basehub/next-image';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/json-ld.tsx"], "sourcesContent": ["import type { Thing, WithContext } from 'schema-dts';\n\ntype JsonLdProps = {\n  code: WithContext<Thing>;\n};\n\nexport const JsonLd = ({ code }: JsonLdProps) => (\n  <script\n    type=\"application/ld+json\"\n    // biome-ignore lint/security/noDangerouslySetInnerHtml: \"This is a JSON-LD script, not user-generated content.\"\n    dangerouslySetInnerHTML={{ __html: JSON.stringify(code) }}\n  />\n);\n\nexport * from 'schema-dts';\n"], "names": [], "mappings": ";;;;AAcA;;AARO,MAAM,SAAS,CAAC,EAAE,IAAI,EAAe,iBAC1C,6VAAC;QACC,MAAK;QACL,gHAAgH;QAChH,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAM", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'next-forge';\nconst author: Metadata['authors'] = {\n  name: 'Vercel',\n  url: 'https://vercel.com/',\n};\nconst publisher = 'Vercel';\nconst twitterHandle = '@vercel';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/blog/page.tsx"], "sourcesContent": ["import { blog } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Image } from '@repo/cms/components/image';\nimport { cn } from '@repo/design-system/lib/utils';\nimport { getDictionary } from '@repo/internationalization';\nimport type { Blog, WithContext } from '@repo/seo/json-ld';\nimport { JsonLd } from '@repo/seo/json-ld';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport Link from 'next/link';\n\ntype BlogProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: BlogProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return createMetadata(dictionary.web.blog.meta);\n};\n\nconst BlogIndex = async ({ params }: BlogProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  const jsonLd: WithContext<Blog> = {\n    '@type': 'Blog',\n    '@context': 'https://schema.org',\n  };\n\n  return (\n    <>\n      <JsonLd code={jsonLd} />\n      <div className=\"w-full pt-40 pb-20 lg:pt-48 lg:pb-40\">\n        <div className=\"container mx-auto flex flex-col gap-14\">\n          <div className=\"flex w-full flex-col gap-8 sm:flex-row sm:items-center sm:justify-between\">\n            <h4 className=\"max-w-xl font-regular text-3xl tracking-tighter md:text-5xl\">\n              {dictionary.web.blog.meta.title}\n            </h4>\n          </div>\n          <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2\">\n            <Feed queries={[blog.postsQuery]}>\n              {async ([data]) => {\n                'use server';\n\n                if (!data.blog.posts.items.length) {\n                  return null;\n                }\n\n                return data.blog.posts.items.map((post, index) => (\n                  <Link\n                    href={`/blog/${post._slug}`}\n                    className={cn(\n                      'flex cursor-pointer flex-col gap-4 hover:opacity-75',\n                      !index && 'md:col-span-2'\n                    )}\n                    key={post._slug}\n                  >\n                    <Image\n                      src={post.image.url}\n                      alt={post.image.alt ?? ''}\n                      width={post.image.width}\n                      height={post.image.height}\n                    />\n                    <div className=\"flex flex-row items-center gap-4\">\n                      <p className=\"text-muted-foreground text-sm\">\n                        {new Date(post.date).toLocaleDateString('en-US', {\n                          month: 'long',\n                          day: 'numeric',\n                          year: 'numeric',\n                        })}\n                      </p>\n                    </div>\n                    <div className=\"flex flex-col gap-2\">\n                      <h3 className=\"max-w-3xl text-4xl tracking-tight\">\n                        {post._title}\n                      </h3>\n                      <p className=\"max-w-3xl text-base text-muted-foreground\">\n                        {post.description}\n                      </p>\n                    </div>\n                  </Link>\n                ));\n              }}\n            </Feed>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default BlogIndex;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AAEA;;;;;;;;;;;;AAQO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;AAChD;MAuBe,gDAAO,CAAC,KAAK;IAGZ,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;QACjC,OAAO;IACT;IAEA,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,6VAAC,2QAAA,CAAA,UAAI;YACH,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;YAC3B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,uDACA,CAAC,SAAS;;8BAIZ,6VAAC,kSAAA,CAAA,QAAK;oBACJ,KAAK,KAAK,KAAK,CAAC,GAAG;oBACnB,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI;oBACvB,OAAO,KAAK,KAAK,CAAC,KAAK;oBACvB,QAAQ,KAAK,KAAK,CAAC,MAAM;;;;;;8BAE3B,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAE,WAAU;kCACV,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;4BAC/C,OAAO;4BACP,KAAK;4BACL,MAAM;wBACR;;;;;;;;;;;8BAGJ,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAG,WAAU;sCACX,KAAK,MAAM;;;;;;sCAEd,6VAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;;;;;;;;WAtBhB,KAAK,KAAK;;;;;AA2BrB;AA9Dd,MAAM,YAAY,OAAO,EAAE,MAAM,EAAa;IAC5C,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,MAAM,SAA4B;QAChC,SAAS;QACT,YAAY;IACd;IAEA,qBACE;;0BACE,6VAAC,8IAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;0BACd,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;;;;;;;;;;;sCAGnC,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,UAAU;iCAAC;0CAC7B,8VAAA;;;;;;;;;;;;;;;;;;;;;;;;AAgDf;uCAEe", "debugId": null}}]}